import { PrismaService } from '../prisma/prisma.service';
import { ApixGateway } from '../websocket/apix.gateway';
import { SystemUserRole } from '@prisma/client';
export interface CreateUserDto {
    email: string;
    name: string;
    password?: string;
    systemRole?: SystemUserRole;
    organizationId: string;
    roleIds?: string[];
    permissionIds?: string[];
}
export interface UpdateUserDto {
    name?: string;
    email?: string;
    systemRole?: SystemUserRole;
    isActive?: boolean;
    roleIds?: string[];
    permissionIds?: string[];
}
export interface UserFilters {
    search?: string;
    role?: SystemUserRole;
    organizationId?: string;
    isActive?: boolean;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}
export declare class UserService {
    private prisma;
    private apixGateway;
    constructor(prisma: PrismaService, apixGateway: ApixGateway);
    createUser(createUserDto: CreateUserDto, createdById: string): Promise<any>;
    updateUser(userId: string, updateUserDto: UpdateUserDto, updatedById: string): Promise<any>;
    deleteUser(userId: string, deletedById: string): Promise<{
        message: string;
    }>;
    getUsers(filters: UserFilters): Promise<{
        users: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            pages: number;
        };
    }>;
    getUserById(userId: string): Promise<any>;
    bulkUpdateUsers(userIds: string[], updateData: Partial<UpdateUserDto>, updatedById: string): Promise<{
        message: string;
    }>;
    resetPassword(userId: string, newPassword: string, resetById: string): Promise<{
        message: string;
    }>;
    private sanitizeUser;
}
