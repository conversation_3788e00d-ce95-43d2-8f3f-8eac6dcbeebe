import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Agent, AgentTemplate, AgentCollaboration } from '../database/entities/agent.entity';
import { AgentExecution } from '../database/entities/agent-execution.entity';
import { AgentsController } from './agents.controller';
import { AgentOrchestratorService } from './agent-orchestrator.service';
import { AgentAnalyticsService } from './agent-analytics.service';
import { AgentTemplatesService } from './agent-templates.service';
import { AgentTemplatesController } from './agent-templates.controller';
import { PromptTemplatesService } from './prompt-templates.service';
import { PromptTemplatesController } from './prompt-templates.controller';
import { SessionMemoryService } from './session-memory.service';
import { AgentCollaborationService } from './agent-collaboration.service';
import { ApixModule } from '../websocket/apix.module';
import { AIProviderModule } from '../providers/ai-provider.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Agent,
      AgentTemplate,
      AgentCollaboration,
      AgentExecution,
    ]),
    ApixModule,
    AIProviderModule,
  ],
  controllers: [
    AgentsController,
    AgentTemplatesController,
    PromptTemplatesController,
  ],
  providers: [
    AgentOrchestratorService,
    AgentAnalyticsService,
    AgentTemplatesService,
    PromptTemplatesService,
    SessionMemoryService,
    AgentCollaborationService,
  ],
  exports: [
    AgentOrchestratorService,
    AgentAnalyticsService,
    SessionMemoryService,
    AgentCollaborationService,
  ],
})
export class AgentsModule {}