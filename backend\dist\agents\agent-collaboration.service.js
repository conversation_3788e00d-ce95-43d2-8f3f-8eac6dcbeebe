"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AgentCollaborationService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentCollaborationService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const agent_entity_1 = require("../database/entities/agent.entity");
const agent_execution_entity_1 = require("../database/entities/agent-execution.entity");
const session_memory_service_1 = require("./session-memory.service");
const apix_gateway_1 = require("../websocket/apix.gateway");
const ai_provider_integration_service_1 = require("../providers/ai-provider-integration.service");
let AgentCollaborationService = AgentCollaborationService_1 = class AgentCollaborationService {
    constructor(collaborationRepository, agentRepository, executionRepository, sessionMemoryService, apixGateway, aiProviderIntegration) {
        this.collaborationRepository = collaborationRepository;
        this.agentRepository = agentRepository;
        this.executionRepository = executionRepository;
        this.sessionMemoryService = sessionMemoryService;
        this.apixGateway = apixGateway;
        this.aiProviderIntegration = aiProviderIntegration;
        this.logger = new common_1.Logger(AgentCollaborationService_1.name);
    }
    async createCollaboration(name, workflow, organizationId, userId) {
        var _a;
        try {
            await this.validateWorkflow(workflow, organizationId);
            const collaboration = this.collaborationRepository.create({
                name,
                agentIds: this.extractAgentIds(workflow),
                coordinatorId: (_a = workflow.steps[0]) === null || _a === void 0 ? void 0 : _a.agentId,
                workflow: workflow,
                organizationId,
                createdBy: userId,
                status: 'ACTIVE',
                sharedContext: {},
            });
            const savedCollaboration = await this.collaborationRepository.save(collaboration);
            this.apixGateway.emitToOrganization(organizationId, 'agent_collaboration_created', {
                collaborationId: savedCollaboration.id,
                name: savedCollaboration.name,
                agentIds: savedCollaboration.agentIds,
                timestamp: new Date(),
            });
            this.logger.log(`Agent collaboration created: ${savedCollaboration.id}`);
            return savedCollaboration;
        }
        catch (error) {
            this.logger.error(`Failed to create collaboration: ${error.message}`, error.stack);
            throw error;
        }
    }
    async executeCollaboration(collaborationId, input, organizationId, options) {
        try {
            const collaboration = await this.collaborationRepository.findOne({
                where: { id: collaborationId, organizationId },
            });
            if (!collaboration) {
                throw new Error('Collaboration not found');
            }
            if (collaboration.status !== 'ACTIVE') {
                throw new Error('Collaboration is not active');
            }
            const workflow = collaboration.workflow;
            let sessionId = options === null || options === void 0 ? void 0 : options.sessionId;
            if (!sessionId) {
                const session = await this.sessionMemoryService.createSession(collaboration.coordinatorId, organizationId, { enableLongTerm: true, maxTokens: 8000 });
                sessionId = session.id;
            }
            const executionContext = {
                input,
                sharedContext: Object.assign(Object.assign({}, collaboration.sharedContext), options === null || options === void 0 ? void 0 : options.context),
                results: [],
                sessionId,
                organizationId,
            };
            this.apixGateway.emitToOrganization(organizationId, 'agent_collaboration_started', {
                collaborationId,
                sessionId,
                input,
                timestamp: new Date(),
            });
            const results = await this.executeWorkflow(workflow, executionContext);
            collaboration.sharedContext = executionContext.sharedContext;
            await this.collaborationRepository.save(collaboration);
            const finalOutput = this.determineFinalOutput(results, workflow);
            this.apixGateway.emitToOrganization(organizationId, 'agent_collaboration_completed', {
                collaborationId,
                sessionId,
                finalOutput,
                resultsCount: results.length,
                timestamp: new Date(),
            });
            return {
                collaborationId,
                sessionId,
                results,
                finalOutput,
                sharedContext: executionContext.sharedContext,
            };
        }
        catch (error) {
            this.logger.error(`Collaboration execution failed: ${error.message}`, error.stack);
            this.apixGateway.emitToOrganization(organizationId, 'agent_collaboration_failed', {
                collaborationId,
                error: error.message,
                timestamp: new Date(),
            });
            throw error;
        }
    }
    async executeWorkflow(workflow, context) {
        var _a;
        const results = [];
        let currentIteration = 0;
        const maxIterations = workflow.settings.maxIterations || 10;
        while (currentIteration < maxIterations) {
            let stepExecuted = false;
            for (const step of workflow.steps) {
                if (await this.shouldSkipStep(step, context)) {
                    continue;
                }
                try {
                    const stepResult = await this.executeStep(step, context, workflow);
                    results.push(stepResult);
                    stepExecuted = true;
                    this.updateContextWithResult(context, step, stepResult);
                    this.apixGateway.emitToOrganization(context.organizationId, 'collaboration_step_completed', {
                        stepId: step.id,
                        agentId: step.agentId,
                        output: stepResult.output,
                        timestamp: new Date(),
                    });
                }
                catch (error) {
                    this.logger.error(`Step ${step.id} failed: ${error.message}`);
                    if (workflow.settings.failureStrategy === 'stop') {
                        throw error;
                    }
                    else if (workflow.settings.failureStrategy === 'retry' &&
                        (((_a = step.conditions) === null || _a === void 0 ? void 0 : _a.maxRetries) || 0) > 0) {
                        continue;
                    }
                }
            }
            if (!stepExecuted || await this.isWorkflowComplete(workflow, context)) {
                break;
            }
            currentIteration++;
        }
        return results;
    }
    async executeStep(step, context, workflow) {
        var _a, _b;
        const agent = await this.agentRepository.findOne({
            where: { id: step.agentId, organizationId: context.organizationId },
            relations: ['template'],
        });
        if (!agent) {
            throw new Error(`Agent ${step.agentId} not found`);
        }
        const agentInput = await this.prepareStepInput(step, context);
        const messages = [];
        messages.push({
            role: 'system',
            content: this.buildStepSystemPrompt(step, agent, workflow, context),
        });
        if (context.sessionId) {
            const history = await this.sessionMemoryService.getSessionHistory(context.sessionId, context.organizationId, 5);
            messages.push(...history.map(msg => ({
                role: msg.role,
                content: msg.content,
            })));
        }
        messages.push({
            role: 'user',
            content: agentInput,
        });
        const aiRequest = {
            requestId: `collab_${step.id}_${Date.now()}`,
            messages,
            temperature: ((_a = agent.config) === null || _a === void 0 ? void 0 : _a.temperature) || 0.7,
            maxTokens: ((_b = agent.config) === null || _b === void 0 ? void 0 : _b.maxTokens) || 1500,
            organizationId: context.organizationId,
        };
        const aiResponse = await this.aiProviderIntegration.processRequest(aiRequest);
        const execution = this.executionRepository.create({
            agentId: step.agentId,
            sessionId: context.sessionId,
            input: agentInput,
            output: aiResponse.content,
            status: 'COMPLETED',
            context: {
                collaborationStep: step.id,
                stepType: step.type,
                sharedContext: context.sharedContext,
            },
            metadata: {
                provider: aiResponse.providerId,
                model: aiResponse.modelId,
                tokens: aiResponse.usage,
                cost: aiResponse.cost,
                latency: aiResponse.latency,
            },
            organizationId: context.organizationId,
            completedAt: new Date(),
        });
        const savedExecution = await this.executionRepository.save(execution);
        if (context.sessionId) {
            await this.sessionMemoryService.addMessage(context.sessionId, {
                role: 'user',
                content: agentInput,
                timestamp: new Date(),
                metadata: { stepId: step.id, stepType: step.type },
            });
            await this.sessionMemoryService.addMessage(context.sessionId, {
                role: 'assistant',
                content: aiResponse.content,
                timestamp: new Date(),
                metadata: Object.assign({ stepId: step.id, stepType: step.type, agentId: step.agentId }, savedExecution.metadata),
            });
        }
        return {
            stepId: step.id,
            agentId: step.agentId,
            output: aiResponse.content,
            executionId: savedExecution.id,
            metadata: savedExecution.metadata,
        };
    }
    buildStepSystemPrompt(step, agent, workflow, context) {
        var _a;
        let prompt = ((_a = agent.template) === null || _a === void 0 ? void 0 : _a.promptTemplate) || 'You are a helpful AI assistant.';
        switch (step.type) {
            case 'execute':
                prompt += '\n\nYou are executing a specific task as part of a collaborative workflow. Focus on completing your assigned task effectively.';
                break;
            case 'review':
                prompt += '\n\nYou are reviewing the work of other agents in this collaboration. Provide constructive feedback and identify areas for improvement.';
                break;
            case 'synthesize':
                prompt += '\n\nYou are synthesizing information from multiple agents. Combine their outputs into a coherent and comprehensive response.';
                break;
            case 'validate':
                prompt += '\n\nYou are validating the results of this collaboration. Check for accuracy, completeness, and quality.';
                break;
        }
        prompt += `\n\nCollaboration Context:
- Workflow: ${workflow.name}
- Step: ${step.id} (${step.type})
- Previous results available in shared context
- Your role: ${agent.name}`;
        if (Object.keys(context.sharedContext).length > 0) {
            prompt += '\n\nShared Context:\n';
            Object.entries(context.sharedContext).forEach(([key, value]) => {
                if (typeof value === 'string' && value.length < 500) {
                    prompt += `- ${key}: ${value}\n`;
                }
                else {
                    prompt += `- ${key}: [Complex data available]\n`;
                }
            });
        }
        return prompt;
    }
    async prepareStepInput(step, context) {
        switch (step.input.source) {
            case 'user':
                return context.input;
            case 'previous':
                const lastResult = context.results[context.results.length - 1];
                return (lastResult === null || lastResult === void 0 ? void 0 : lastResult.output) || context.input;
            case 'context':
                return JSON.stringify(context.sharedContext, null, 2);
            case 'shared':
                const key = step.input.transformation;
                return context.sharedContext[key] || context.input;
            default:
                return context.input;
        }
    }
    updateContextWithResult(context, step, result) {
        switch (step.output.target) {
            case 'shared':
                const key = step.output.key || `step_${step.id}_output`;
                context.sharedContext[key] = result.output;
                context.sharedContext[`${key}_metadata`] = result.metadata;
                break;
            case 'final':
                context.finalOutput = result.output;
                break;
        }
        context.results.push(result);
    }
    async shouldSkipStep(step, context) {
        var _a;
        if (!((_a = step.conditions) === null || _a === void 0 ? void 0 : _a.skipIf)) {
            return false;
        }
        try {
            const condition = step.conditions.skipIf;
            return this.evaluateCondition(condition, context);
        }
        catch (error) {
            this.logger.warn(`Failed to evaluate skip condition for step ${step.id}: ${error.message}`);
            return false;
        }
    }
    async isWorkflowComplete(workflow, context) {
        const executedSteps = new Set(context.results.map(r => r.stepId));
        const requiredSteps = workflow.steps.filter(s => { var _a; return !((_a = s.conditions) === null || _a === void 0 ? void 0 : _a.skipIf); });
        return requiredSteps.every(step => executedSteps.has(step.id));
    }
    evaluateCondition(condition, context) {
        try {
            let evaluableCondition = condition;
            Object.entries(context.sharedContext).forEach(([key, value]) => {
                evaluableCondition = evaluableCondition.replace(new RegExp(`\\$\\{${key}\\}`, 'g'), JSON.stringify(value));
            });
            if (evaluableCondition.includes('==') || evaluableCondition.includes('!=') ||
                evaluableCondition.includes('>') || evaluableCondition.includes('<')) {
                return eval(evaluableCondition);
            }
            return false;
        }
        catch (error) {
            this.logger.warn(`Condition evaluation failed: ${error.message}`);
            return false;
        }
    }
    determineFinalOutput(results, workflow) {
        var _a;
        const finalResult = results.find(r => { var _a; return ((_a = workflow.steps.find(s => s.id === r.stepId)) === null || _a === void 0 ? void 0 : _a.output.target) === 'final'; });
        if (finalResult) {
            return finalResult.output;
        }
        return ((_a = results[results.length - 1]) === null || _a === void 0 ? void 0 : _a.output) || 'No output generated';
    }
    async validateWorkflow(workflow, organizationId) {
        const agentIds = this.extractAgentIds(workflow);
        const agents = await this.agentRepository.find({
            where: { id: agentIds, organizationId },
        });
        if (agents.length !== agentIds.length) {
            throw new Error('One or more agents in the workflow do not exist');
        }
        if (!workflow.steps || workflow.steps.length === 0) {
            throw new Error('Workflow must have at least one step');
        }
        const stepIds = new Set(workflow.steps.map(s => s.id));
        workflow.steps.forEach(step => {
            if (!agentIds.includes(step.agentId)) {
                throw new Error(`Step ${step.id} references non-existent agent ${step.agentId}`);
            }
        });
    }
    extractAgentIds(workflow) {
        return [...new Set(workflow.steps.map(step => step.agentId))];
    }
    async getCollaborationsByOrganization(organizationId) {
        return this.collaborationRepository.find({
            where: { organizationId },
            order: { createdAt: 'DESC' },
        });
    }
    async getCollaborationById(collaborationId, organizationId) {
        const collaboration = await this.collaborationRepository.findOne({
            where: { id: collaborationId, organizationId },
        });
        if (!collaboration) {
            throw new Error('Collaboration not found');
        }
        return collaboration;
    }
    async updateCollaborationStatus(collaborationId, status, organizationId) {
        const collaboration = await this.getCollaborationById(collaborationId, organizationId);
        collaboration.status = status;
        return this.collaborationRepository.save(collaboration);
    }
};
exports.AgentCollaborationService = AgentCollaborationService;
exports.AgentCollaborationService = AgentCollaborationService = AgentCollaborationService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(agent_entity_1.AgentCollaboration)),
    __param(1, (0, typeorm_1.InjectRepository)(agent_entity_1.Agent)),
    __param(2, (0, typeorm_1.InjectRepository)(agent_execution_entity_1.AgentExecution)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        session_memory_service_1.SessionMemoryService,
        apix_gateway_1.ApixGateway,
        ai_provider_integration_service_1.AIProviderIntegrationService])
], AgentCollaborationService);
//# sourceMappingURL=agent-collaboration.service.js.map