"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AgentOrchestratorService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentOrchestratorService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const agent_entity_1 = require("../database/entities/agent.entity");
const agent_execution_entity_1 = require("../database/entities/agent-execution.entity");
const session_memory_service_1 = require("./session-memory.service");
const agent_analytics_service_1 = require("./agent-analytics.service");
const apix_gateway_1 = require("../websocket/apix.gateway");
const ai_provider_integration_service_1 = require("../providers/ai-provider-integration.service");
const ai_provider_selector_service_1 = require("../providers/ai-provider-selector.service");
let AgentOrchestratorService = AgentOrchestratorService_1 = class AgentOrchestratorService {
    constructor(agentRepository, executionRepository, collaborationRepository, sessionMemoryService, analyticsService, apixGateway, aiProviderIntegration, aiProviderSelector) {
        this.agentRepository = agentRepository;
        this.executionRepository = executionRepository;
        this.collaborationRepository = collaborationRepository;
        this.sessionMemoryService = sessionMemoryService;
        this.analyticsService = analyticsService;
        this.apixGateway = apixGateway;
        this.aiProviderIntegration = aiProviderIntegration;
        this.aiProviderSelector = aiProviderSelector;
        this.logger = new common_1.Logger(AgentOrchestratorService_1.name);
    }
    async createAgent(createAgentDto, organizationId, userId) {
        try {
            const agent = this.agentRepository.create(Object.assign(Object.assign({}, createAgentDto), { organizationId, createdBy: userId, performanceMetrics: {
                    totalExecutions: 0,
                    successRate: 0,
                    averageResponseTime: 0,
                    lastExecuted: new Date(),
                } }));
            const savedAgent = await this.agentRepository.save(agent);
            this.apixGateway.emitToOrganization(organizationId, 'agent_created', {
                agentId: savedAgent.id,
                name: savedAgent.name,
                type: savedAgent.type,
                timestamp: new Date(),
            });
            this.logger.log(`Agent created: ${savedAgent.id} for organization: ${organizationId}`);
            return savedAgent;
        }
        catch (error) {
            this.logger.error(`Failed to create agent: ${error.message}`, error.stack);
            throw error;
        }
    }
    async updateAgent(agentId, updateAgentDto, organizationId) {
        try {
            const agent = await this.agentRepository.findOne({
                where: { id: agentId, organizationId },
            });
            if (!agent) {
                throw new Error('Agent not found');
            }
            Object.assign(agent, updateAgentDto);
            const updatedAgent = await this.agentRepository.save(agent);
            this.apixGateway.emitToOrganization(organizationId, 'agent_updated', {
                agentId: updatedAgent.id,
                changes: updateAgentDto,
                timestamp: new Date(),
            });
            return updatedAgent;
        }
        catch (error) {
            this.logger.error(`Failed to update agent ${agentId}: ${error.message}`, error.stack);
            throw error;
        }
    }
    async deleteAgent(agentId, organizationId) {
        try {
            const result = await this.agentRepository.delete({
                id: agentId,
                organizationId,
            });
            if (result.affected === 0) {
                throw new Error('Agent not found');
            }
            this.apixGateway.emitToOrganization(organizationId, 'agent_deleted', {
                agentId,
                timestamp: new Date(),
            });
            this.logger.log(`Agent deleted: ${agentId}`);
        }
        catch (error) {
            this.logger.error(`Failed to delete agent ${agentId}: ${error.message}`, error.stack);
            throw error;
        }
    }
    async executeAgent(agentId, executeDto, organizationId) {
        const startTime = Date.now();
        let execution;
        try {
            const agent = await this.agentRepository.findOne({
                where: { id: agentId, organizationId },
                relations: ['template'],
            });
            if (!agent) {
                throw new Error('Agent not found');
            }
            if (agent.status !== agent_entity_1.AgentStatus.ACTIVE) {
                throw new Error('Agent is not active');
            }
            execution = this.executionRepository.create({
                agentId,
                sessionId: executeDto.sessionId,
                input: executeDto.message,
                status: agent_execution_entity_1.ExecutionStatus.RUNNING,
                context: executeDto.context || {},
                organizationId,
            });
            execution = await this.executionRepository.save(execution);
            this.apixGateway.emitToOrganization(organizationId, 'agent_execution_started', {
                executionId: execution.id,
                agentId,
                timestamp: new Date(),
            });
            let sessionMemory = null;
            if (executeDto.sessionId) {
                sessionMemory = await this.sessionMemoryService.getSession(executeDto.sessionId, organizationId);
            }
            else {
                sessionMemory = await this.sessionMemoryService.createSession(agentId, organizationId);
                execution.sessionId = sessionMemory.id;
                await this.executionRepository.save(execution);
            }
            const result = await this.executeAgentLogic(agent, executeDto.message, sessionMemory, executeDto.context);
            const duration = Date.now() - startTime;
            execution.output = result.output;
            execution.status = agent_execution_entity_1.ExecutionStatus.COMPLETED;
            execution.metadata = {
                provider: result.provider,
                model: result.model,
                tokens: result.tokens,
                cost: result.cost,
                duration,
                retryCount: result.retryCount || 0,
            };
            execution.completedAt = new Date();
            execution = await this.executionRepository.save(execution);
            await this.sessionMemoryService.addMessage(sessionMemory.id, {
                role: 'user',
                content: executeDto.message,
                timestamp: new Date(),
            });
            await this.sessionMemoryService.addMessage(sessionMemory.id, {
                role: 'assistant',
                content: result.output,
                timestamp: new Date(),
                metadata: execution.metadata,
            });
            await this.updateAgentMetrics(agentId, duration, true);
            this.apixGateway.emitToOrganization(organizationId, 'agent_execution_completed', {
                executionId: execution.id,
                agentId,
                output: result.output,
                duration,
                timestamp: new Date(),
            });
            await this.analyticsService.trackExecution(execution);
            return execution;
        }
        catch (error) {
            this.logger.error(`Agent execution failed: ${error.message}`, error.stack);
            if (execution) {
                execution.status = agent_execution_entity_1.ExecutionStatus.FAILED;
                execution.errorMessage = error.message;
                execution.errorDetails = { stack: error.stack };
                execution.completedAt = new Date();
                await this.executionRepository.save(execution);
                await this.updateAgentMetrics(agentId, Date.now() - startTime, false);
                this.apixGateway.emitToOrganization(organizationId, 'agent_execution_failed', {
                    executionId: execution.id,
                    agentId,
                    error: error.message,
                    timestamp: new Date(),
                });
            }
            throw error;
        }
    }
    async executeAgentLogic(agent, message, sessionMemory, context = {}) {
        var _a, _b, _c;
        try {
            const messages = [];
            if ((_a = agent.template) === null || _a === void 0 ? void 0 : _a.promptTemplate) {
                messages.push({
                    role: 'system',
                    content: this.buildSystemPrompt(agent.template.promptTemplate, agent, context),
                });
            }
            if (sessionMemory.messages && sessionMemory.messages.length > 0) {
                const recentMessages = sessionMemory.messages.slice(-10);
                messages.push(...recentMessages.map(msg => ({
                    role: msg.role,
                    content: msg.content,
                })));
            }
            messages.push({
                role: 'user',
                content: message,
            });
            const capabilities = this.getRequiredCapabilities(agent);
            const aiRequest = {
                requestId: `agent_${agent.id}_${Date.now()}`,
                providerId: agent.primaryProvider ? this.mapProviderType(agent.primaryProvider) : undefined,
                messages,
                temperature: ((_b = agent.config) === null || _b === void 0 ? void 0 : _b.temperature) || 0.7,
                maxTokens: ((_c = agent.config) === null || _c === void 0 ? void 0 : _c.maxTokens) || 1000,
                organizationId: agent.organizationId,
            };
            const aiResponse = await this.aiProviderIntegration.processRequest(aiRequest);
            return {
                output: aiResponse.content,
                provider: aiResponse.providerId,
                model: aiResponse.modelId,
                tokens: {
                    input: aiResponse.usage.promptTokens,
                    output: aiResponse.usage.completionTokens,
                    total: aiResponse.usage.totalTokens,
                },
                cost: aiResponse.cost,
            };
        }
        catch (error) {
            this.logger.error(`Agent logic execution failed: ${error.message}`, error.stack);
            throw error;
        }
    }
    buildSystemPrompt(template, agent, context) {
        let prompt = template;
        prompt = prompt.replace(/\{agent\.name\}/g, agent.name);
        prompt = prompt.replace(/\{agent\.type\}/g, agent.type);
        if (agent.skills && agent.skills.length > 0) {
            prompt = prompt.replace(/\{agent\.skills\}/g, agent.skills.join(', '));
        }
        Object.entries(context).forEach(([key, value]) => {
            const regex = new RegExp(`\\{context\\.${key}\\}`, 'g');
            prompt = prompt.replace(regex, String(value));
        });
        prompt = prompt.replace(/\{timestamp\}/g, new Date().toISOString());
        return prompt;
    }
    getRequiredCapabilities(agent) {
        const capabilities = ['chat'];
        switch (agent.type) {
            case agent_entity_1.AgentType.TOOL_DRIVEN:
                capabilities.push('function-calling');
                break;
            case agent_entity_1.AgentType.MULTI_TASK:
                capabilities.push('analysis', 'code-generation');
                break;
            case agent_entity_1.AgentType.COLLABORATIVE:
                capabilities.push('function-calling', 'analysis');
                break;
        }
        if (agent.skills) {
            if (agent.skills.includes('code_generation')) {
                capabilities.push('code-generation');
            }
            if (agent.skills.includes('image_analysis')) {
                capabilities.push('vision');
            }
            if (agent.skills.includes('function_calling')) {
                capabilities.push('function-calling');
            }
        }
        return [...new Set(capabilities)];
    }
    mapProviderType(providerType) {
        const mapping = {
            OPENAI: 'openai',
            CLAUDE: 'claude',
            GEMINI: 'gemini',
            MISTRAL: 'mistral',
            GROQ: 'groq',
        };
        return mapping[providerType] || providerType.toLowerCase();
    }
    async updateAgentMetrics(agentId, duration, success) {
        const agent = await this.agentRepository.findOne({ where: { id: agentId } });
        if (!agent)
            return;
        const metrics = agent.performanceMetrics || {
            totalExecutions: 0,
            successRate: 0,
            averageResponseTime: 0,
            lastExecuted: new Date(),
        };
        metrics.totalExecutions += 1;
        metrics.successRate = success
            ? (metrics.successRate * (metrics.totalExecutions - 1) + 1) / metrics.totalExecutions
            : (metrics.successRate * (metrics.totalExecutions - 1)) / metrics.totalExecutions;
        metrics.averageResponseTime =
            (metrics.averageResponseTime * (metrics.totalExecutions - 1) + duration) / metrics.totalExecutions;
        metrics.lastExecuted = new Date();
        agent.performanceMetrics = metrics;
        await this.agentRepository.save(agent);
    }
    async createCollaboration(name, agentIds, coordinatorId, workflow, organizationId, userId) {
        try {
            const agents = await this.agentRepository.find({
                where: { id: agentIds, organizationId },
            });
            if (agents.length !== agentIds.length) {
                throw new Error('One or more agents not found');
            }
            const collaboration = this.collaborationRepository.create({
                name,
                agentIds,
                coordinatorId,
                workflow,
                organizationId,
                createdBy: userId,
                status: 'ACTIVE',
                sharedContext: {},
            });
            const savedCollaboration = await this.collaborationRepository.save(collaboration);
            this.apixGateway.emitToOrganization(organizationId, 'agent_collaboration_created', {
                collaborationId: savedCollaboration.id,
                name: savedCollaboration.name,
                agentIds,
                timestamp: new Date(),
            });
            this.logger.log(`Agent collaboration created: ${savedCollaboration.id}`);
            return savedCollaboration;
        }
        catch (error) {
            this.logger.error(`Failed to create agent collaboration: ${error.message}`, error.stack);
            throw error;
        }
    }
    async executeCollaboration(collaborationId, input, organizationId) {
        var _a;
        try {
            const collaboration = await this.collaborationRepository.findOne({
                where: { id: collaborationId, organizationId },
            });
            if (!collaboration) {
                throw new Error('Collaboration not found');
            }
            if (collaboration.status !== 'ACTIVE') {
                throw new Error('Collaboration is not active');
            }
            const coordinator = await this.agentRepository.findOne({
                where: { id: collaboration.coordinatorId, organizationId },
            });
            if (!coordinator) {
                throw new Error('Coordinator agent not found');
            }
            const results = await this.executeCollaborationWorkflow(collaboration, input, organizationId);
            this.apixGateway.emitToOrganization(organizationId, 'agent_collaboration_completed', {
                collaborationId,
                results: results.map(r => ({ agentId: r.agentId, executionId: r.executionId })),
                timestamp: new Date(),
            });
            return {
                collaborationId,
                results,
                finalOutput: ((_a = results[results.length - 1]) === null || _a === void 0 ? void 0 : _a.output) || 'No output generated',
            };
        }
        catch (error) {
            this.logger.error(`Collaboration execution failed: ${error.message}`, error.stack);
            throw error;
        }
    }
    async executeCollaborationWorkflow(collaboration, input, organizationId) {
        const results = [];
        let currentInput = input;
        let sharedContext = collaboration.sharedContext || {};
        for (const agentId of collaboration.agentIds) {
            try {
                const execution = await this.executeAgent(agentId, {
                    message: currentInput,
                    context: Object.assign(Object.assign({}, sharedContext), { collaborationId: collaboration.id, previousResults: results }),
                }, organizationId);
                results.push({
                    agentId,
                    output: execution.output,
                    executionId: execution.id,
                });
                sharedContext = Object.assign(Object.assign({}, sharedContext), { [`agent_${agentId}_output`]: execution.output, [`agent_${agentId}_metadata`]: execution.metadata });
                currentInput = execution.output;
            }
            catch (error) {
                this.logger.error(`Agent ${agentId} failed in collaboration: ${error.message}`);
                results.push({
                    agentId,
                    output: `Error: ${error.message}`,
                    executionId: 'failed',
                });
            }
        }
        collaboration.sharedContext = sharedContext;
        await this.collaborationRepository.save(collaboration);
        return results;
    }
    async getAgentsByOrganization(organizationId) {
        return this.agentRepository.find({
            where: { organizationId },
            relations: ['template', 'executions'],
            order: { createdAt: 'DESC' },
        });
    }
    async getAgentById(agentId, organizationId) {
        const agent = await this.agentRepository.findOne({
            where: { id: agentId, organizationId },
            relations: ['template', 'executions'],
        });
        if (!agent) {
            throw new Error('Agent not found');
        }
        return agent;
    }
    async getAgentExecutions(agentId, organizationId) {
        return this.executionRepository.find({
            where: { agentId, organizationId },
            order: { createdAt: 'DESC' },
            take: 100,
        });
    }
    async getCollaborationsByOrganization(organizationId) {
        return this.collaborationRepository.find({
            where: { organizationId },
            order: { createdAt: 'DESC' },
        });
    }
};
exports.AgentOrchestratorService = AgentOrchestratorService;
exports.AgentOrchestratorService = AgentOrchestratorService = AgentOrchestratorService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(agent_entity_1.Agent)),
    __param(1, (0, typeorm_1.InjectRepository)(agent_execution_entity_1.AgentExecution)),
    __param(2, (0, typeorm_1.InjectRepository)(agent_entity_1.AgentCollaboration)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        session_memory_service_1.SessionMemoryService,
        agent_analytics_service_1.AgentAnalyticsService,
        apix_gateway_1.ApixGateway,
        ai_provider_integration_service_1.AIProviderIntegrationService,
        ai_provider_selector_service_1.AIProviderSelectorService])
], AgentOrchestratorService);
//# sourceMappingURL=agent-orchestrator.service.js.map