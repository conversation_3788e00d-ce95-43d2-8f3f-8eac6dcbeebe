import { User } from './user.entity';
import { Agent } from './agent.entity';
import { Tool } from './tool.entity';
import { Session } from './session.entity';
export declare class Organization {
    id: string;
    name: string;
    slug: string;
    settings: Record<string, any>;
    quotas: {
        agents: number;
        tools: number;
        executions: number;
        storage: number;
    };
    billing: {
        plan: string;
        status: string;
        usage: Record<string, number>;
    };
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
    users: User[];
    agents: Agent[];
    tools: Tool[];
    sessions: Session[];
}
