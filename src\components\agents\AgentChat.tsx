'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Send, Bot, User, Loader2, Refresh<PERSON>w, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/components/ui/use-toast';
import { AgentInstance, SessionMessage, AgentExecution } from '@/lib/types/agent.types';
import { agentApi } from '@/lib/api/agent-api';

interface AgentChatProps {
  agent: AgentInstance;
  onClose: () => void;
}

export function AgentChat({ agent, onClose }: AgentChatProps) {
  const [messages, setMessages] = useState<SessionMessage[]>([]);
  const [input, setInput] = useState('');
  const [loading, setLoading] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [executionHistory, setExecutionHistory] = useState<AgentExecution[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  useEffect(() => {
    loadExecutionHistory();
  }, [agent.id]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const loadExecutionHistory = async () => {
    try {
      const response = await agentApi.getAgentExecutions(agent.id!);
      if (response.success) {
        setExecutionHistory(response.data || []);
      }
    } catch (error) {
      console.error('Failed to load execution history:', error);
    }
  };

  const handleSendMessage = async () => {
    if (!input.trim() || loading) return;

    const userMessage: SessionMessage = {
      role: 'user',
      content: input.trim(),
      timestamp: new Date().toISOString(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setLoading(true);

    try {
      const response = await agentApi.executeAgent(agent.id!, {
        message: userMessage.content,
        sessionId: sessionId || undefined,
        context: {},
        streamResponse: false,
      });

      if (response.success && response.data) {
        const execution = response.data;
        
        // Update session ID if this is the first message
        if (!sessionId && execution.sessionId) {
          setSessionId(execution.sessionId);
        }

        // Add assistant response
        const assistantMessage: SessionMessage = {
          role: 'assistant',
          content: execution.output || 'No response received',
          timestamp: new Date().toISOString(),
          metadata: execution.metadata,
        };

        setMessages(prev => [...prev, assistantMessage]);
        
        // Update execution history
        loadExecutionHistory();

        toast({
          title: 'Success',
          description: 'Message sent successfully',
        });
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Failed to send message',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to send message',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const clearChat = () => {
    setMessages([]);
    setSessionId(null);
    toast({
      title: 'Chat Cleared',
      description: 'Chat history has been cleared',
    });
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const getProviderBadgeColor = (provider: string) => {
    const colors = {
      OPENAI: 'bg-green-100 text-green-800',
      CLAUDE: 'bg-purple-100 text-purple-800',
      GEMINI: 'bg-blue-100 text-blue-800',
      MISTRAL: 'bg-orange-100 text-orange-800',
      GROQ: 'bg-red-100 text-red-800',
    };
    return colors[provider as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="flex flex-col h-[600px] bg-white">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Bot className="w-5 h-5 text-blue-600" />
          </div>
          <div>
            <h3 className="font-semibold">{agent.name}</h3>
            <p className="text-sm text-gray-600">
              {agent.primaryProvider} • {agent.type}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={clearChat}
            disabled={messages.length === 0}
          >
            <Trash2 className="w-4 h-4 mr-1" />
            Clear
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={loadExecutionHistory}
          >
            <RefreshCw className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Messages */}
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-4">
          {messages.length === 0 ? (
            <div className="text-center py-8">
              <Bot className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-600 mb-2">
                Start a conversation
              </h3>
              <p className="text-gray-500">
                Send a message to test your agent
              </p>
            </div>
          ) : (
            messages.map((message, index) => (
              <div
                key={index}
                className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[80%] rounded-lg p-3 ${
                    message.role === 'user'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-900'
                  }`}
                >
                  <div className="flex items-center space-x-2 mb-1">
                    {message.role === 'user' ? (
                      <User className="w-4 h-4" />
                    ) : (
                      <Bot className="w-4 h-4" />
                    )}
                    <span className="text-xs opacity-75">
                      {formatTimestamp(message.timestamp)}
                    </span>
                    {message.metadata?.provider && (
                      <Badge
                        className={`text-xs ${getProviderBadgeColor(message.metadata.provider)}`}
                      >
                        {message.metadata.provider}
                      </Badge>
                    )}
                  </div>
                  <p className="whitespace-pre-wrap">{message.content}</p>
                  {message.metadata && (
                    <div className="mt-2 text-xs opacity-75">
                      <div className="flex items-center space-x-4">
                        {message.metadata.tokens && (
                          <span>Tokens: {message.metadata.tokens.total}</span>
                        )}
                        {message.metadata.cost && (
                          <span>Cost: ${message.metadata.cost.toFixed(4)}</span>
                        )}
                        {message.metadata.duration && (
                          <span>Time: {message.metadata.duration}ms</span>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))
          )}
          {loading && (
            <div className="flex justify-start">
              <div className="bg-gray-100 rounded-lg p-3">
                <div className="flex items-center space-x-2">
                  <Bot className="w-4 h-4" />
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span className="text-sm text-gray-600">Thinking...</span>
                </div>
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>

      {/* Input */}
      <div className="p-4 border-t">
        <div className="flex space-x-2">
          <Input
            placeholder="Type your message..."
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyPress={handleKeyPress}
            disabled={loading}
            className="flex-1"
          />
          <Button
            onClick={handleSendMessage}
            disabled={!input.trim() || loading}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {loading ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Send className="w-4 h-4" />
            )}
          </Button>
        </div>
        
        {sessionId && (
          <div className="mt-2 text-xs text-gray-500">
            Session: {sessionId}
          </div>
        )}
      </div>

      {/* Recent Executions */}
      {executionHistory.length > 0 && (
        <div className="border-t p-4">
          <h4 className="text-sm font-semibold mb-2">Recent Executions</h4>
          <div className="space-y-1">
            {executionHistory.slice(0, 3).map((execution) => (
              <div
                key={execution.id}
                className="flex items-center justify-between text-xs text-gray-600"
              >
                <span className="truncate max-w-[200px]">
                  {execution.input}
                </span>
                <div className="flex items-center space-x-2">
                  <Badge
                    variant={execution.status === 'COMPLETED' ? 'default' : 'destructive'}
                    className="text-xs"
                  >
                    {execution.status}
                  </Badge>
                  <span>{formatTimestamp(execution.createdAt)}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}