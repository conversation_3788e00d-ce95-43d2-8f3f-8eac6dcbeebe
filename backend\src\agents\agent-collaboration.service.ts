import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AgentCollaboration, Agent } from '../database/entities/agent.entity';
import { AgentExecution } from '../database/entities/agent-execution.entity';
import { SessionMemoryService } from './session-memory.service';
import { ApixGateway } from '../websocket/apix.gateway';
import { AIProviderIntegrationService } from '../providers/ai-provider-integration.service';
import { LoggerService } from '@nestjs/common';

export interface CollaborationWorkflow {
  id: string;
  name: string;
  steps: CollaborationStep[];
  conditions: CollaborationCondition[];
  settings: {
    maxIterations: number;
    timeoutMs: number;
    failureStrategy: 'stop' | 'continue' | 'retry';
    parallelExecution: boolean;
  };
}

export interface CollaborationStep {
  id: string;
  agentId: string;
  type: 'execute' | 'review' | 'synthesize' | 'validate';
  input: {
    source: 'user' | 'previous' | 'context' | 'shared';
    transformation?: string;
  };
  output: {
    target: 'next' | 'shared' | 'final';
    key?: string;
  };
  conditions?: {
    skipIf?: string;
    retryIf?: string;
    maxRetries?: number;
  };
}

export interface CollaborationCondition {
  id: string;
  type: 'if' | 'while' | 'until';
  condition: string;
  actions: {
    true: string[];
    false: string[];
  };
}

@Injectable()
export class AgentCollaborationService {
  private readonly logger = new Logger(AgentCollaborationService.name);

  constructor(
    @InjectRepository(AgentCollaboration)
    private collaborationRepository: Repository<AgentCollaboration>,
    @InjectRepository(Agent)
    private agentRepository: Repository<Agent>,
    @InjectRepository(AgentExecution)
    private executionRepository: Repository<AgentExecution>,
    private sessionMemoryService: SessionMemoryService,
    private apixGateway: ApixGateway,
    private aiProviderIntegration: AIProviderIntegrationService,
  ) {}

  async createCollaboration(
    name: string,
    workflow: CollaborationWorkflow,
    organizationId: string,
    userId: string,
  ): Promise<AgentCollaboration> {
    try {
      // Validate workflow and agents
      await this.validateWorkflow(workflow, organizationId);

      const collaboration = this.collaborationRepository.create({
        name,
        agentIds: this.extractAgentIds(workflow),
        coordinatorId: workflow.steps[0]?.agentId,
        workflow: workflow as any,
        organizationId,
        createdBy: userId,
        status: 'ACTIVE',
        sharedContext: {},
      });

      const savedCollaboration = await this.collaborationRepository.save(collaboration);

      // Emit creation event
      this.apixGateway.publishToOrganization(organizationId, {
        type: 'agent_collaboration_created',
        payload: {
          organizationId,
          userId,
          collaborationId: savedCollaboration.id,
          name: savedCollaboration.name,
          agentIds: savedCollaboration.agentIds,
          timestamp: new Date(),
        },
        timestamp: new Date(),
      });

      this.logger.log(`Agent collaboration created: ${savedCollaboration.id}`);
      return savedCollaboration;
    } catch (error) {
      this.logger.error(`Failed to create collaboration: ${error instanceof Error ? error.message : String(error)}`, error instanceof Error ? error.stack : undefined);
      throw error;
    }
  }

  async executeCollaboration(
    collaborationId: string,
    input: string,
    organizationId: string,
    userId: string,
    options?: {
      sessionId?: string;
      context?: Record<string, any>;
      streaming?: boolean;
    },
  ): Promise<{
    collaborationId: string;
    sessionId: string;
    results: Array<{
      stepId: string;
      agentId: string;
      output: string;
      executionId: string;
      metadata: Record<string, any>;
    }>;
    finalOutput: string;
    sharedContext: Record<string, any>;
  }> {
    try {
      const collaboration = await this.collaborationRepository.findOne({
        where: { id: collaborationId, organizationId },
      });

      if (!collaboration) {
        throw new Error('Collaboration not found');
      }

      if (collaboration.status !== 'ACTIVE') {
        throw new Error('Collaboration is not active');
      }

      const workflow = collaboration.workflow as CollaborationWorkflow;

      // Create or get session
      let sessionId = options?.sessionId;
      if (!sessionId) {
        const session = await this.sessionMemoryService.createSession(
          collaboration.coordinatorId,
          organizationId,
          { enableLongTerm: true, maxTokens: 8000 }
        );
        sessionId = session.id;
      }

      // Initialize execution context
      const executionContext = {
        input,
        sharedContext: { ...collaboration.sharedContext, ...options?.context },
        results: [],
        sessionId,
        organizationId,
      };

      // Emit collaboration started event
      this.apixGateway.publishToOrganization(organizationId, {
        type: 'agent_collaboration_started',
        payload: {
          collaborationId,
          sessionId,
          userId,
          input,
          organizationId,
          timestamp: new Date(),
        },
        timestamp: new Date(),
      });

      // Execute workflow
      const results = await this.executeWorkflow(workflow, executionContext);

      // Update collaboration with final shared context
      collaboration.sharedContext = executionContext.sharedContext;
      await this.collaborationRepository.save(collaboration);

      const finalOutput = this.determineFinalOutput(results, workflow);

      // Emit collaboration completed event
      this.apixGateway.publishToOrganization(organizationId, {
        type: 'agent_collaboration_completed',
        payload: {
        collaborationId,
        sessionId,
        finalOutput,
        resultsCount: results.length,
        },
        timestamp: new Date(),
      });

      return {
        collaborationId,
        sessionId,
        results,
        finalOutput,
        sharedContext: executionContext.sharedContext,
      };
    } catch (error) {
      this.logger.error(`Collaboration execution failed: ${error instanceof Error ? error.message : String(error)}`, error instanceof Error ? error.stack : undefined );
      
      // Emit failure event
      this.apixGateway.publishToOrganization(organizationId, {
        type: 'agent_collaboration_failed',
        payload: {
        collaborationId,
          error: error instanceof Error ? error.message : String(error),
          timestamp: new Date(),
        },
        timestamp: new Date(),
      });

      throw error;
    }
  }

  private async executeWorkflow(
    workflow: CollaborationWorkflow,
    context: any,
  ): Promise<Array<{
    stepId: string;
    agentId: string;
    output: string;
    executionId: string;
    metadata: Record<string, any>;
  }>> {
    const results = [];
    let currentIteration = 0;
    const maxIterations = workflow.settings.maxIterations || 10;

    while (currentIteration < maxIterations) {
      let stepExecuted = false;

      for (const step of workflow.steps) {
        // Check if step should be skipped
        if (await this.shouldSkipStep(step, context)) {
          continue;
        }

        try {
          const stepResult = await this.executeStep(step, context, workflow);
          results.push(stepResult);
          stepExecuted = true;

          // Update context with step result
          this.updateContextWithResult(context, step, stepResult);

          // Emit step completed event
          this.apixGateway.publishToOrganization(context.organizationId, {
            type: 'collaboration_step_completed',
            payload: {
            stepId: step.id,
            agentId: step.agentId,
            output: stepResult.output,
            timestamp: new Date(),
            },
            timestamp: new Date(),  
          });

        } catch (error) {
          this.logger.error(`Step ${step.id} failed: ${error instanceof Error ? error.message : String(error)}`);
          
          if (workflow.settings.failureStrategy === 'stop') {
            throw error;
          } else if (workflow.settings.failureStrategy === 'retry' && 
                     (step.conditions?.maxRetries || 0) > 0) {
            // Implement retry logic
            continue;
          }
          // Continue with next step if strategy is 'continue'
        }
      }

      // Check workflow completion conditions
      if (!stepExecuted || await this.isWorkflowComplete(workflow, context)) {
        break;
      }

      currentIteration++;
    }

    return results;
  }

  private async executeStep(
    step: CollaborationStep,
    context: any,
    workflow: CollaborationWorkflow,
  ): Promise<{
    stepId: string;
    agentId: string;
    output: string;
    executionId: string;
    metadata: Record<string, any>;
  }> {
    // Get agent
    const agent = await this.agentRepository.findOne({
      where: { id: step.agentId, organizationId: context.organizationId },
      relations: ['template'],
    });

    if (!agent) {
      throw new Error(`Agent ${step.agentId} not found`);
    }

    // Prepare input for agent
    const agentInput = await this.prepareStepInput(step, context);

    // Build messages for AI provider
    const messages = [];

    // Add system message based on step type
    messages.push({
      role: 'system' as const,
      content: this.buildStepSystemPrompt(step, agent, workflow, context),
    });

    // Add conversation history if available
    if (context.sessionId) {
      const history = await this.sessionMemoryService.getSessionHistory(
        context.sessionId,
        context.organizationId,
        5 // Last 5 messages
      );
      
      messages.push(...history.map(msg => ({
        role: msg.role as 'user' | 'assistant',
        content: msg.content,
      })));
    }

    // Add current input
    messages.push({
      role: 'user' as const,
      content: agentInput,
    });

    // Execute through AI provider
    const aiRequest = {
      requestId: `collab_${step.id}_${Date.now()}`,
      messages,
      temperature: agent.config?.temperature || 0.7,
      maxTokens: agent.config?.maxTokens || 1500,
      organizationId: context.organizationId,
    };

    const aiResponse = await this.aiProviderIntegration.processRequest(aiRequest);

    // Create execution record
    const execution = this.executionRepository.create({
      agentId: step.agentId,
      sessionId: context.sessionId,
      input: agentInput,
      output: aiResponse.content,
      status: 'COMPLETED',
      context: {
        collaborationStep: step.id,
        stepType: step.type,
        sharedContext: context.sharedContext,
      },
      metadata: {
        provider: aiResponse.providerId,
        model: aiResponse.modelId,
        tokens: aiResponse.usage,
        cost: aiResponse.cost,
        latency: aiResponse.latency,
      },
      organizationId: context.organizationId,
      completedAt: new Date(),
    });

    const savedExecution = await this.executionRepository.save(execution);

    // Update session memory
    if (context.sessionId) {
      await this.sessionMemoryService.addMessage(context.sessionId, {
        role: 'user',
        content: agentInput,
        timestamp: new Date(),
        metadata: { stepId: step.id, stepType: step.type },
      });

      await this.sessionMemoryService.addMessage(context.sessionId, {
        role: 'assistant',
        content: aiResponse.content,
        timestamp: new Date(),
        metadata: { 
          stepId: step.id, 
          stepType: step.type,
          agentId: step.agentId,
          ...savedExecution.metadata 
        },
      });
    }

    return {
      stepId: step.id,
      agentId: step.agentId,
      output: aiResponse.content,
      executionId: savedExecution.id,
      metadata: savedExecution.metadata,
    };
  }

  private buildStepSystemPrompt(
    step: CollaborationStep,
    agent: Agent,
    workflow: CollaborationWorkflow,
    context: any,
  ): string {
    let prompt = agent.template?.promptTemplate || 'You are a helpful AI assistant.';

    // Add step-specific instructions
    switch (step.type) {
      case 'execute':
        prompt += '\n\nYou are executing a specific task as part of a collaborative workflow. Focus on completing your assigned task effectively.';
        break;
      case 'review':
        prompt += '\n\nYou are reviewing the work of other agents in this collaboration. Provide constructive feedback and identify areas for improvement.';
        break;
      case 'synthesize':
        prompt += '\n\nYou are synthesizing information from multiple agents. Combine their outputs into a coherent and comprehensive response.';
        break;
      case 'validate':
        prompt += '\n\nYou are validating the results of this collaboration. Check for accuracy, completeness, and quality.';
        break;
    }

    // Add collaboration context
    prompt += `\n\nCollaboration Context:
- Workflow: ${workflow.name}
- Step: ${step.id} (${step.type})
- Previous results available in shared context
- Your role: ${agent.name}`;

    // Add shared context information
    if (Object.keys(context.sharedContext).length > 0) {
      prompt += '\n\nShared Context:\n';
      Object.entries(context.sharedContext).forEach(([key, value]) => {
        if (typeof value === 'string' && value.length < 500) {
          prompt += `- ${key}: ${value}\n`;
        } else {
          prompt += `- ${key}: [Complex data available]\n`;
        }
      });
    }

    return prompt;
  }

  private async prepareStepInput(step: CollaborationStep, context: any): string {
    switch (step.input.source) {
      case 'user':
        return context.input;
      case 'previous':
        const lastResult = context.results[context.results.length - 1];
        return lastResult?.output || context.input;
      case 'context':
        return JSON.stringify(context.sharedContext, null, 2);
      case 'shared':
        const key = step.input.transformation;
        return context.sharedContext[key] || context.input;
      default:
        return context.input;
    }
  }

  private updateContextWithResult(context: any, step: CollaborationStep, result: any): void {
    switch (step.output.target) {
      case 'shared':
        const key = step.output.key || `step_${step.id}_output`;
        context.sharedContext[key] = result.output;
        context.sharedContext[`${key}_metadata`] = result.metadata;
        break;
      case 'final':
        context.finalOutput = result.output;
        break;
      // 'next' is handled automatically by workflow execution order
    }

    // Always add to results
    context.results.push(result);
  }

  private async shouldSkipStep(step: CollaborationStep, context: any): Promise<boolean> {
    if (!step.conditions?.skipIf) {
      return false;
    }

    // Simple condition evaluation (in production, use a proper expression evaluator)
    try {
      const condition = step.conditions.skipIf;
      return this.evaluateCondition(condition, context);
    } catch (error) {
      this.logger.warn(`Failed to evaluate skip condition for step ${step.id}: ${error instanceof Error ? error.message : String(error)}`);
      return false;
    }
  }

  private async isWorkflowComplete(workflow: CollaborationWorkflow, context: any): Promise<boolean> {
    // Check if all required steps have been executed
    const executedSteps = new Set(context.results.map(r => r.stepId));
    const requiredSteps = workflow.steps.filter(s => !s.conditions?.skipIf);
    
    return requiredSteps.every(step => executedSteps.has(step.id));
  }

  private evaluateCondition(condition: string, context: any): boolean {
    // Simple condition evaluation - in production, use a proper expression evaluator
    // This is a simplified implementation for demonstration
    try {
      // Replace context variables
      let evaluableCondition = condition;
      Object.entries(context.sharedContext).forEach(([key, value]) => {
        evaluableCondition = evaluableCondition.replace(
          new RegExp(`\\$\\{${key}\\}`, 'g'),
          JSON.stringify(value)
        );
      });

      // For safety, only allow simple comparisons
      if (evaluableCondition.includes('==') || evaluableCondition.includes('!=') || 
          evaluableCondition.includes('>') || evaluableCondition.includes('<')) {
        return eval(evaluableCondition);
      }

      return false;
    } catch (error) {
      this.logger.warn(`Condition evaluation failed: ${error instanceof Error ? error.message : String(error)}`);
      return false;
    }
  }

  private determineFinalOutput(results: any[], workflow: CollaborationWorkflow): string {
    // Find the final output from results
    const finalResult = results.find(r => 
      workflow.steps.find(s => s.id === r.stepId)?.output.target === 'final'
    );

    if (finalResult) {
      return finalResult.output;
    }

    // If no explicit final output, use the last result
    return results[results.length - 1]?.output || 'No output generated';
  }

  private async validateWorkflow(workflow: CollaborationWorkflow, organizationId: string): Promise<void> {
    // Validate that all referenced agents exist
    const agentIds = this.extractAgentIds(workflow);
    const agents = await this.agentRepository.find({
      where: { id: agentIds as any, organizationId },
    });

    if (agents.length !== agentIds.length) {
      throw new Error('One or more agents in the workflow do not exist');
    }

    // Validate workflow structure
    if (!workflow.steps || workflow.steps.length === 0) {
      throw new Error('Workflow must have at least one step');
    }

    // Validate step references
    const stepIds = new Set(workflow.steps.map(s => s.id));
    workflow.steps.forEach(step => {
      if (!agentIds.includes(step.agentId)) {
        throw new Error(`Step ${step.id} references non-existent agent ${step.agentId}`);
      }
    });
  }

  private extractAgentIds(workflow: CollaborationWorkflow): string[] {
    return [...new Set(workflow.steps.map(step => step.agentId))];
  }

  async getCollaborationsByOrganization(organizationId: string): Promise<AgentCollaboration[]> {
    return this.collaborationRepository.find({
      where: { organizationId },
      order: { createdAt: 'DESC' },
    });
  }

  async getCollaborationById(collaborationId: string, organizationId: string): Promise<AgentCollaboration> {
    const collaboration = await this.collaborationRepository.findOne({
      where: { id: collaborationId, organizationId },
    });

    if (!collaboration) {
      throw new Error('Collaboration not found');
    }

    return collaboration;
  }

  async updateCollaborationStatus(
    collaborationId: string,
    status: string,
    organizationId: string,
  ): Promise<AgentCollaboration> {
    const collaboration = await this.getCollaborationById(collaborationId, organizationId);
    collaboration.status = status;
    return this.collaborationRepository.save(collaboration);
  }
}