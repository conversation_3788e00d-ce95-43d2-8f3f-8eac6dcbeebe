import { RoleService, CreateRoleDto, UpdateRoleDto, RoleFilters } from './roles.service';
export declare class RoleController {
    private readonly roleService;
    constructor(roleService: RoleService);
    createRole(createRoleDto: CreateRoleDto, req: any): Promise<any>;
    getRoles(filters: RoleFilters, req: any): Promise<{
        roles: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            pages: number;
        };
    }>;
    getRoleById(id: string): Promise<any>;
    updateRole(id: string, updateRoleDto: UpdateRoleDto, req: any): Promise<any>;
    deleteRole(id: string, req: any): Promise<{
        message: string;
    }>;
    assignRoleToUser(roleId: string, userId: string, req: any): Promise<{
        message: string;
    }>;
    removeRoleFromUser(roleId: string, userId: string, req: any): Promise<{
        message: string;
    }>;
}
