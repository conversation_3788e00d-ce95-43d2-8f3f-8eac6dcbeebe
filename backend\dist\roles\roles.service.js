"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoleService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const apix_gateway_1 = require("../websocket/apix.gateway");
let RoleService = class RoleService {
    constructor(prisma, apixGateway) {
        this.prisma = prisma;
        this.apixGateway = apixGateway;
    }
    async createRole(createRoleDto, createdById) {
        const { name, description, organizationId, permissionIds = [] } = createRoleDto;
        const existingRole = await this.prisma.role.findUnique({
            where: {
                name_organizationId: {
                    name,
                    organizationId,
                },
            },
        });
        if (existingRole) {
            throw new common_1.ConflictException('Role with this name already exists in organization');
        }
        const organization = await this.prisma.organization.findUnique({
            where: { id: organizationId },
        });
        if (!organization) {
            throw new common_1.NotFoundException('Organization not found');
        }
        if (permissionIds.length > 0) {
            const permissions = await this.prisma.permission.findMany({
                where: {
                    id: { in: permissionIds },
                    organizationId,
                },
            });
            if (permissions.length !== permissionIds.length) {
                throw new common_1.BadRequestException('Some permissions not found or do not belong to organization');
            }
        }
        const result = await this.prisma.$transaction(async (tx) => {
            const role = await tx.role.create({
                data: {
                    name,
                    description,
                    organizationId,
                },
                include: {
                    organization: true,
                    rolePermissions: {
                        include: {
                            permission: true,
                        },
                    },
                },
            });
            if (permissionIds.length > 0) {
                await tx.rolePermission.createMany({
                    data: permissionIds.map(permissionId => ({
                        roleId: role.id,
                        permissionId,
                        organizationId,
                    })),
                });
            }
            await tx.auditLog.create({
                data: {
                    userId: createdById,
                    organizationId,
                    action: 'CREATE',
                    resource: 'role',
                    resourceId: role.id,
                    details: {
                        action: 'role_created',
                        name: role.name,
                        description: role.description,
                        permissionIds,
                    },
                },
            });
            return role;
        });
        await this.apixGateway.publishToOrganization(organizationId, {
            type: 'role.created',
            payload: {
                roleId: result.id,
                name: result.name,
                description: result.description,
                createdBy: createdById,
            },
        });
        return result;
    }
    async updateRole(roleId, updateRoleDto, updatedById) {
        const { name, description, isActive, permissionIds } = updateRoleDto;
        const existingRole = await this.prisma.role.findUnique({
            where: { id: roleId },
            include: {
                organization: true,
                rolePermissions: true,
            },
        });
        if (!existingRole) {
            throw new common_1.NotFoundException('Role not found');
        }
        if (existingRole.isSystem) {
            throw new common_1.BadRequestException('Cannot modify system role');
        }
        if (name && name !== existingRole.name) {
            const nameExists = await this.prisma.role.findUnique({
                where: {
                    name_organizationId: {
                        name,
                        organizationId: existingRole.organizationId,
                    },
                },
            });
            if (nameExists) {
                throw new common_1.ConflictException('Role name already exists in organization');
            }
        }
        if (permissionIds !== undefined && permissionIds.length > 0) {
            const permissions = await this.prisma.permission.findMany({
                where: {
                    id: { in: permissionIds },
                    organizationId: existingRole.organizationId,
                },
            });
            if (permissions.length !== permissionIds.length) {
                throw new common_1.BadRequestException('Some permissions not found or do not belong to organization');
            }
        }
        const result = await this.prisma.$transaction(async (tx) => {
            const role = await tx.role.update({
                where: { id: roleId },
                data: Object.assign(Object.assign(Object.assign({}, (name && { name })), (description !== undefined && { description })), (isActive !== undefined && { isActive })),
                include: {
                    organization: true,
                    rolePermissions: {
                        include: {
                            permission: true,
                        },
                    },
                },
            });
            if (permissionIds !== undefined) {
                await tx.rolePermission.deleteMany({
                    where: { roleId },
                });
                if (permissionIds.length > 0) {
                    await tx.rolePermission.createMany({
                        data: permissionIds.map(permissionId => ({
                            roleId,
                            permissionId,
                            organizationId: existingRole.organizationId,
                        })),
                    });
                }
            }
            await tx.auditLog.create({
                data: {
                    userId: updatedById,
                    organizationId: existingRole.organizationId,
                    action: 'UPDATE',
                    resource: 'role',
                    resourceId: roleId,
                    details: {
                        action: 'role_updated',
                        changes: updateRoleDto,
                    },
                },
            });
            return role;
        });
        await this.apixGateway.publishToOrganization(existingRole.organizationId, {
            type: 'role.updated',
            payload: {
                roleId,
                changes: updateRoleDto,
                updatedBy: updatedById,
            },
        });
        return result;
    }
    async deleteRole(roleId, deletedById) {
        const role = await this.prisma.role.findUnique({
            where: { id: roleId },
            include: {
                organization: true,
                userRoles: true,
            },
        });
        if (!role) {
            throw new common_1.NotFoundException('Role not found');
        }
        if (role.isSystem) {
            throw new common_1.BadRequestException('Cannot delete system role');
        }
        if (role.userRoles.length > 0) {
            throw new common_1.BadRequestException('Cannot delete role that is assigned to users');
        }
        await this.prisma.$transaction(async (tx) => {
            await tx.rolePermission.deleteMany({
                where: { roleId },
            });
            await tx.role.delete({
                where: { id: roleId },
            });
            await tx.auditLog.create({
                data: {
                    userId: deletedById,
                    organizationId: role.organizationId,
                    action: 'DELETE',
                    resource: 'role',
                    resourceId: roleId,
                    details: {
                        action: 'role_deleted',
                        name: role.name,
                        description: role.description,
                    },
                },
            });
        });
        await this.apixGateway.publishToOrganization(role.organizationId, {
            type: 'role.deleted',
            payload: {
                roleId,
                name: role.name,
                deletedBy: deletedById,
            },
        });
        return { message: 'Role deleted successfully' };
    }
    async getRoles(filters) {
        const { search, organizationId, isActive, page = 1, limit = 20, sortBy = 'createdAt', sortOrder = 'desc', } = filters;
        const skip = (page - 1) * limit;
        const where = {};
        if (organizationId) {
            where.organizationId = organizationId;
        }
        if (search) {
            where.OR = [
                { name: { contains: search, mode: 'insensitive' } },
                { description: { contains: search, mode: 'insensitive' } },
            ];
        }
        if (isActive !== undefined) {
            where.isActive = isActive;
        }
        const [roles, total] = await Promise.all([
            this.prisma.role.findMany({
                where,
                include: {
                    organization: true,
                    rolePermissions: {
                        include: {
                            permission: true,
                        },
                    },
                    userRoles: {
                        include: {
                            user: {
                                select: {
                                    id: true,
                                    name: true,
                                    email: true,
                                },
                            },
                        },
                    },
                },
                orderBy: { [sortBy]: sortOrder },
                skip,
                take: limit,
            }),
            this.prisma.role.count({ where }),
        ]);
        return {
            roles,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit),
            },
        };
    }
    async getRoleById(roleId) {
        const role = await this.prisma.role.findUnique({
            where: { id: roleId },
            include: {
                organization: true,
                rolePermissions: {
                    include: {
                        permission: true,
                    },
                },
                userRoles: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                name: true,
                                email: true,
                                systemRole: true,
                                isActive: true,
                            },
                        },
                    },
                },
            },
        });
        if (!role) {
            throw new common_1.NotFoundException('Role not found');
        }
        return role;
    }
    async assignRoleToUser(roleId, userId, assignedById) {
        const role = await this.prisma.role.findUnique({
            where: { id: roleId },
            include: { organization: true },
        });
        if (!role) {
            throw new common_1.NotFoundException('Role not found');
        }
        const user = await this.prisma.user.findUnique({
            where: { id: userId, organizationId: role.organizationId },
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found in organization');
        }
        const existingAssignment = await this.prisma.userRole.findUnique({
            where: {
                userId_roleId: {
                    userId,
                    roleId,
                },
            },
        });
        if (existingAssignment) {
            throw new common_1.ConflictException('User already has this role');
        }
        await this.prisma.$transaction(async (tx) => {
            await tx.userRole.create({
                data: {
                    userId,
                    roleId,
                    organizationId: role.organizationId,
                },
            });
            await tx.auditLog.create({
                data: {
                    userId: assignedById,
                    organizationId: role.organizationId,
                    action: 'CREATE',
                    resource: 'user_role',
                    resourceId: `${userId}-${roleId}`,
                    details: {
                        action: 'role_assigned',
                        userId,
                        roleId,
                        roleName: role.name,
                        userEmail: user.email,
                    },
                },
            });
        });
        await this.apixGateway.publishToOrganization(role.organizationId, {
            type: 'role.assigned',
            payload: {
                userId,
                roleId,
                roleName: role.name,
                userEmail: user.email,
                assignedBy: assignedById,
            },
        });
        return { message: 'Role assigned successfully' };
    }
    async removeRoleFromUser(roleId, userId, removedById) {
        const assignment = await this.prisma.userRole.findUnique({
            where: {
                userId_roleId: {
                    userId,
                    roleId,
                },
            },
            include: {
                role: true,
                user: true,
            },
        });
        if (!assignment) {
            throw new common_1.NotFoundException('Role assignment not found');
        }
        await this.prisma.$transaction(async (tx) => {
            await tx.userRole.delete({
                where: {
                    userId_roleId: {
                        userId,
                        roleId,
                    },
                },
            });
            await tx.auditLog.create({
                data: {
                    userId: removedById,
                    organizationId: assignment.organizationId,
                    action: 'DELETE',
                    resource: 'user_role',
                    resourceId: `${userId}-${roleId}`,
                    details: {
                        action: 'role_removed',
                        userId,
                        roleId,
                        roleName: assignment.role.name,
                        userEmail: assignment.user.email,
                    },
                },
            });
        });
        await this.apixGateway.publishToOrganization(assignment.organizationId, {
            type: 'role.removed',
            payload: {
                userId,
                roleId,
                roleName: assignment.role.name,
                userEmail: assignment.user.email,
                removedBy: removedById,
            },
        });
        return { message: 'Role removed successfully' };
    }
};
exports.RoleService = RoleService;
exports.RoleService = RoleService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        apix_gateway_1.ApixGateway])
], RoleService);
//# sourceMappingURL=roles.service.js.map