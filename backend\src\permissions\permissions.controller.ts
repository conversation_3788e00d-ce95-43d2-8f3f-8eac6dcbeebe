import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
} from '@nestjs/common';
import { PermissionService, CreatePermissionDto, UpdatePermissionDto, PermissionFilters } from './permissions.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { PermissionGuard } from '../auth/permission.guard';
import { RequirePermissions } from '../auth/permissions.decorator';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

@ApiTags('permissions')
@ApiBearerAuth()
@Controller('api/permissions')
@UseGuards(JwtAuthGuard)
export class PermissionController {
  constructor(private readonly permissionService: PermissionService) {}

  @Post()
  @UseGuards(PermissionGuard)
  @RequirePermissions('permissions.write')
  @ApiOperation({ summary: 'Create a new permission' })
  async createPermission(@Body() createPermissionDto: CreatePermissionDto, @Request() req) {
    createPermissionDto.organizationId = req.user.organizationId;
    return this.permissionService.createPermission(createPermissionDto, req.user.userId);
  }

  @Get()
  @UseGuards(PermissionGuard)
  @RequirePermissions('permissions.read')
  @ApiOperation({ summary: 'Get permissions with filtering and pagination' })
  async getPermissions(@Query() filters: PermissionFilters, @Request() req) {
    filters.organizationId = req.user.organizationId;
    return this.permissionService.getPermissions(filters);
  }

  @Get('resources')
  @UseGuards(PermissionGuard)
  @RequirePermissions('permissions.read')
  @ApiOperation({ summary: 'Get list of resources' })
  async getResourceList(@Request() req) {
    return this.permissionService.getResourceList(req.user.organizationId);
  }

  @Get('actions')
  @UseGuards(PermissionGuard)
  @RequirePermissions('permissions.read')
  @ApiOperation({ summary: 'Get list of actions' })
  async getActionList(@Query('resource') resource: string, @Request() req) {
    return this.permissionService.getActionList(req.user.organizationId, resource);
  }

  @Get(':id')
  @UseGuards(PermissionGuard)
  @RequirePermissions('permissions.read')
  @ApiOperation({ summary: 'Get permission by ID' })
  async getPermissionById(@Param('id') id: string) {
    return this.permissionService.getPermissionById(id);
  }

  @Put(':id')
  @UseGuards(PermissionGuard)
  @RequirePermissions('permissions.write')
  @ApiOperation({ summary: 'Update permission' })
  async updatePermission(
    @Param('id') id: string,
    @Body() updatePermissionDto: UpdatePermissionDto,
    @Request() req,
  ) {
    return this.permissionService.updatePermission(id, updatePermissionDto, req.user.userId);
  }

  @Delete(':id')
  @UseGuards(PermissionGuard)
  @RequirePermissions('permissions.write')
  @ApiOperation({ summary: 'Delete permission' })
  async deletePermission(@Param('id') id: string, @Request() req) {
    return this.permissionService.deletePermission(id, req.user.userId);
  }

  @Post(':permissionId/users/:userId')
  @UseGuards(PermissionGuard)
  @RequirePermissions('permissions.write')
  @ApiOperation({ summary: 'Assign permission to user' })
  async assignPermissionToUser(
    @Param('permissionId') permissionId: string,
    @Param('userId') userId: string,
    @Request() req,
  ) {
    return this.permissionService.assignPermissionToUser(permissionId, userId, req.user.userId);
  }

  @Delete(':permissionId/users/:userId')
  @UseGuards(PermissionGuard)
  @RequirePermissions('permissions.write')
  @ApiOperation({ summary: 'Remove permission from user' })
  async removePermissionFromUser(
    @Param('permissionId') permissionId: string,
    @Param('userId') userId: string,
    @Request() req,
  ) {
    return this.permissionService.removePermissionFromUser(permissionId, userId, req.user.userId);
  }
}