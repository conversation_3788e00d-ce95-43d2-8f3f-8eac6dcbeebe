import { Repository } from 'typeorm';
import { AgentTemplate } from '../database/entities/agent.entity';
import { CreateAgentTemplateDto } from './dto/agent.dto';
export declare class AgentTemplatesService {
    private templateRepository;
    private readonly logger;
    constructor(templateRepository: Repository<AgentTemplate>);
    createTemplate(createTemplateDto: CreateAgentTemplateDto, organizationId: string, userId: string): Promise<AgentTemplate>;
    getTemplatesByOrganization(organizationId: string): Promise<AgentTemplate[]>;
    getTemplateById(templateId: string, organizationId: string): Promise<AgentTemplate>;
    updateTemplate(templateId: string, updateData: Partial<CreateAgentTemplateDto>, organizationId: string): Promise<AgentTemplate>;
    deleteTemplate(templateId: string, organizationId: string): Promise<void>;
    getPublicTemplates(): Promise<AgentTemplate[]>;
    getTemplatesByCategory(category: string, organizationId: string): Promise<AgentTemplate[]>;
    seedDefaultTemplates(): Promise<void>;
}
