{"version": 3, "file": "agent-templates.service.js", "sourceRoot": "", "sources": ["../../src/agents/agent-templates.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,6CAAmD;AACnD,qCAAqC;AACrC,oEAAkE;AAI3D,IAAM,qBAAqB,6BAA3B,MAAM,qBAAqB;IAGhC,YAEE,kBAAqD;QAA7C,uBAAkB,GAAlB,kBAAkB,CAA2B;QAJtC,WAAM,GAAG,IAAI,eAAM,CAAC,uBAAqB,CAAC,IAAI,CAAC,CAAC;IAK9D,CAAC;IAEJ,KAAK,CAAC,cAAc,CAClB,iBAAyC,EACzC,cAAsB,EACtB,MAAc;QAEd,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,iCAC1C,iBAAiB,KACpB,cAAc,EACd,SAAS,EAAE,MAAM,IACjB,CAAC;YAEH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,2BAA2B,aAAa,CAAC,EAAE,EAAE,CAAC,CAAC;YAC/D,OAAO,aAAa,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACvE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,0BAA0B,CAAC,cAAsB;QACrD,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;YAClC,KAAK,EAAE;gBACL,EAAE,cAAc,EAAE;gBAClB,EAAE,QAAQ,EAAE,IAAI,EAAE;aACnB;YACD,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;SAC7B,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,UAAkB,EAAE,cAAsB;QAC9D,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;YACrD,KAAK,EAAE;gBACL,EAAE,EAAE,EAAE,UAAU,EAAE,cAAc,EAAE;gBAClC,EAAE,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,IAAI,EAAE;aACnC;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;QACxC,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,cAAc,CAClB,UAAkB,EAClB,UAA2C,EAC3C,cAAsB;QAEtB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;YACrD,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE,cAAc,EAAE;SAC1C,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;QACxC,CAAC;QAED,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QACpC,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAChD,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,UAAkB,EAAE,cAAsB;QAC7D,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;YAClD,EAAE,EAAE,UAAU;YACd,cAAc;SACf,CAAC,CAAC;QAEH,IAAI,MAAM,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;YAC1B,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;QACxC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,kBAAkB;QACtB,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;YAClC,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;YACzB,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;SAC7B,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,sBAAsB,CAAC,QAAgB,EAAE,cAAsB;QACnE,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;YAClC,KAAK,EAAE;gBACL,EAAE,QAAQ,EAAE,cAAc,EAAE;gBAC5B,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE;aAC7B;YACD,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;SAC7B,CAAC,CAAC;IACL,CAAC;IAGD,KAAK,CAAC,oBAAoB;QACxB,MAAM,gBAAgB,GAAG;YACvB;gBACE,IAAI,EAAE,wBAAwB;gBAC9B,QAAQ,EAAE,kBAAkB;gBAC5B,WAAW,EAAE,wDAAwD;gBACrE,MAAM,EAAE;oBACN,WAAW,EAAE,SAAS;oBACtB,aAAa,EAAE,cAAc;oBAC7B,iBAAiB,EAAE,GAAG;iBACvB;gBACD,MAAM,EAAE,CAAC,kBAAkB,EAAE,mBAAmB,EAAE,YAAY,CAAC;gBAC/D,QAAQ,EAAE,IAAI;gBACd,cAAc,EAAE;mEAC2C;gBAC3D,IAAI,EAAE,OAAO;gBACb,kBAAkB,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;gBACxC,SAAS,EAAE,QAAQ;gBACnB,cAAc,EAAE,QAAQ;aACzB;YACD;gBACE,IAAI,EAAE,iBAAiB;gBACvB,QAAQ,EAAE,OAAO;gBACjB,WAAW,EAAE,qDAAqD;gBAClE,MAAM,EAAE;oBACN,WAAW,EAAE,YAAY;oBACzB,aAAa,EAAE,UAAU;oBACzB,iBAAiB,EAAE,GAAG;iBACvB;gBACD,MAAM,EAAE,CAAC,oBAAoB,EAAE,cAAc,EAAE,oBAAoB,CAAC;gBACpE,QAAQ,EAAE,IAAI;gBACd,cAAc,EAAE;uDAC+B;gBAC/C,IAAI,EAAE,aAAa;gBACnB,kBAAkB,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;gBAClD,SAAS,EAAE,QAAQ;gBACnB,cAAc,EAAE,QAAQ;aACzB;YACD;gBACE,IAAI,EAAE,iBAAiB;gBACvB,QAAQ,EAAE,WAAW;gBACrB,WAAW,EAAE,gDAAgD;gBAC7D,MAAM,EAAE;oBACN,WAAW,EAAE,UAAU;oBACvB,aAAa,EAAE,UAAU;oBACzB,iBAAiB,EAAE,IAAI;iBACxB;gBACD,MAAM,EAAE,CAAC,iBAAiB,EAAE,kBAAkB,EAAE,cAAc,CAAC;gBAC/D,QAAQ,EAAE,IAAI;gBACd,cAAc,EAAE;wDACgC;gBAChD,IAAI,EAAE,QAAQ;gBACd,kBAAkB,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;gBAClD,SAAS,EAAE,QAAQ;gBACnB,cAAc,EAAE,QAAQ;aACzB;YACD;gBACE,IAAI,EAAE,cAAc;gBACpB,QAAQ,EAAE,WAAW;gBACrB,WAAW,EAAE,qCAAqC;gBAClD,MAAM,EAAE;oBACN,WAAW,EAAE,YAAY;oBACzB,aAAa,EAAE,UAAU;oBACzB,iBAAiB,EAAE,GAAG;iBACvB;gBACD,MAAM,EAAE,CAAC,eAAe,EAAE,eAAe,EAAE,WAAW,CAAC;gBACvD,QAAQ,EAAE,IAAI;gBACd,cAAc,EAAE;iEACyC;gBACzD,IAAI,EAAE,aAAa;gBACnB,kBAAkB,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;gBACxC,SAAS,EAAE,QAAQ;gBACnB,cAAc,EAAE,QAAQ;aACzB;YACD;gBACE,IAAI,EAAE,iBAAiB;gBACvB,QAAQ,EAAE,YAAY;gBACtB,WAAW,EAAE,4CAA4C;gBACzD,MAAM,EAAE;oBACN,WAAW,EAAE,WAAW;oBACxB,aAAa,EAAE,YAAY;oBAC3B,iBAAiB,EAAE,GAAG;iBACvB;gBACD,MAAM,EAAE,CAAC,kBAAkB,EAAE,mBAAmB,EAAE,iBAAiB,CAAC;gBACpE,QAAQ,EAAE,IAAI;gBACd,cAAc,EAAE;wDACgC;gBAChD,IAAI,EAAE,YAAY;gBAClB,kBAAkB,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;gBAClD,SAAS,EAAE,QAAQ;gBACnB,cAAc,EAAE,QAAQ;aACzB;SACF,CAAC;QAEF,KAAK,MAAM,QAAQ,IAAI,gBAAgB,EAAE,CAAC;YACxC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;gBACnD,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,cAAc,EAAE,QAAQ,EAAE;aACzD,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC7C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,oBAAoB,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;YACvD,CAAC;QACH,CAAC;IACH,CAAC;CACF,CAAA;AA7MY,sDAAqB;gCAArB,qBAAqB;IADjC,IAAA,mBAAU,GAAE;IAKR,WAAA,IAAA,0BAAgB,EAAC,4BAAa,CAAC,CAAA;qCACJ,oBAAU;GAL7B,qBAAqB,CA6MjC"}