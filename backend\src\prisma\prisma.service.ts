import { Injectable, OnModuleInit } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';

@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit {
  constructor() {
    super({
      log: ['query', 'info', 'warn', 'error'],
    });
  }

  async onModuleInit() {
    await this.$connect();
  }

  async onModuleDestroy() {
    await this.$disconnect();
  }

  // Tenant-aware query helpers
  async findManyWithOrganization<T>(
    model: any,
    organizationId: string,
    args?: any
  ): Promise<T[]> {
    return model.findMany({
      ...args,
      where: {
        ...args?.where,
        organizationId,
      },
    });
  }

  async findUniqueWithOrganization<T>(
    model: any,
    organizationId: string,
    args: any
  ): Promise<T | null> {
    return model.findUnique({
      ...args,
      where: {
        ...args.where,
        organizationId,
      },
    });
  }

  async createWithOrganization<T>(
    model: any,
    organizationId: string,
    data: any
  ): Promise<T> {
    return model.create({
      data: {
        ...data,
        organizationId,
      },
    });
  }

  async updateWithOrganization<T>(
    model: any,
    organizationId: string,
    where: any,
    data: any
  ): Promise<T> {
    return model.update({
      where: {
        ...where,
        organizationId,
      },
      data,
    });
  }

  async deleteWithOrganization<T>(
    model: any,
    organizationId: string,
    where: any
  ): Promise<T> {
    return model.delete({
      where: {
        ...where,
        organizationId,
      },
    });
  }
}