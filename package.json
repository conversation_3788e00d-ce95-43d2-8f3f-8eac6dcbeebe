{"private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@nestjs/common": "^10.4.20", "@nestjs/config": "^4.0.2", "@nestjs/core": "^10.4.20", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^10.4.20", "@nestjs/platform-socket.io": "^10.4.20", "@nestjs/typeorm": "^11.0.0", "@nestjs/websockets": "^10.4.20", "@prisma/client": "^6.13.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.3", "@supabase/ssr": "latest", "@supabase/supabase-js": "latest", "autoprefixer": "10.4.20", "bcryptjs": "^3.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.4", "date-fns": "^4.1.0", "embla-carousel-react": "^8.5.2", "framer-motion": "^12.23.12", "ioredis": "^5.7.0", "lucide-react": "^0.468.0", "next": "14.2.23", "next-themes": "^0.2.1", "passport": "^0.7.0", "passport-github2": "^0.1.12", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "pg": "^8.16.3", "prettier": "^3.3.3", "prisma": "^6.13.0", "radix-ui": "^1.1.3", "react": "^18", "react-day-picker": "^9.5.1", "react-dom": "^18", "react-hook-form": "^7.54.2", "react-resizable-panels": "^2.1.7", "reactflow": "^11.11.4", "redis": "^4.7.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.7", "stripe": "^17.6.0", "tempo-devtools": "^2.0.109", "typeorm": "^0.3.25", "vaul": "^1.1.2", "zod": "^4.0.14"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "postcss": "^8", "tailwind-merge": "^2", "tailwindcss": "^3", "tailwindcss-animate": "^1", "typescript": "^5"}}