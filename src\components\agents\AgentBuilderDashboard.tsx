import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  <PERSON><PERSON>, 
  Brain, 
  Settings, 
  Play, 
  Save, 
  Users, 
  Zap, 
  MessageSquare,
  Code,
  Eye,
  Cpu,
  Network,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';

interface Agent {
  id?: string;
  name: string;
  type: 'BASIC' | 'TOOL_DRIVEN' | 'HYBRID' | 'MULTI_TASK' | 'MULTI_PROVIDER' | 'COLLABORATIVE';
  status: 'ACTIVE' | 'INACTIVE' | 'ERROR' | 'PAUSED';
  templateId: string;
  config: {
    temperature: number;
    maxTokens: number;
    systemPrompt: string;
    skills: string[];
    capabilities: string[];
  };
  primaryProvider: string;
  fallbackProviders: string[];
  memoryConfig: {
    maxTokens: number;
    retentionDays: number;
    enableLongTerm: boolean;
  };
  performanceMetrics?: {
    totalExecutions: number;
    successRate: number;
    averageResponseTime: number;
    lastExecuted: Date;
  };
}

interface AIProvider {
  id: string;
  name: string;
  type: string;
  isActive: boolean;
  models: Array<{
    id: string;
    name: string;
    capabilities: Record<string, boolean>;
  }>;
  performanceMetrics?: {
    averageLatency: number;
    reliability: number;
    healthStatus: 'healthy' | 'degraded' | 'unhealthy';
  };
}

interface AgentTemplate {
  id: string;
  name: string;
  category: string;
  description: string;
  type: string;
  promptTemplate: string;
  skills: string[];
  supportedProviders: string[];
}

export default function AgentBuilderDashboard() {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [providers, setProviders] = useState<AIProvider[]>([]);
  const [templates, setTemplates] = useState<AgentTemplate[]>([]);
  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [testMessage, setTestMessage] = useState('');
  const [testResult, setTestResult] = useState<any>(null);
  const [isTesting, setIsTesting] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  // Initialize with default agent
  const [currentAgent, setCurrentAgent] = useState<Agent>({
    name: '',
    type: 'BASIC',
    status: 'ACTIVE',
    templateId: '',
    config: {
      temperature: 0.7,
      maxTokens: 1000,
      systemPrompt: '',
      skills: [],
      capabilities: ['chat'],
    },
    primaryProvider: '',
    fallbackProviders: [],
    memoryConfig: {
      maxTokens: 4000,
      retentionDays: 30,
      enableLongTerm: false,
    },
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setIsLoading(true);
    try {
      // Load agents, providers, and templates
      const [agentsRes, providersRes, templatesRes] = await Promise.all([
        fetch('/api/v1/agents'),
        fetch('/api/v1/providers/active'),
        fetch('/api/v1/agent-templates'),
      ]);

      const agentsData = await agentsRes.json();
      const providersData = await providersRes.json();
      const templatesData = await templatesRes.json();

      if (agentsData.success) setAgents(agentsData.data);
      if (providersData.success) setProviders(providersData.data);
      if (templatesData.success) setTemplates(templatesData.data);
    } catch (error) {
      console.error('Failed to load data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveAgent = async () => {
    setIsLoading(true);
    try {
      const url = selectedAgent ? `/api/v1/agents/${selectedAgent.id}` : '/api/v1/agents';
      const method = selectedAgent ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(currentAgent),
      });

      const result = await response.json();
      
      if (result.success) {
        await loadData();
        setIsCreating(false);
        setSelectedAgent(null);
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Failed to save agent:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleTestAgent = async () => {
    if (!selectedAgent || !testMessage.trim()) return;

    setIsTesting(true);
    try {
      const response = await fetch(`/api/v1/agents/${selectedAgent.id}/execute`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message: testMessage,
          context: { testing: true },
        }),
      });

      const result = await response.json();
      setTestResult(result);
    } catch (error) {
      console.error('Test failed:', error);
      setTestResult({ success: false, error: error.message });
    } finally {
      setIsTesting(false);
    }
  };

  const handleTemplateSelect = (templateId: string) => {
    const template = templates.find(t => t.id === templateId);
    if (template) {
      setCurrentAgent(prev => ({
        ...prev,
        templateId: template.id,
        name: template.name,
        type: template.type as any,
        config: {
          ...prev.config,
          systemPrompt: template.promptTemplate,
          skills: template.skills,
          capabilities: template.skills.includes('vision') ? ['chat', 'vision'] : ['chat'],
        },
        primaryProvider: template.supportedProviders[0] || '',
        fallbackProviders: template.supportedProviders.slice(1) || [],
      }));
    }
  };

  const getProviderStatus = (provider: AIProvider) => {
    if (!provider.performanceMetrics) return 'unknown';
    return provider.performanceMetrics.healthStatus;
  };

  const getProviderLatency = (provider: AIProvider) => {
    return provider.performanceMetrics?.averageLatency || 0;
  };

  const getProviderReliability = (provider: AIProvider) => {
    return Math.round((provider.performanceMetrics?.reliability || 0.95) * 100);
  };

  if (isLoading && agents.length === 0) {
    return (
      <div className="flex items-center justify-center h-96 bg-white">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading Agent Builder...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                <Bot className="h-8 w-8 text-blue-600" />
                Agent Builder
              </h1>
              <p className="text-gray-600 mt-2">Create and manage intelligent AI agents with real-time collaboration</p>
            </div>
            <div className="flex gap-3">
              <Button
                onClick={() => {
                  setIsCreating(true);
                  setSelectedAgent(null);
                  setCurrentAgent({
                    name: '',
                    type: 'BASIC',
                    status: 'ACTIVE',
                    templateId: '',
                    config: {
                      temperature: 0.7,
                      maxTokens: 1000,
                      systemPrompt: '',
                      skills: [],
                      capabilities: ['chat'],
                    },
                    primaryProvider: '',
                    fallbackProviders: [],
                    memoryConfig: {
                      maxTokens: 4000,
                      retentionDays: 30,
                      enableLongTerm: false,
                    },
                  });
                }}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Bot className="h-4 w-4 mr-2" />
                Create Agent
              </Button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Agent List */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Your Agents ({agents.length})
                </CardTitle>
                <CardDescription>
                  Manage your AI agents and their configurations
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-96">
                  <div className="space-y-3">
                    {agents.map((agent) => (
                      <div
                        key={agent.id}
                        className={`p-4 rounded-lg border cursor-pointer transition-all ${
                          selectedAgent?.id === agent.id
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => {
                          setSelectedAgent(agent);
                          setCurrentAgent(agent);
                          setIsCreating(false);
                        }}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h3 className="font-medium text-gray-900">{agent.name}</h3>
                            <p className="text-sm text-gray-600 mt-1">
                              {agent.type.replace('_', ' ')}
                            </p>
                            <div className="flex items-center gap-2 mt-2">
                              <Badge
                                variant={agent.status === 'ACTIVE' ? 'default' : 'secondary'}
                                className="text-xs"
                              >
                                {agent.status}
                              </Badge>
                              <Badge variant="outline" className="text-xs">
                                {agent.primaryProvider}
                              </Badge>
                            </div>
                          </div>
                          <div className="text-right">
                            {agent.performanceMetrics && (
                              <div className="text-xs text-gray-500">
                                <div>{agent.performanceMetrics.totalExecutions} runs</div>
                                <div>{Math.round(agent.performanceMetrics.successRate * 100)}% success</div>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                    
                    {agents.length === 0 && (
                      <div className="text-center py-8 text-gray-500">
                        <Bot className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>No agents created yet</p>
                        <p className="text-sm">Create your first agent to get started</p>
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>

          {/* Agent Configuration */}
          <div className="lg:col-span-2">
            {(selectedAgent || isCreating) ? (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    {isCreating ? 'Create New Agent' : `Configure ${currentAgent.name}`}
                  </CardTitle>
                  <CardDescription>
                    Configure your agent's behavior, capabilities, and AI providers
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Tabs value={activeTab} onValueChange={setActiveTab}>
                    <TabsList className="grid w-full grid-cols-5">
                      <TabsTrigger value="overview">Overview</TabsTrigger>
                      <TabsTrigger value="config">Configuration</TabsTrigger>
                      <TabsTrigger value="providers">Providers</TabsTrigger>
                      <TabsTrigger value="memory">Memory</TabsTrigger>
                      <TabsTrigger value="test">Test</TabsTrigger>
                    </TabsList>

                    <TabsContent value="overview" className="space-y-6">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="name">Agent Name</Label>
                          <Input
                            id="name"
                            value={currentAgent.name}
                            onChange={(e) => setCurrentAgent(prev => ({ ...prev, name: e.target.value }))}
                            placeholder="Enter agent name"
                          />
                        </div>
                        <div>
                          <Label htmlFor="type">Agent Type</Label>
                          <Select
                            value={currentAgent.type}
                            onValueChange={(value) => setCurrentAgent(prev => ({ ...prev, type: value as any }))}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="BASIC">Basic Agent</SelectItem>
                              <SelectItem value="TOOL_DRIVEN">Tool-Driven Agent</SelectItem>
                              <SelectItem value="HYBRID">Hybrid Agent</SelectItem>
                              <SelectItem value="MULTI_TASK">Multi-Task Agent</SelectItem>
                              <SelectItem value="MULTI_PROVIDER">Multi-Provider Agent</SelectItem>
                              <SelectItem value="COLLABORATIVE">Collaborative Agent</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div>
                        <Label htmlFor="template">Agent Template</Label>
                        <Select
                          value={currentAgent.templateId}
                          onValueChange={handleTemplateSelect}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select a template" />
                          </SelectTrigger>
                          <SelectContent>
                            {templates.map((template) => (
                              <SelectItem key={template.id} value={template.id}>
                                {template.name} - {template.category}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label htmlFor="systemPrompt">System Prompt</Label>
                        <Textarea
                          id="systemPrompt"
                          value={currentAgent.config.systemPrompt}
                          onChange={(e) => setCurrentAgent(prev => ({
                            ...prev,
                            config: { ...prev.config, systemPrompt: e.target.value }
                          }))}
                          placeholder="Define your agent's role and behavior..."
                          rows={6}
                        />
                      </div>
                    </TabsContent>

                    <TabsContent value="config" className="space-y-6">
                      <div>
                        <Label>Temperature: {currentAgent.config.temperature}</Label>
                        <Slider
                          value={[currentAgent.config.temperature]}
                          onValueChange={([value]) => setCurrentAgent(prev => ({
                            ...prev,
                            config: { ...prev.config, temperature: value }
                          }))}
                          max={2}
                          min={0}
                          step={0.1}
                          className="mt-2"
                        />
                        <p className="text-sm text-gray-600 mt-1">
                          Controls randomness in responses (0 = deterministic, 2 = very creative)
                        </p>
                      </div>

                      <div>
                        <Label>Max Tokens: {currentAgent.config.maxTokens}</Label>
                        <Slider
                          value={[currentAgent.config.maxTokens]}
                          onValueChange={([value]) => setCurrentAgent(prev => ({
                            ...prev,
                            config: { ...prev.config, maxTokens: value }
                          }))}
                          max={4000}
                          min={100}
                          step={100}
                          className="mt-2"
                        />
                        <p className="text-sm text-gray-600 mt-1">
                          Maximum length of agent responses
                        </p>
                      </div>

                      <div>
                        <Label>Capabilities</Label>
                        <div className="flex flex-wrap gap-2 mt-2">
                          {['chat', 'vision', 'function-calling', 'code-generation', 'analysis'].map((capability) => (
                            <Badge
                              key={capability}
                              variant={currentAgent.config.capabilities.includes(capability) ? 'default' : 'outline'}
                              className="cursor-pointer"
                              onClick={() => {
                                const capabilities = currentAgent.config.capabilities.includes(capability)
                                  ? currentAgent.config.capabilities.filter(c => c !== capability)
                                  : [...currentAgent.config.capabilities, capability];
                                setCurrentAgent(prev => ({
                                  ...prev,
                                  config: { ...prev.config, capabilities }
                                }));
                              }}
                            >
                              {capability}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="providers" className="space-y-6">
                      <div>
                        <Label>Primary Provider</Label>
                        <Select
                          value={currentAgent.primaryProvider}
                          onValueChange={(value) => setCurrentAgent(prev => ({ ...prev, primaryProvider: value }))}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select primary provider" />
                          </SelectTrigger>
                          <SelectContent>
                            {providers.map((provider) => (
                              <SelectItem key={provider.id} value={provider.id}>
                                <div className="flex items-center gap-2">
                                  <div className={`w-2 h-2 rounded-full ${
                                    getProviderStatus(provider) === 'healthy' ? 'bg-green-500' :
                                    getProviderStatus(provider) === 'degraded' ? 'bg-yellow-500' : 'bg-red-500'
                                  }`} />
                                  {provider.name} ({provider.type})
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label>Provider Performance</Label>
                        <div className="space-y-3 mt-2">
                          {providers.map((provider) => (
                            <div key={provider.id} className="p-3 border rounded-lg">
                              <div className="flex items-center justify-between mb-2">
                                <div className="flex items-center gap-2">
                                  <div className={`w-2 h-2 rounded-full ${
                                    getProviderStatus(provider) === 'healthy' ? 'bg-green-500' :
                                    getProviderStatus(provider) === 'degraded' ? 'bg-yellow-500' : 'bg-red-500'
                                  }`} />
                                  <span className="font-medium">{provider.name}</span>
                                  <Badge variant="outline" className="text-xs">{provider.type}</Badge>
                                </div>
                                <div className="text-sm text-gray-600">
                                  {getProviderLatency(provider)}ms avg
                                </div>
                              </div>
                              <div className="flex items-center gap-4 text-sm">
                                <div>
                                  <span className="text-gray-600">Reliability: </span>
                                  <span className="font-medium">{getProviderReliability(provider)}%</span>
                                </div>
                                <div>
                                  <span className="text-gray-600">Models: </span>
                                  <span className="font-medium">{provider.models?.length || 0}</span>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="memory" className="space-y-6">
                      <div>
                        <Label>Memory Token Limit: {currentAgent.memoryConfig.maxTokens}</Label>
                        <Slider
                          value={[currentAgent.memoryConfig.maxTokens]}
                          onValueChange={([value]) => setCurrentAgent(prev => ({
                            ...prev,
                            memoryConfig: { ...prev.memoryConfig, maxTokens: value }
                          }))}
                          max={16000}
                          min={1000}
                          step={1000}
                          className="mt-2"
                        />
                      </div>

                      <div>
                        <Label>Retention Days: {currentAgent.memoryConfig.retentionDays}</Label>
                        <Slider
                          value={[currentAgent.memoryConfig.retentionDays]}
                          onValueChange={([value]) => setCurrentAgent(prev => ({
                            ...prev,
                            memoryConfig: { ...prev.memoryConfig, retentionDays: value }
                          }))}
                          max={365}
                          min={1}
                          step={1}
                          className="mt-2"
                        />
                      </div>

                      <div className="flex items-center space-x-2">
                        <Switch
                          id="longTerm"
                          checked={currentAgent.memoryConfig.enableLongTerm}
                          onCheckedChange={(checked) => setCurrentAgent(prev => ({
                            ...prev,
                            memoryConfig: { ...prev.memoryConfig, enableLongTerm: checked }
                          }))}
                        />
                        <Label htmlFor="longTerm">Enable Long-term Memory</Label>
                      </div>
                    </TabsContent>

                    <TabsContent value="test" className="space-y-6">
                      {selectedAgent && (
                        <>
                          <div>
                            <Label htmlFor="testMessage">Test Message</Label>
                            <Textarea
                              id="testMessage"
                              value={testMessage}
                              onChange={(e) => setTestMessage(e.target.value)}
                              placeholder="Enter a message to test your agent..."
                              rows={3}
                            />
                          </div>

                          <Button
                            onClick={handleTestAgent}
                            disabled={isTesting || !testMessage.trim()}
                            className="w-full"
                          >
                            {isTesting ? (
                              <>
                                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                Testing Agent...
                              </>
                            ) : (
                              <>
                                <Play className="h-4 w-4 mr-2" />
                                Test Agent
                              </>
                            )}
                          </Button>

                          {testResult && (
                            <div className="mt-4">
                              <Label>Test Result</Label>
                              <div className={`p-4 rounded-lg border mt-2 ${
                                testResult.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'
                              }`}>
                                {testResult.success ? (
                                  <div>
                                    <div className="flex items-center gap-2 mb-2">
                                      <CheckCircle className="h-4 w-4 text-green-600" />
                                      <span className="font-medium text-green-800">Success</span>
                                    </div>
                                    <p className="text-gray-800 mb-2">{testResult.data.output}</p>
                                    {testResult.data.metadata && (
                                      <div className="text-sm text-gray-600">
                                        <div>Provider: {testResult.data.metadata.provider}</div>
                                        <div>Model: {testResult.data.metadata.model}</div>
                                        <div>Tokens: {testResult.data.metadata.tokens?.total}</div>
                                        <div>Duration: {testResult.data.metadata.duration}ms</div>
                                      </div>
                                    )}
                                  </div>
                                ) : (
                                  <div>
                                    <div className="flex items-center gap-2 mb-2">
                                      <AlertCircle className="h-4 w-4 text-red-600" />
                                      <span className="font-medium text-red-800">Error</span>
                                    </div>
                                    <p className="text-red-800">{testResult.error}</p>
                                  </div>
                                )}
                              </div>
                            </div>
                          )}
                        </>
                      )}
                    </TabsContent>
                  </Tabs>

                  <Separator className="my-6" />

                  <div className="flex justify-end gap-3">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setIsCreating(false);
                        setSelectedAgent(null);
                      }}
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleSaveAgent}
                      disabled={isLoading || !currentAgent.name.trim()}
                    >
                      {isLoading ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <Save className="h-4 w-4 mr-2" />
                          {isCreating ? 'Create Agent' : 'Save Changes'}
                        </>
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="flex items-center justify-center h-96">
                  <div className="text-center">
                    <Bot className="h-16 w-16 mx-auto mb-4 text-gray-400" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No Agent Selected</h3>
                    <p className="text-gray-600 mb-4">Select an agent from the list or create a new one to get started</p>
                    <Button
                      onClick={() => {
                        setIsCreating(true);
                        setCurrentAgent({
                          name: '',
                          type: 'BASIC',
                          status: 'ACTIVE',
                          templateId: '',
                          config: {
                            temperature: 0.7,
                            maxTokens: 1000,
                            systemPrompt: '',
                            skills: [],
                            capabilities: ['chat'],
                          },
                          primaryProvider: '',
                          fallbackProviders: [],
                          memoryConfig: {
                            maxTokens: 4000,
                            retentionDays: 30,
                            enableLongTerm: false,
                          },
                        });
                      }}
                    >
                      <Bot className="h-4 w-4 mr-2" />
                      Create Your First Agent
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}