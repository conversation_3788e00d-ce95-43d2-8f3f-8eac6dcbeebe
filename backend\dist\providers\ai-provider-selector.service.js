"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AIProviderSelectorService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIProviderSelectorService = void 0;
const common_1 = require("@nestjs/common");
const ioredis_1 = require("@nestjs-modules/ioredis");
const ioredis_2 = require("ioredis");
const ai_provider_manager_service_1 = require("./ai-provider-manager.service");
const apix_gateway_1 = require("../websocket/apix.gateway");
let AIProviderSelectorService = AIProviderSelectorService_1 = class AIProviderSelectorService {
    constructor(providerManager, redis, apixGateway) {
        this.providerManager = providerManager;
        this.redis = redis;
        this.apixGateway = apixGateway;
        this.logger = new common_1.Logger(AIProviderSelectorService_1.name);
        this.PERFORMANCE_CACHE_TTL = 300;
        this.HEALTH_CHECK_INTERVAL = 60000;
        setInterval(() => this.performHealthChecks(), this.HEALTH_CHECK_INTERVAL);
    }
    async selectOptimalProvider(request) {
        try {
            const activeProviders = await this.providerManager.getActiveProviders(request.organizationId);
            if (activeProviders.length === 0) {
                throw new Error('No active providers available');
            }
            const capableProviders = activeProviders.filter(provider => this.hasRequiredCapabilities(provider, request.capabilities));
            if (capableProviders.length === 0) {
                throw new Error('No providers support the required capabilities');
            }
            const scoredProviders = await Promise.all(capableProviders.map(provider => this.scoreProvider(provider, request)));
            scoredProviders.sort((a, b) => b.score - a.score);
            const filteredProviders = this.applyPreferences(scoredProviders, request);
            if (filteredProviders.length === 0) {
                throw new Error('No providers match the selection criteria');
            }
            const selectedProvider = filteredProviders[0];
            const fallbackChain = filteredProviders.slice(1, 4).map(p => p.providerId);
            const provider = capableProviders.find(p => p.id === selectedProvider.providerId);
            const selectedModel = this.selectBestModel(provider, request.capabilities);
            const reasoning = this.generateSelectionReasoning(selectedProvider, request);
            this.logger.log(`Selected provider ${selectedProvider.providerId} for organization ${request.organizationId}`);
            return {
                providerId: selectedProvider.providerId,
                modelId: selectedModel.id,
                fallbackChain,
                reasoning,
            };
        }
        catch (error) {
            this.logger.error(`Provider selection failed: ${error.message}`, error.stack);
            throw error;
        }
    }
    async scoreProvider(provider, request) {
        const performanceData = await this.getProviderPerformance(provider.id);
        const latencyScore = this.calculateLatencyScore(performanceData.averageLatency, request.maxLatency);
        const costScore = this.calculateCostScore(performanceData.averageCost, request.maxCost);
        const reliabilityScore = performanceData.reliability || 0.95;
        const capabilityScore = this.calculateCapabilityScore(provider, request.capabilities);
        const score = (latencyScore * 0.25 +
            costScore * 0.25 +
            reliabilityScore * 0.3 +
            capabilityScore * 0.2);
        return {
            providerId: provider.id,
            score,
            latency: performanceData.averageLatency,
            cost: performanceData.averageCost,
            reliability: reliabilityScore,
            capabilities: this.getProviderCapabilities(provider),
        };
    }
    calculateLatencyScore(latency, maxLatency) {
        if (maxLatency && latency > maxLatency) {
            return 0;
        }
        return Math.max(0, Math.min(1, (2000 - latency) / 1900));
    }
    calculateCostScore(cost, maxCost) {
        if (maxCost && cost > maxCost) {
            return 0;
        }
        return Math.max(0, Math.min(1, (0.1 - cost) / 0.099));
    }
    calculateCapabilityScore(provider, requiredCapabilities) {
        const providerCapabilities = this.getProviderCapabilities(provider);
        const matchedCapabilities = requiredCapabilities.filter(cap => providerCapabilities.includes(cap));
        return matchedCapabilities.length / requiredCapabilities.length;
    }
    hasRequiredCapabilities(provider, requiredCapabilities) {
        const providerCapabilities = this.getProviderCapabilities(provider);
        return requiredCapabilities.every(cap => providerCapabilities.includes(cap));
    }
    getProviderCapabilities(provider) {
        const baseCapabilities = ['chat', 'completion'];
        switch (provider.type) {
            case 'OPENAI':
                return [...baseCapabilities, 'function-calling', 'vision', 'code-generation'];
            case 'CLAUDE':
                return [...baseCapabilities, 'function-calling', 'vision', 'analysis'];
            case 'GEMINI':
                return [...baseCapabilities, 'vision', 'multimodal', 'code-generation'];
            case 'MISTRAL':
                return [...baseCapabilities, 'function-calling', 'code-generation'];
            case 'GROQ':
                return [...baseCapabilities, 'fast-inference'];
            default:
                return baseCapabilities;
        }
    }
    selectBestModel(provider, capabilities) {
        if (!provider.models || provider.models.length === 0) {
            throw new Error(`No models available for provider ${provider.id}`);
        }
        const suitableModels = provider.models.filter(model => this.modelSupportsCapabilities(model, capabilities));
        if (suitableModels.length === 0) {
            return provider.models[0];
        }
        return suitableModels.reduce((best, current) => this.compareModels(best, current) > 0 ? best : current);
    }
    modelSupportsCapabilities(model, capabilities) {
        const modelCapabilities = model.capabilities || {};
        return capabilities.every(cap => modelCapabilities[cap] === true);
    }
    compareModels(modelA, modelB) {
        const scoreA = this.getModelScore(modelA);
        const scoreB = this.getModelScore(modelB);
        return scoreA - scoreB;
    }
    getModelScore(model) {
        const capabilityCount = Object.keys(model.capabilities || {}).length;
        const versionScore = model.version ? parseFloat(model.version) || 1 : 1;
        return capabilityCount * versionScore;
    }
    applyPreferences(scoredProviders, request) {
        let filtered = [...scoredProviders];
        if (request.excludeProviders && request.excludeProviders.length > 0) {
            filtered = filtered.filter(p => !request.excludeProviders.includes(p.providerId));
        }
        if (request.preferredProviders && request.preferredProviders.length > 0) {
            filtered = filtered.map(p => (Object.assign(Object.assign({}, p), { score: request.preferredProviders.includes(p.providerId) ? p.score * 1.2 : p.score })));
            filtered.sort((a, b) => b.score - a.score);
        }
        return filtered;
    }
    generateSelectionReasoning(provider, request) {
        const reasons = [];
        if (provider.score > 0.8) {
            reasons.push('High overall performance score');
        }
        if (provider.latency < 500) {
            reasons.push('Low latency response time');
        }
        if (provider.reliability > 0.95) {
            reasons.push('High reliability rating');
        }
        if (provider.cost < 0.01) {
            reasons.push('Cost-effective pricing');
        }
        if (request.preferredProviders && request.preferredProviders.includes(provider.providerId)) {
            reasons.push('Matches user preferences');
        }
        return reasons.length > 0 ? reasons.join(', ') : 'Best available option';
    }
    async getProviderPerformance(providerId) {
        const cacheKey = `provider:performance:${providerId}`;
        const cached = await this.redis.get(cacheKey);
        if (cached) {
            return JSON.parse(cached);
        }
        const defaultPerformance = {
            averageLatency: 1000,
            averageCost: 0.01,
            reliability: 0.95,
            lastUpdated: new Date(),
        };
        await this.redis.setex(cacheKey, this.PERFORMANCE_CACHE_TTL, JSON.stringify(defaultPerformance));
        return defaultPerformance;
    }
    async updateProviderPerformance(providerId, metrics) {
        try {
            const cacheKey = `provider:performance:${providerId}`;
            const existing = await this.getProviderPerformance(providerId);
            const alpha = 0.1;
            const newLatency = existing.averageLatency * (1 - alpha) + metrics.latency * alpha;
            const newCost = existing.averageCost * (1 - alpha) + metrics.cost * alpha;
            const newReliability = existing.reliability * (1 - alpha) + (metrics.success ? 1 : 0) * alpha;
            const updated = {
                averageLatency: newLatency,
                averageCost: newCost,
                reliability: newReliability,
                lastUpdated: new Date(),
            };
            await this.redis.setex(cacheKey, this.PERFORMANCE_CACHE_TTL, JSON.stringify(updated));
            this.logger.debug(`Updated performance metrics for provider ${providerId}`);
        }
        catch (error) {
            this.logger.error(`Failed to update provider performance: ${error.message}`, error.stack);
        }
    }
    async performHealthChecks() {
        try {
            this.logger.debug('Performing provider health checks');
        }
        catch (error) {
            this.logger.error(`Health check failed: ${error.message}`, error.stack);
        }
    }
    async getProviderRankings(organizationId) {
        const activeProviders = await this.providerManager.getActiveProviders(organizationId);
        const scoredProviders = await Promise.all(activeProviders.map(provider => this.scoreProvider(provider, {
            organizationId,
            capabilities: ['chat'],
        })));
        return scoredProviders.sort((a, b) => b.score - a.score);
    }
};
exports.AIProviderSelectorService = AIProviderSelectorService;
exports.AIProviderSelectorService = AIProviderSelectorService = AIProviderSelectorService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, ioredis_1.InjectRedis)()),
    __metadata("design:paramtypes", [ai_provider_manager_service_1.AIProviderManagerService,
        ioredis_2.default,
        apix_gateway_1.ApixGateway])
], AIProviderSelectorService);
//# sourceMappingURL=ai-provider-selector.service.js.map