import { Organization } from './organization.entity';
import { User } from './user.entity';
export declare enum SessionStatus {
    ACTIVE = "ACTIVE",
    COMPLETED = "COMPLETED",
    FAILED = "FAILED",
    TIMEOUT = "TIMEOUT"
}
export declare class Session {
    id: string;
    status: SessionStatus;
    messages: Array<{
        id: string;
        role: 'user' | 'assistant' | 'system';
        content: string;
        timestamp: Date;
        metadata?: Record<string, any>;
    }>;
    context: Record<string, any>;
    metadata: {
        agentId?: string;
        toolIds?: string[];
        workflowId?: string;
        totalTokens?: number;
        totalCost?: number;
    };
    expiresAt: Date;
    createdAt: Date;
    updatedAt: Date;
    organization: Organization;
    organizationId: string;
    user: User;
    userId: string;
}
