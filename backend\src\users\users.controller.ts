import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { UserService, CreateUserDto, UpdateUserDto, UserFilters } from './users.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { PermissionGuard } from '../auth/permission.guard';
import { RequirePermissions } from '../auth/permissions.decorator';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

@ApiTags('users')
@ApiBearerAuth()
@Controller('api/users')
@UseGuards(JwtAuthGuard)
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Post()
  @UseGuards(PermissionGuard)
  @RequirePermissions('users.write')
  @ApiOperation({ summary: 'Create a new user' })
  @ApiResponse({ status: 201, description: 'User created successfully' })
  @ApiResponse({ status: 409, description: 'User already exists' })
  async createUser(@Body() createUserDto: CreateUserDto, @Request() req) {
    return this.userService.createUser(createUserDto, req.user.userId);
  }

  @Get()
  @UseGuards(PermissionGuard)
  @RequirePermissions('users.read')
  @ApiOperation({ summary: 'Get users with filtering and pagination' })
  @ApiResponse({ status: 200, description: 'Users retrieved successfully' })
  async getUsers(@Query() filters: UserFilters, @Request() req) {
    // Ensure organization scoping
    filters.organizationId = req.user.organizationId;
    return this.userService.getUsers(filters);
  }

  @Get(':id')
  @UseGuards(PermissionGuard)
  @RequirePermissions('users.read')
  @ApiOperation({ summary: 'Get user by ID' })
  @ApiResponse({ status: 200, description: 'User retrieved successfully' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async getUserById(@Param('id') id: string) {
    return this.userService.getUserById(id);
  }

  @Put(':id')
  @UseGuards(PermissionGuard)
  @RequirePermissions('users.write')
  @ApiOperation({ summary: 'Update user' })
  @ApiResponse({ status: 200, description: 'User updated successfully' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async updateUser(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
    @Request() req,
  ) {
    return this.userService.updateUser(id, updateUserDto, req.user.userId);
  }

  @Delete(':id')
  @UseGuards(PermissionGuard)
  @RequirePermissions('users.write')
  @ApiOperation({ summary: 'Delete user' })
  @ApiResponse({ status: 200, description: 'User deleted successfully' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async deleteUser(@Param('id') id: string, @Request() req) {
    return this.userService.deleteUser(id, req.user.userId);
  }

  @Put('bulk-update')
  @UseGuards(PermissionGuard)
  @RequirePermissions('users.write')
  @ApiOperation({ summary: 'Bulk update users' })
  @ApiResponse({ status: 200, description: 'Users updated successfully' })
  async bulkUpdateUsers(
    @Body() body: { userIds: string[]; updateData: Partial<UpdateUserDto> },
    @Request() req,
  ) {
    return this.userService.bulkUpdateUsers(
      body.userIds,
      body.updateData,
      req.user.userId,
    );
  }

  @Put(':id/reset-password')
  @UseGuards(PermissionGuard)
  @RequirePermissions('users.write')
  @ApiOperation({ summary: 'Reset user password' })
  @ApiResponse({ status: 200, description: 'Password reset successfully' })
  async resetPassword(
    @Param('id') id: string,
    @Body() body: { newPassword: string },
    @Request() req,
  ) {
    return this.userService.resetPassword(id, body.newPassword, req.user.userId);
  }
}