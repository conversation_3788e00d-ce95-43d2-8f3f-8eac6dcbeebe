import { Module } from '@nestjs/common';
import { ApixGateway } from './apix.gateway';
import { JwtModule } from '@nestjs/jwt';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [
    PrismaModule,
    JwtModule.register({
      secret: process.env.JWT_SECRET || 'your-secret-key',
    }),
  ],
  providers: [ApixGateway],
  exports: [ApixGateway],
})
export class ApixModule {}