import Redis from 'ioredis';
export interface SessionMessage {
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: Date;
    metadata?: Record<string, any>;
}
export interface SessionMemory {
    id: string;
    agentId: string;
    organizationId: string;
    messages: SessionMessage[];
    context: Record<string, any>;
    metadata: {
        tokenCount: number;
        maxTokens: number;
        retentionDays: number;
        enableLongTerm: boolean;
    };
    createdAt: Date;
    updatedAt: Date;
}
export declare class SessionMemoryService {
    private readonly redis;
    private readonly logger;
    private readonly SESSION_PREFIX;
    private readonly DEFAULT_MAX_TOKENS;
    private readonly DEFAULT_RETENTION_DAYS;
    constructor(redis: Redis);
    createSession(agentId: string, organizationId: string, config?: {
        maxTokens?: number;
        retentionDays?: number;
        enableLongTerm?: boolean;
    }): Promise<SessionMemory>;
    getSession(sessionId: string, organizationId: string): Promise<SessionMemory | null>;
    addMessage(sessionId: string, message: SessionMessage): Promise<void>;
    updateContext(sessionId: string, organizationId: string, context: Record<string, any>): Promise<void>;
    getSessionHistory(sessionId: string, organizationId: string, limit?: number): Promise<SessionMessage[]>;
    deleteSession(sessionId: string, organizationId: string): Promise<void>;
    getSessionsByAgent(agentId: string, organizationId: string): Promise<SessionMemory[]>;
    private saveSession;
    private truncateSession;
    private estimateTokens;
    getSessionStats(organizationId: string): Promise<{
        totalSessions: number;
        activeSessions: number;
        totalMessages: number;
        averageSessionLength: number;
    }>;
}
