import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { ApixGateway } from '../websocket/apix.gateway';
import { OrganizationStatus } from '@prisma/client';

export interface CreateOrganizationDto {
  name: string;
  slug: string;
  domain?: string;
  settings?: any;
  quotas?: any;
  billing?: any;
  branding?: any;
}

export interface UpdateOrganizationDto {
  name?: string;
  slug?: string;
  domain?: string;
  settings?: any;
  quotas?: any;
  billing?: any;
  branding?: any;
  status?: OrganizationStatus;
  isActive?: boolean;
}

export interface OrganizationFilters {
  search?: string;
  status?: OrganizationStatus;
  isActive?: boolean;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

@Injectable()
export class OrganizationService {
  constructor(
    private prisma: PrismaService,
    private apixGateway: ApixGateway,
  ) {}

  async createOrganization(createOrganizationDto: CreateOrganizationDto, createdById: string) {
    const { name, slug, domain, settings = {}, quotas = {}, billing = {}, branding = {} } = createOrganizationDto;

    // Check if slug already exists
    const existingOrg = await this.prisma.organization.findUnique({
      where: { slug },
    });

    if (existingOrg) {
      throw new ConflictException('Organization slug already exists');
    }

    // Check if domain already exists (if provided)
    if (domain) {
      const existingDomain = await this.prisma.organization.findFirst({
        where: { domain },
      });

      if (existingDomain) {
        throw new ConflictException('Domain already in use');
      }
    }

    const defaultQuotas = {
      agents: 10,
      tools: 50,
      executions: 1000,
      storage: 1024 * 1024 * 100, // 100MB
      users: 5,
      ...quotas,
    };

    const defaultBilling = {
      plan: 'starter',
      status: 'active',
      usage: {},
      ...billing,
    };

    const result = await this.prisma.$transaction(async (tx) => {
      // Create organization
      const organization = await tx.organization.create({
        data: {
          name,
          slug,
          domain,
          settings,
          quotas: defaultQuotas,
          billing: defaultBilling,
          branding,
          status: OrganizationStatus.ACTIVE,
        },
      });

      // Create default roles for the organization
      const adminRole = await tx.role.create({
        data: {
          name: 'Admin',
          description: 'Full administrative access',
          isSystem: true,
          organizationId: organization.id,
        },
      });

      const memberRole = await tx.role.create({
        data: {
          name: 'Member',
          description: 'Standard member access',
          isSystem: true,
          organizationId: organization.id,
        },
      });

      const viewerRole = await tx.role.create({
        data: {
          name: 'Viewer',
          description: 'Read-only access',
          isSystem: true,
          organizationId: organization.id,
        },
      });

      // Create default permissions
      const permissions = [
        { name: 'users.read', description: 'View users', resource: 'users', action: 'read' },
        { name: 'users.write', description: 'Manage users', resource: 'users', action: 'write' },
        { name: 'roles.read', description: 'View roles', resource: 'roles', action: 'read' },
        { name: 'roles.write', description: 'Manage roles', resource: 'roles', action: 'write' },
        { name: 'permissions.read', description: 'View permissions', resource: 'permissions', action: 'read' },
        { name: 'permissions.write', description: 'Manage permissions', resource: 'permissions', action: 'write' },
        { name: 'agents.read', description: 'View agents', resource: 'agents', action: 'read' },
        { name: 'agents.write', description: 'Manage agents', resource: 'agents', action: 'write' },
        { name: 'tools.read', description: 'View tools', resource: 'tools', action: 'read' },
        { name: 'tools.write', description: 'Manage tools', resource: 'tools', action: 'write' },
        { name: 'workflows.read', description: 'View workflows', resource: 'workflows', action: 'read' },
        { name: 'workflows.write', description: 'Manage workflows', resource: 'workflows', action: 'write' },
        { name: 'organization.read', description: 'View organization', resource: 'organization', action: 'read' },
        { name: 'organization.write', description: 'Manage organization', resource: 'organization', action: 'write' },
      ];

      const createdPermissions = await Promise.all(
        permissions.map(permission =>
          tx.permission.create({
            data: {
              ...permission,
              isSystem: true,
              organizationId: organization.id,
            },
          })
        )
      );

      // Assign permissions to roles
      const adminPermissions = createdPermissions; // Admin gets all permissions
      const memberPermissions = createdPermissions.filter(p => 
        ['agents.read', 'agents.write', 'tools.read', 'tools.write', 'workflows.read', 'workflows.write'].includes(p.name)
      );
      const viewerPermissions = createdPermissions.filter(p => p.action === 'read');

      // Admin role permissions
      await tx.rolePermission.createMany({
        data: adminPermissions.map(permission => ({
          roleId: adminRole.id,
          permissionId: permission.id,
          organizationId: organization.id,
        })),
      });

      // Member role permissions
      await tx.rolePermission.createMany({
        data: memberPermissions.map(permission => ({
          roleId: memberRole.id,
          permissionId: permission.id,
          organizationId: organization.id,
        })),
      });

      // Viewer role permissions
      await tx.rolePermission.createMany({
        data: viewerPermissions.map(permission => ({
          roleId: viewerRole.id,
          permissionId: permission.id,
          organizationId: organization.id,
        })),
      });

      // Log audit event
      await tx.auditLog.create({
        data: {
          userId: createdById,
          organizationId: organization.id,
          action: 'CREATE',
          resource: 'organization',
          resourceId: organization.id,
          details: {
            action: 'organization_created',
            name: organization.name,
            slug: organization.slug,
            domain: organization.domain,
          },
        },
      });

      return organization;
    });

    // Publish APIX event
    await this.apixGateway.publishToOrganization(result.id, {
      type: 'organization.created',
      payload: {
        organizationId: result.id,
        name: result.name,
        slug: result.slug,
        createdBy: createdById,
      },
    });

    return result;
  }

  async updateOrganization(organizationId: string, updateOrganizationDto: UpdateOrganizationDto, updatedById: string) {
    const { name, slug, domain, settings, quotas, billing, branding, status, isActive } = updateOrganizationDto;

    const existingOrg = await this.prisma.organization.findUnique({
      where: { id: organizationId },
    });

    if (!existingOrg) {
      throw new NotFoundException('Organization not found');
    }

    // Check slug uniqueness if slug is being updated
    if (slug && slug !== existingOrg.slug) {
      const slugExists = await this.prisma.organization.findUnique({
        where: { slug },
      });

      if (slugExists) {
        throw new ConflictException('Slug already in use');
      }
    }

    // Check domain uniqueness if domain is being updated
    if (domain && domain !== existingOrg.domain) {
      const domainExists = await this.prisma.organization.findFirst({
        where: { domain },
      });

      if (domainExists) {
        throw new ConflictException('Domain already in use');
      }
    }

    const result = await this.prisma.$transaction(async (tx) => {
      // Update organization
      const organization = await tx.organization.update({
        where: { id: organizationId },
        data: {
          ...(name && { name }),
          ...(slug && { slug }),
          ...(domain !== undefined && { domain }),
          ...(settings && { settings }),
          ...(quotas && { quotas }),
          ...(billing && { billing }),
          ...(branding && { branding }),
          ...(status && { status }),
          ...(isActive !== undefined && { isActive }),
        },
      });

      // Log audit event
      await tx.auditLog.create({
        data: {
          userId: updatedById,
          organizationId,
          action: 'UPDATE',
          resource: 'organization',
          resourceId: organizationId,
          details: {
            action: 'organization_updated',
            changes: updateOrganizationDto,
          },
        },
      });

      return organization;
    });

    // Publish APIX event
    await this.apixGateway.publishToOrganization(organizationId, {
      type: 'organization.updated',
      payload: {
        organizationId,
        changes: updateOrganizationDto,
        updatedBy: updatedById,
      },
    });

    return result;
  }

  async deleteOrganization(organizationId: string, deletedById: string) {
    const organization = await this.prisma.organization.findUnique({
      where: { id: organizationId },
      include: {
        users: true,
        agents: true,
        tools: true,
        workflows: true,
      },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    // Check if organization has active resources
    if (organization.users.length > 0 || organization.agents.length > 0 || 
        organization.tools.length > 0 || organization.workflows.length > 0) {
      throw new BadRequestException('Cannot delete organization with active resources. Please remove all users, agents, tools, and workflows first.');
    }

    await this.prisma.$transaction(async (tx) => {
      // Update status to deleted instead of hard delete
      await tx.organization.update({
        where: { id: organizationId },
        data: {
          status: OrganizationStatus.DELETED,
          isActive: false,
        },
      });

      // Log audit event
      await tx.auditLog.create({
        data: {
          userId: deletedById,
          organizationId,
          action: 'DELETE',
          resource: 'organization',
          resourceId: organizationId,
          details: {
            action: 'organization_deleted',
            name: organization.name,
            slug: organization.slug,
          },
        },
      });
    });

    // Publish APIX event
    await this.apixGateway.publishToOrganization(organizationId, {
      type: 'organization.deleted',
      payload: {
        organizationId,
        name: organization.name,
        deletedBy: deletedById,
      },
    });

    return { message: 'Organization deleted successfully' };
  }

  async getOrganizations(filters: OrganizationFilters) {
    const {
      search,
      status,
      isActive,
      page = 1,
      limit = 20,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = filters;

    const skip = (page - 1) * limit;

    const where: any = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { slug: { contains: search, mode: 'insensitive' } },
        { domain: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (status) {
      where.status = status;
    }

    if (isActive !== undefined) {
      where.isActive = isActive;
    }

    const [organizations, total] = await Promise.all([
      this.prisma.organization.findMany({
        where,
        include: {
          users: {
            select: {
              id: true,
              name: true,
              email: true,
              systemRole: true,
              isActive: true,
            },
          },
          _count: {
            select: {
              users: true,
              agents: true,
              tools: true,
              workflows: true,
            },
          },
        },
        orderBy: { [sortBy]: sortOrder },
        skip,
        take: limit,
      }),
      this.prisma.organization.count({ where }),
    ]);

    return {
      organizations,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }

  async getOrganizationById(organizationId: string) {
    const organization = await this.prisma.organization.findUnique({
      where: { id: organizationId },
      include: {
        users: {
          select: {
            id: true,
            name: true,
            email: true,
            systemRole: true,
            isActive: true,
            lastLoginAt: true,
            createdAt: true,
          },
        },
        roles: {
          include: {
            _count: {
              select: {
                userRoles: true,
              },
            },
          },
        },
        permissions: {
          include: {
            _count: {
              select: {
                rolePermissions: true,
                userPermissions: true,
              },
            },
          },
        },
        _count: {
          select: {
            users: true,
            agents: true,
            tools: true,
            workflows: true,
            sessions: true,
          },
        },
      },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    return organization;
  }

  async getOrganizationStats(organizationId: string) {
    const organization = await this.prisma.organization.findUnique({
      where: { id: organizationId },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    const [
      userStats,
      agentStats,
      toolStats,
      workflowStats,
      sessionStats,
      recentActivity,
    ] = await Promise.all([
      this.prisma.user.groupBy({
        by: ['systemRole', 'isActive'],
        where: { organizationId },
        _count: true,
      }),
      this.prisma.agent.groupBy({
        by: ['status'],
        where: { organizationId },
        _count: true,
      }),
      this.prisma.tool.groupBy({
        by: ['status'],
        where: { organizationId },
        _count: true,
      }),
      this.prisma.workflow.groupBy({
        by: ['isActive'],
        where: { organizationId },
        _count: true,
      }),
      this.prisma.session.groupBy({
        by: ['status'],
        where: { organizationId },
        _count: true,
      }),
      this.prisma.auditLog.findMany({
        where: { organizationId },
        include: {
          user: {
            select: {
              name: true,
              email: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        take: 10,
      }),
    ]);

    return {
      users: userStats,
      agents: agentStats,
      tools: toolStats,
      workflows: workflowStats,
      sessions: sessionStats,
      recentActivity,
      quotas: organization.quotas,
      billing: organization.billing,
    };
  }

  async suspendOrganization(organizationId: string, suspendedById: string, reason?: string) {
    const organization = await this.prisma.organization.findUnique({
      where: { id: organizationId },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    if (organization.status === OrganizationStatus.SUSPENDED) {
      throw new BadRequestException('Organization is already suspended');
    }

    await this.prisma.$transaction(async (tx) => {
      await tx.organization.update({
        where: { id: organizationId },
        data: {
          status: OrganizationStatus.SUSPENDED,
          isActive: false,
        },
      });

      // Log audit event
      await tx.auditLog.create({
        data: {
          userId: suspendedById,
          organizationId,
          action: 'UPDATE',
          resource: 'organization',
          resourceId: organizationId,
          details: {
            action: 'organization_suspended',
            reason,
          },
        },
      });
    });

    // Publish APIX event
    await this.apixGateway.publishToOrganization(organizationId, {
      type: 'organization.suspended',
      payload: {
        organizationId,
        reason,
        suspendedBy: suspendedById,
      },
    });

    return { message: 'Organization suspended successfully' };
  }

  async activateOrganization(organizationId: string, activatedById: string) {
    const organization = await this.prisma.organization.findUnique({
      where: { id: organizationId },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    if (organization.status === OrganizationStatus.ACTIVE) {
      throw new BadRequestException('Organization is already active');
    }

    await this.prisma.$transaction(async (tx) => {
      await tx.organization.update({
        where: { id: organizationId },
        data: {
          status: OrganizationStatus.ACTIVE,
          isActive: true,
        },
      });

      // Log audit event
      await tx.auditLog.create({
        data: {
          userId: activatedById,
          organizationId,
          action: 'UPDATE',
          resource: 'organization',
          resourceId: organizationId,
          details: {
            action: 'organization_activated',
          },
        },
      });
    });

    // Publish APIX event
    await this.apixGateway.publishToOrganization(organizationId, {
      type: 'organization.activated',
      payload: {
        organizationId,
        activatedBy: activatedById,
      },
    });

    return { message: 'Organization activated successfully' };
  }
}