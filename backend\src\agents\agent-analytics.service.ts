import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AgentExecution } from '../database/entities/agent-execution.entity';
import { Agent } from '../database/entities/agent.entity';

export interface AgentAnalytics {
  agentId: string;
  totalExecutions: number;
  successRate: number;
  averageResponseTime: number;
  totalCost: number;
  providerDistribution: Record<string, number>;
  errorRate: number;
  lastExecuted: Date;
  performanceTrend: {
    period: string;
    executions: number;
    successRate: number;
    averageTime: number;
    cost: number;
  }[];
}

@Injectable()
export class AgentAnalyticsService {
  private readonly logger = new Logger(AgentAnalyticsService.name);

  constructor(
    @InjectRepository(AgentExecution)
    private executionRepository: Repository<AgentExecution>,
    @InjectRepository(Agent)
    private agentRepository: Repository<Agent>,
  ) {}

  async trackExecution(execution: AgentExecution): Promise<void> {
    try {
      // Analytics tracking is handled by the execution record itself
      // Additional analytics processing can be added here
      this.logger.debug(`Tracked execution: ${execution.id} for agent: ${execution.agentId}`);
    } catch (error) {
      this.logger.error(`Failed to track execution analytics: ${error.message}`);
    }
  }

  async getAgentAnalytics(agentId: string, organizationId: string): Promise<AgentAnalytics> {
    try {
      const executions = await this.executionRepository.find({
        where: { agentId, organizationId },
        order: { createdAt: 'DESC' },
      });

      const totalExecutions = executions.length;
      const successfulExecutions = executions.filter(e => e.status === 'COMPLETED').length;
      const successRate = totalExecutions > 0 ? successfulExecutions / totalExecutions : 0;

      const responseTimes = executions
        .filter(e => e.metadata?.duration)
        .map(e => e.metadata.duration);
      const averageResponseTime = responseTimes.length > 0 
        ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length 
        : 0;

      const totalCost = executions
        .filter(e => e.metadata?.cost)
        .reduce((sum, e) => sum + e.metadata.cost, 0);

      const providerDistribution = executions
        .filter(e => e.metadata?.provider)
        .reduce((acc, e) => {
          const provider = e.metadata.provider;
          acc[provider] = (acc[provider] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);

      const errorRate = totalExecutions > 0 
        ? (totalExecutions - successfulExecutions) / totalExecutions 
        : 0;

      const lastExecuted = executions.length > 0 ? executions[0].createdAt : new Date();

      // Generate performance trend (last 7 days)
      const performanceTrend = await this.generatePerformanceTrend(agentId, organizationId, 7);

      return {
        agentId,
        totalExecutions,
        successRate,
        averageResponseTime,
        totalCost,
        providerDistribution,
        errorRate,
        lastExecuted,
        performanceTrend,
      };
    } catch (error) {
      this.logger.error(`Failed to get agent analytics for ${agentId}: ${error.message}`);
      throw error;
    }
  }

  async getOrganizationAnalytics(organizationId: string): Promise<{
    totalAgents: number;
    totalExecutions: number;
    totalCost: number;
    averageSuccessRate: number;
    topPerformingAgents: Array<{
      agentId: string;
      name: string;
      executions: number;
      successRate: number;
    }>;
    providerUsage: Record<string, number>;
    dailyExecutions: Array<{
      date: string;
      executions: number;
      cost: number;
    }>;
  }> {
    try {
      const agents = await this.agentRepository.find({
        where: { organizationId },
      });

      const executions = await this.executionRepository.find({
        where: { organizationId },
        relations: ['agent'],
        order: { createdAt: 'DESC' },
      });

      const totalAgents = agents.length;
      const totalExecutions = executions.length;
      const totalCost = executions
        .filter(e => e.metadata?.cost)
        .reduce((sum, e) => sum + e.metadata.cost, 0);

      const successfulExecutions = executions.filter(e => e.status === 'COMPLETED').length;
      const averageSuccessRate = totalExecutions > 0 ? successfulExecutions / totalExecutions : 0;

      // Top performing agents
      const agentStats = agents.map(agent => {
        const agentExecutions = executions.filter(e => e.agentId === agent.id);
        const successful = agentExecutions.filter(e => e.status === 'COMPLETED').length;
        const successRate = agentExecutions.length > 0 ? successful / agentExecutions.length : 0;

        return {
          agentId: agent.id,
          name: agent.name,
          executions: agentExecutions.length,
          successRate,
        };
      });

      const topPerformingAgents = agentStats
        .sort((a, b) => b.executions - a.executions)
        .slice(0, 5);

      // Provider usage
      const providerUsage = executions
        .filter(e => e.metadata?.provider)
        .reduce((acc, e) => {
          const provider = e.metadata.provider;
          acc[provider] = (acc[provider] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);

      // Daily executions (last 7 days)
      const dailyExecutions = await this.getDailyExecutions(organizationId, 7);

      return {
        totalAgents,
        totalExecutions,
        totalCost,
        averageSuccessRate,
        topPerformingAgents,
        providerUsage,
        dailyExecutions,
      };
    } catch (error) {
      this.logger.error(`Failed to get organization analytics: ${error.message}`);
      throw error;
    }
  }

  private async generatePerformanceTrend(
    agentId: string,
    organizationId: string,
    days: number,
  ): Promise<Array<{
    period: string;
    executions: number;
    successRate: number;
    averageTime: number;
    cost: number;
  }>> {
    const trend = [];
    const now = new Date();

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      const startOfDay = new Date(date.setHours(0, 0, 0, 0));
      const endOfDay = new Date(date.setHours(23, 59, 59, 999));

      const dayExecutions = await this.executionRepository.find({
        where: {
          agentId,
          organizationId,
          createdAt: {
            gte: startOfDay,
            lte: endOfDay,
          } as any,
        },
      });

      const executions = dayExecutions.length;
      const successful = dayExecutions.filter(e => e.status === 'COMPLETED').length;
      const successRate = executions > 0 ? successful / executions : 0;

      const responseTimes = dayExecutions
        .filter(e => e.metadata?.duration)
        .map(e => e.metadata.duration);
      const averageTime = responseTimes.length > 0 
        ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length 
        : 0;

      const cost = dayExecutions
        .filter(e => e.metadata?.cost)
        .reduce((sum, e) => sum + e.metadata.cost, 0);

      trend.push({
        period: startOfDay.toISOString().split('T')[0],
        executions,
        successRate,
        averageTime,
        cost,
      });
    }

    return trend;
  }

  private async getDailyExecutions(
    organizationId: string,
    days: number,
  ): Promise<Array<{
    date: string;
    executions: number;
    cost: number;
  }>> {
    const dailyData = [];
    const now = new Date();

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      const startOfDay = new Date(date.setHours(0, 0, 0, 0));
      const endOfDay = new Date(date.setHours(23, 59, 59, 999));

      const dayExecutions = await this.executionRepository.find({
        where: {
          organizationId,
          createdAt: {
            gte: startOfDay,
            lte: endOfDay,
          } as any,
        },
      });

      const executions = dayExecutions.length;
      const cost = dayExecutions
        .filter(e => e.metadata?.cost)
        .reduce((sum, e) => sum + e.metadata.cost, 0);

      dailyData.push({
        date: startOfDay.toISOString().split('T')[0],
        executions,
        cost,
      });
    }

    return dailyData;
  }

  async getProviderPerformance(organizationId: string): Promise<Array<{
    provider: string;
    executions: number;
    successRate: number;
    averageResponseTime: number;
    totalCost: number;
    reliability: number;
  }>> {
    try {
      const executions = await this.executionRepository.find({
        where: { organizationId },
      });

      const providerStats = executions
        .filter(e => e.metadata?.provider)
        .reduce((acc, execution) => {
          const provider = execution.metadata.provider;
          
          if (!acc[provider]) {
            acc[provider] = {
              executions: 0,
              successful: 0,
              totalTime: 0,
              totalCost: 0,
              timeCount: 0,
            };
          }

          acc[provider].executions++;
          if (execution.status === 'COMPLETED') {
            acc[provider].successful++;
          }
          if (execution.metadata.duration) {
            acc[provider].totalTime += execution.metadata.duration;
            acc[provider].timeCount++;
          }
          if (execution.metadata.cost) {
            acc[provider].totalCost += execution.metadata.cost;
          }

          return acc;
        }, {} as Record<string, any>);

      return Object.entries(providerStats).map(([provider, stats]) => ({
        provider,
        executions: stats.executions,
        successRate: stats.executions > 0 ? stats.successful / stats.executions : 0,
        averageResponseTime: stats.timeCount > 0 ? stats.totalTime / stats.timeCount : 0,
        totalCost: stats.totalCost,
        reliability: stats.executions > 0 ? stats.successful / stats.executions : 0,
      }));
    } catch (error) {
      this.logger.error(`Failed to get provider performance: ${error.message}`);
      return [];
    }
  }
}