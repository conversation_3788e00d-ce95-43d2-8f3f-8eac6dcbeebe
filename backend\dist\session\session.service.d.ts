import { PrismaService } from '../prisma/prisma.service';
import { ApixGateway } from '../websocket/apix.gateway';
export interface CreateSessionDto {
    userId: string;
    organizationId: string;
    metadata?: Record<string, any>;
}
export interface AddMessageDto {
    sessionId: string;
    role: 'user' | 'assistant' | 'system';
    content: string;
    metadata?: Record<string, any>;
}
export interface UpdateContextDto {
    sessionId: string;
    context: Record<string, any>;
}
export declare class SessionService {
    private prisma;
    private apixGateway;
    private redis;
    constructor(prisma: PrismaService, apixGateway: ApixGateway);
    createSession(createSessionDto: CreateSessionDto): Promise<any>;
    getSession(sessionId: string, organizationId: string): Promise<any>;
    addMessage(addMessageDto: AddMessageDto, organizationId: string): Promise<any>;
    updateContext(updateContextDto: UpdateContextDto, organizationId: string): Promise<any>;
    getUserSessions(userId: string, organizationId: string, limit?: number): Promise<any>;
    completeSession(sessionId: string, organizationId: string): Promise<any>;
    shareSessionContext(sessionId: string, organizationId: string, targetModule: string): Promise<{
        sessionId: any;
        userId: any;
        organizationId: any;
        context: any;
        recentMessages: any;
        metadata: any;
    }>;
    getSharedContext(sessionId: string, module: string, organizationId: string): Promise<any>;
    private applyMemoryLimits;
    cleanupExpiredSessions(): Promise<void>;
}
