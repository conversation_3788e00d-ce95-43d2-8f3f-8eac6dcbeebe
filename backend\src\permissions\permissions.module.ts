import { Module } from '@nestjs/common';
import { PermissionService } from './permissions.service';
import { PermissionController } from './permissions.controller';
import { PrismaModule } from '../prisma/prisma.module';
import { ApixModule } from '../websocket/apix.module';

@Module({
  imports: [PrismaModule, ApixModule],
  providers: [PermissionService],
  controllers: [PermissionController],
  exports: [PermissionService],
})
export class PermissionModule {}