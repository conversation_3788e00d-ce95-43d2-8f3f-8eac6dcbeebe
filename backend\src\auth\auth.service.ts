import { Injectable, UnauthorizedException, ConflictException, ForbiddenException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { PrismaService } from '../prisma/prisma.service';
import * as bcrypt from 'bcryptjs';
import Redis from 'ioredis';
import { UserRole } from '@prisma/client';

export interface JwtPayload {
  userId: string;
  organizationId: string;
  role: UserRole;
  permissions: string[];
}

export interface LoginDto {
  email: string;
  password: string;
}

export interface RegisterDto {
  email: string;
  password: string;
  name: string;
  organizationName: string;
}

@Injectable()
export class AuthService {
  private redis: Redis;

  constructor(
    private prisma: PrismaService,
    private jwtService: JwtService,
  ) {
    this.redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT) || 6379,
      password: process.env.REDIS_PASSWORD,
    });
  }

  async register(registerDto: RegisterDto) {
    const { email, password, name, organizationName } = registerDto;

    // Check if user already exists
    const existingUser = await this.prisma.user.findUnique({ where: { email } });
    if (existingUser) {
      throw new ConflictException('User with this email already exists');
    }

    // Hash password
    const passwordHash = await bcrypt.hash(password, 12);

    // Create organization and user in transaction
    const result = await this.prisma.$transaction(async (tx) => {
      // Create organization
      const organization = await tx.organization.create({
        data: {
          name: organizationName,
          slug: organizationName.toLowerCase().replace(/[^a-z0-9]/g, '-'),
          quotas: {
            agents: 10,
            tools: 50,
            executions: 1000,
            storage: 1024 * 1024 * 100, // 100MB
          },
          billing: {
            plan: 'starter',
            status: 'active',
            usage: {},
          },
        },
      });

      // Create user as org admin
      const user = await tx.user.create({
        data: {
          email,
          name,
          passwordHash,
          role: UserRole.ORG_ADMIN,
          permissions: this.getDefaultPermissions(UserRole.ORG_ADMIN),
          organizationId: organization.id,
        },
        include: {
          organization: true,
        },
      });

      return { user, organization };
    });

    // Generate tokens
    const tokens = await this.generateTokens(result.user);

    // Store refresh token in Redis
    await this.redis.setex(
      `refresh_token:${result.user.id}`,
      7 * 24 * 60 * 60, // 7 days
      tokens.refreshToken
    );

    // Log audit event
    await this.logAuditEvent(result.user.id, result.organization.id, 'CREATE', 'user', result.user.id, {
      action: 'user_registered',
      email: result.user.email,
    });

    return {
      user: this.sanitizeUser(result.user),
      organization: result.organization,
      ...tokens,
    };
  }

  async login(loginDto: LoginDto) {
    const { email, password } = loginDto;

    // Find user with organization
    const user = await this.prisma.user.findUnique({
      where: { email, isActive: true },
      include: { organization: true },
    });

    if (!user || !user.passwordHash) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Update last login
    await this.prisma.user.update({
      where: { id: user.id },
      data: { lastLoginAt: new Date() },
    });

    // Generate tokens
    const tokens = await this.generateTokens(user);

    // Store refresh token in Redis
    await this.redis.setex(
      `refresh_token:${user.id}`,
      7 * 24 * 60 * 60, // 7 days
      tokens.refreshToken
    );

    // Log audit event
    await this.logAuditEvent(user.id, user.organizationId, 'LOGIN', 'user', user.id, {
      action: 'user_login',
      email: user.email,
    });

    return {
      user: this.sanitizeUser(user),
      organization: user.organization,
      ...tokens,
    };
  }

  async refreshToken(refreshToken: string) {
    try {
      const payload = this.jwtService.verify(refreshToken);
      const { userId } = payload;

      // Check if refresh token exists in Redis
      const storedToken = await this.redis.get(`refresh_token:${userId}`);
      if (storedToken !== refreshToken) {
        throw new UnauthorizedException('Invalid refresh token');
      }

      // Get user
      const user = await this.prisma.user.findUnique({
        where: { id: userId, isActive: true },
        include: { organization: true },
      });

      if (!user) {
        throw new UnauthorizedException('User not found');
      }

      // Generate new tokens
      const tokens = await this.generateTokens(user);

      // Update refresh token in Redis
      await this.redis.setex(
        `refresh_token:${user.id}`,
        7 * 24 * 60 * 60, // 7 days
        tokens.refreshToken
      );

      return {
        user: this.sanitizeUser(user),
        organization: user.organization,
        ...tokens,
      };
    } catch (error) {
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  async logout(userId: string) {
    // Remove refresh token from Redis
    await this.redis.del(`refresh_token:${userId}`);
    
    // Log audit event
    const user = await this.prisma.user.findUnique({ where: { id: userId } });
    if (user) {
      await this.logAuditEvent(userId, user.organizationId, 'LOGOUT', 'user', userId, {
        action: 'user_logout',
      });
    }

    return { message: 'Logged out successfully' };
  }

  async validateUser(payload: JwtPayload) {
    const user = await this.prisma.user.findUnique({
      where: { id: payload.userId, isActive: true },
      include: { organization: true },
    });

    if (!user || user.organizationId !== payload.organizationId) {
      throw new UnauthorizedException('Invalid token');
    }

    return user;
  }

  // Permission checking methods
  hasPermission(user: any, permission: string): boolean {
    return user.permissions.includes(permission) || user.role === UserRole.SUPER_ADMIN;
  }

  hasAnyPermission(user: any, permissions: string[]): boolean {
    return permissions.some(permission => this.hasPermission(user, permission));
  }

  hasAllPermissions(user: any, permissions: string[]): boolean {
    return permissions.every(permission => this.hasPermission(user, permission));
  }

  // Organization scoping
  async enforceOrganizationAccess(userId: string, organizationId: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user || user.organizationId !== organizationId) {
      throw new ForbiddenException('Access denied to organization resources');
    }

    return user;
  }

  // API Key management
  async createApiKey(userId: string, name: string, permissions: string[] = []) {
    const user = await this.prisma.user.findUnique({ where: { id: userId } });
    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    const key = `sk_${Math.random().toString(36).substr(2, 32)}`;
    
    const apiKey = await this.prisma.apiKey.create({
      data: {
        name,
        key: await bcrypt.hash(key, 10),
        permissions,
        organizationId: user.organizationId,
      },
    });

    // Log audit event
    await this.logAuditEvent(userId, user.organizationId, 'CREATE', 'api_key', apiKey.id, {
      action: 'api_key_created',
      name,
      permissions,
    });

    return { ...apiKey, key }; // Return plain key only once
  }

  async validateApiKey(key: string) {
    const apiKeys = await this.prisma.apiKey.findMany({
      where: { isActive: true },
      include: { organization: true },
    });

    for (const apiKey of apiKeys) {
      const isValid = await bcrypt.compare(key, apiKey.key);
      if (isValid) {
        // Update last used
        await this.prisma.apiKey.update({
          where: { id: apiKey.id },
          data: { lastUsedAt: new Date() },
        });

        return apiKey;
      }
    }

    throw new UnauthorizedException('Invalid API key');
  }

  private async generateTokens(user: any) {
    const payload: JwtPayload = {
      userId: user.id,
      organizationId: user.organizationId,
      role: user.role,
      permissions: user.permissions,
    };

    const accessToken = this.jwtService.sign(payload, {
      expiresIn: '15m',
    });

    const refreshToken = this.jwtService.sign(
      { userId: user.id },
      { expiresIn: '7d' }
    );

    return {
      accessToken,
      refreshToken,
      expiresIn: 15 * 60, // 15 minutes in seconds
    };
  }

  private sanitizeUser(user: any) {
    const { passwordHash, ...sanitized } = user;
    return sanitized;
  }

  private getDefaultPermissions(role: UserRole): string[] {
    const permissions = {
      [UserRole.SUPER_ADMIN]: [
        'system:read',
        'system:write',
        'org:read',
        'org:write',
        'user:read',
        'user:write',
        'agent:read',
        'agent:write',
        'tool:read',
        'tool:write',
        'session:read',
        'session:write',
        'billing:read',
        'billing:write',
      ],
      [UserRole.ORG_ADMIN]: [
        'org:read',
        'org:write',
        'user:read',
        'user:write',
        'agent:read',
        'agent:write',
        'tool:read',
        'tool:write',
        'session:read',
        'session:write',
        'billing:read',
      ],
      [UserRole.DEVELOPER]: [
        'agent:read',
        'agent:write',
        'tool:read',
        'tool:write',
        'session:read',
        'session:write',
      ],
      [UserRole.VIEWER]: [
        'agent:read',
        'tool:read',
        'session:read',
      ],
    };

    return permissions[role] || [];
  }

  private async logAuditEvent(
    userId: string,
    organizationId: string,
    action: any,
    resource: string,
    resourceId: string,
    details: any
  ) {
    await this.prisma.auditLog.create({
      data: {
        userId,
        organizationId,
        action,
        resource,
        resourceId,
        details,
      },
    });
  }
}