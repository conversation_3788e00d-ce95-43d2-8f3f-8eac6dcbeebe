import { User } from '../../users/user.entity';
import { Organization } from '../../organizations/organization.entity';
import { AgentExecution } from './agent-execution.entity';
export declare enum AgentType {
    BASIC = "BASIC",
    TOOL_DRIVEN = "TOOL_DRIVEN",
    HYBRID = "HYBRID",
    MULTI_TASK = "MULTI_TASK",
    MULTI_PROVIDER = "MULTI_PROVIDER",
    COLLABORATIVE = "COLLABORATIVE"
}
export declare enum AgentStatus {
    ACTIVE = "ACTIVE",
    INACTIVE = "INACTIVE",
    ERROR = "ERROR",
    PAUSED = "PAUSED"
}
export declare enum ProviderType {
    OPENAI = "OPENAI",
    CLAUDE = "CLAUDE",
    GEMINI = "GEMINI",
    MISTRAL = "MISTRAL",
    GROQ = "GROQ"
}
export declare class AgentTemplate {
    id: string;
    name: string;
    category: string;
    description: string;
    config: Record<string, any>;
    skills: string[];
    isPublic: boolean;
    promptTemplate: string;
    type: AgentType;
    supportedProviders: ProviderType[];
    metadata: Record<string, any>;
    createdBy: string;
    creator: User;
    organizationId: string;
    organization: Organization;
    instances: Agent[];
    createdAt: Date;
    updatedAt: Date;
}
export declare class Agent {
    id: string;
    name: string;
    templateId: string;
    template: AgentTemplate;
    config: Record<string, any>;
    type: AgentType;
    status: AgentStatus;
    primaryProvider: ProviderType;
    fallbackProviders: ProviderType[];
    memoryConfig: {
        maxTokens: number;
        retentionDays: number;
        enableLongTerm: boolean;
    };
    skills: string[];
    performanceMetrics: {
        totalExecutions: number;
        successRate: number;
        averageResponseTime: number;
        lastExecuted: Date;
    };
    organizationId: string;
    organization: Organization;
    createdBy: string;
    creator: User;
    executions: AgentExecution[];
    createdAt: Date;
    updatedAt: Date;
}
export declare class AgentCollaboration {
    id: string;
    name: string;
    agentIds: string[];
    coordinatorId: string;
    workflow: Record<string, any>;
    sharedContext: Record<string, any>;
    status: string;
    organizationId: string;
    organization: Organization;
    createdBy: string;
    creator: User;
    createdAt: Date;
    updatedAt: Date;
}
