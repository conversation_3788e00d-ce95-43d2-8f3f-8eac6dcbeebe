import { PermissionService, CreatePermissionDto, UpdatePermissionDto, PermissionFilters } from './permissions.service';
export declare class PermissionController {
    private readonly permissionService;
    constructor(permissionService: PermissionService);
    createPermission(createPermissionDto: CreatePermissionDto, req: any): Promise<any>;
    getPermissions(filters: PermissionFilters, req: any): Promise<{
        permissions: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            pages: number;
        };
    }>;
    getResourceList(req: any): Promise<any>;
    getActionList(resource: string, req: any): Promise<any>;
    getPermissionById(id: string): Promise<any>;
    updatePermission(id: string, updatePermissionDto: UpdatePermissionDto, req: any): Promise<any>;
    deletePermission(id: string, req: any): Promise<{
        message: string;
    }>;
    assignPermissionToUser(permissionId: string, userId: string, req: any): Promise<{
        message: string;
    }>;
    removePermissionFromUser(permissionId: string, userId: string, req: any): Promise<{
        message: string;
    }>;
}
