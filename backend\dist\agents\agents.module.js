"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const agent_entity_1 = require("../database/entities/agent.entity");
const agent_execution_entity_1 = require("../database/entities/agent-execution.entity");
const agents_controller_1 = require("./agents.controller");
const agent_orchestrator_service_1 = require("./agent-orchestrator.service");
const agent_analytics_service_1 = require("./agent-analytics.service");
const agent_templates_service_1 = require("./agent-templates.service");
const agent_templates_controller_1 = require("./agent-templates.controller");
const prompt_templates_service_1 = require("./prompt-templates.service");
const prompt_templates_controller_1 = require("./prompt-templates.controller");
const session_memory_service_1 = require("./session-memory.service");
const agent_collaboration_service_1 = require("./agent-collaboration.service");
const apix_module_1 = require("../websocket/apix.module");
const ai_provider_module_1 = require("../providers/ai-provider.module");
let AgentsModule = class AgentsModule {
};
exports.AgentsModule = AgentsModule;
exports.AgentsModule = AgentsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                agent_entity_1.Agent,
                agent_entity_1.AgentTemplate,
                agent_entity_1.AgentCollaboration,
                agent_execution_entity_1.AgentExecution,
            ]),
            apix_module_1.ApixModule,
            ai_provider_module_1.AIProviderModule,
        ],
        controllers: [
            agents_controller_1.AgentsController,
            agent_templates_controller_1.AgentTemplatesController,
            prompt_templates_controller_1.PromptTemplatesController,
        ],
        providers: [
            agent_orchestrator_service_1.AgentOrchestratorService,
            agent_analytics_service_1.AgentAnalyticsService,
            agent_templates_service_1.AgentTemplatesService,
            prompt_templates_service_1.PromptTemplatesService,
            session_memory_service_1.SessionMemoryService,
            agent_collaboration_service_1.AgentCollaborationService,
        ],
        exports: [
            agent_orchestrator_service_1.AgentOrchestratorService,
            agent_analytics_service_1.AgentAnalyticsService,
            session_memory_service_1.SessionMemoryService,
            agent_collaboration_service_1.AgentCollaborationService,
        ],
    })
], AgentsModule);
//# sourceMappingURL=agents.module.js.map