'use client'

import { useState, useEffect } from 'react'
import { motion, useScroll, useTransform } from 'framer-motion'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Brain, 
  Zap, 
  Shield, 
  Globe, 
  ArrowRight, 
  Check, 
  Star,
  Menu,
  X,
  Github,
  Twitter,
  Linkedin,
  TrendingUp,
  Users,
  Building2,
  Workflow,
  Bot,
  Database,
  BarChart3,
  Sparkles,
  Target,
  Clock,
  DollarSign,
  Award,
  ChevronRight,
  Play,
  CheckCircle,
  XCircle,
  Minus
} from 'lucide-react'
import Link from 'next/link'
import { ThemeToggle } from '@/components/theme-switcher'

const features = [
  {
    icon: Brain,
    title: 'AI Agent Builder',
    description: 'Create intelligent agents with drag-and-drop simplicity. No coding required.',
    stats: '10x faster deployment'
  },
  {
    icon: Zap,
    title: 'Tool Manager',
    description: 'Connect any API or service as a tool. 500+ pre-built integrations available.',
    stats: '99.9% uptime'
  },
  {
    icon: Workflow,
    title: 'Hybrid Workflows',
    description: 'Combine agents and tools in sophisticated automation workflows.',
    stats: '85% cost reduction'
  },
  {
    icon: Shield,
    title: 'Enterprise Security',
    description: 'SOC 2 Type II compliant with multi-tenant isolation and RBAC.',
    stats: 'Bank-grade security'
  },
  {
    icon: Database,
    title: 'Knowledge Base',
    description: 'RAG-powered knowledge integration with semantic search.',
    stats: '95% accuracy'
  },
  {
    icon: BarChart3,
    title: 'Real-time Analytics',
    description: 'Monitor performance, costs, and ROI with live dashboards.',
    stats: 'Real-time insights'
  },
]

const competitors = [
  {
    name: 'SynapseAI',
    logo: Brain,
    pricing: '$49/mo',
    features: {
      'Visual Workflow Builder': true,
      'AI Agent Creation': true,
      'Multi-Provider Routing': true,
      'Real-time Collaboration': true,
      'Enterprise Security': true,
      'Custom Integrations': true,
      'Advanced Analytics': true,
      'White-label Options': true,
      'HITL Workflows': true,
      'Knowledge Base RAG': true,
    },
    highlight: true
  },
  {
    name: 'Zapier',
    logo: Zap,
    pricing: '$99/mo',
    features: {
      'Visual Workflow Builder': true,
      'AI Agent Creation': false,
      'Multi-Provider Routing': false,
      'Real-time Collaboration': false,
      'Enterprise Security': true,
      'Custom Integrations': true,
      'Advanced Analytics': false,
      'White-label Options': false,
      'HITL Workflows': false,
      'Knowledge Base RAG': false,
    }
  },
  {
    name: 'Microsoft Power Automate',
    logo: Building2,
    pricing: '$150/mo',
    features: {
      'Visual Workflow Builder': true,
      'AI Agent Creation': false,
      'Multi-Provider Routing': false,
      'Real-time Collaboration': true,
      'Enterprise Security': true,
      'Custom Integrations': true,
      'Advanced Analytics': true,
      'White-label Options': false,
      'HITL Workflows': false,
      'Knowledge Base RAG': false,
    }
  },
  {
    name: 'n8n.io',
    logo: Bot,
    pricing: '$50/mo',
    features: {
      'Visual Workflow Builder': true,
      'AI Agent Creation': false,
      'Multi-Provider Routing': false,
      'Real-time Collaboration': false,
      'Enterprise Security': false,
      'Custom Integrations': true,
      'Advanced Analytics': false,
      'White-label Options': false,
      'HITL Workflows': false,
      'Knowledge Base RAG': false,
    }
  }
]

const pricing = [
  {
    name: 'Starter',
    price: '$49',
    period: '/month',
    description: 'Perfect for small teams getting started with AI automation',
    features: [
      '5 AI Agents',
      '25 Tools & Integrations',
      '1,000 Workflow Executions/month',
      '10GB Knowledge Base Storage',
      'Basic Analytics Dashboard',
      'Email Support',
      'Community Access',
      'Standard Security'
    ],
    popular: false,
    savings: null
  },
  {
    name: 'Professional',
    price: '$199',
    period: '/month',
    description: 'For growing businesses scaling AI operations',
    features: [
      '50 AI Agents',
      'Unlimited Tools & Integrations',
      '25,000 Workflow Executions/month',
      '100GB Knowledge Base Storage',
      'Advanced Analytics & Reporting',
      'Priority Support (24/7)',
      'Custom Integrations',
      'Advanced Security & Compliance',
      'Multi-tenant Management',
      'API Access & SDKs'
    ],
    popular: true,
    savings: 'Save $600/year'
  },
  {
    name: 'Enterprise',
    price: 'Custom',
    period: '',
    description: 'For large organizations with specific requirements',
    features: [
      'Unlimited Everything',
      'On-premise Deployment Options',
      'Custom AI Model Integration',
      'Dedicated Account Manager',
      'Custom SLA & Support',
      'Advanced Security Auditing',
      'White-label Solutions',
      'Custom Training & Onboarding',
      'Data Residency Options',
      'Enterprise SSO Integration'
    ],
    popular: false,
    savings: 'Contact for pricing'
  },
]

const testimonials = [
  {
    name: 'Sarah Chen',
    role: 'CTO at TechFlow Inc.',
    company: 'TechFlow',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=sarah',
    content: 'SynapseAI completely transformed our AI operations. We reduced deployment time by 90% and cut operational costs by $50K annually. The visual workflow builder is intuitive enough for our non-technical team members.',
    rating: 5,
    metrics: '90% faster deployment, $50K saved annually'
  },
  {
    name: 'Marcus Rodriguez',
    role: 'AI Engineering Lead at DataCorp',
    company: 'DataCorp',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=marcus',
    content: 'The multi-provider routing and intelligent fallback systems saved us thousands in API costs while improving reliability to 99.9%. Best investment we made this year.',
    rating: 5,
    metrics: '99.9% uptime, 60% cost reduction'
  },
  {
    name: 'Emily Watson',
    role: 'Head of Operations at InnovateLab',
    company: 'InnovateLab',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=emily',
    content: 'Finally, a platform that makes AI accessible to business users. Our productivity increased by 300% and we automated 80% of our repetitive tasks within the first month.',
    rating: 5,
    metrics: '300% productivity increase, 80% automation'
  },
  {
    name: 'David Kim',
    role: 'VP of Engineering at ScaleUp',
    company: 'ScaleUp',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=david',
    content: 'The enterprise security and compliance features gave us confidence to deploy AI across our entire organization. RBAC and audit trails are exactly what we needed.',
    rating: 5,
    metrics: 'SOC 2 compliant, enterprise-ready'
  },
  {
    name: 'Lisa Park',
    role: 'Chief Innovation Officer at FutureCorps',
    company: 'FutureCorps',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=lisa',
    content: 'SynapseAI enabled us to build and deploy AI solutions 10x faster than traditional development. The ROI was evident within weeks, not months.',
    rating: 5,
    metrics: '10x faster development, immediate ROI'
  },
  {
    name: 'James Wilson',
    role: 'Director of Automation at MegaCorp',
    company: 'MegaCorp',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=james',
    content: 'The knowledge base integration with RAG capabilities revolutionized how our agents access and use company information. Accuracy improved by 95%.',
    rating: 5,
    metrics: '95% accuracy improvement'
  }
]

const stats = [
  { label: 'Active Organizations', value: '10,000+', icon: Building2 },
  { label: 'AI Agents Deployed', value: '500K+', icon: Bot },
  { label: 'Workflows Automated', value: '2M+', icon: Workflow },
  { label: 'API Calls Processed', value: '1B+', icon: Zap },
]

export default function LandingPage() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const { scrollYProgress } = useScroll()
  const y = useTransform(scrollYProgress, [0, 1], ['0%', '50%'])
  const opacity = useTransform(scrollYProgress, [0, 0.5], [1, 0])

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="sticky top-0 z-50 w-full border-b bg-background/80 backdrop-blur-md">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            {/* Logo */}
            <div className="flex items-center space-x-2">
              <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-r from-indigo-600 to-purple-600">
                <Brain className="h-5 w-5 text-white" />
              </div>
              <span className="text-xl font-bold">SynapseAI</span>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center space-x-8">
              <Link href="#features" className="text-sm font-medium hover:text-primary transition-colors">
                Features
              </Link>
              <Link href="#comparison" className="text-sm font-medium hover:text-primary transition-colors">
                vs Competitors
              </Link>
              <Link href="#pricing" className="text-sm font-medium hover:text-primary transition-colors">
                Pricing
              </Link>
              <Link href="#testimonials" className="text-sm font-medium hover:text-primary transition-colors">
                Testimonials
              </Link>
              <Link href="/docs" className="text-sm font-medium hover:text-primary transition-colors">
                Docs
              </Link>
            </nav>

            {/* Right side */}
            <div className="flex items-center space-x-4">
              <ThemeToggle />
              <div className="hidden md:flex items-center space-x-2">
                <Button variant="ghost" asChild>
                  <Link href="/auth/signin">Sign In</Link>
                </Button>
                <Button asChild className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700">
                  <Link href="/auth/signup">Start Free Trial</Link>
                </Button>
              </div>
              
              {/* Mobile menu button */}
              <Button
                variant="ghost"
                size="sm"
                className="md:hidden"
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              >
                {mobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
              </Button>
            </div>
          </div>
        </div>

        {/* Mobile Navigation */}
        {mobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="md:hidden border-t bg-background/95 backdrop-blur-md"
          >
            <div className="container mx-auto px-4 py-4 space-y-4">
              <Link href="#features" className="block text-sm font-medium hover:text-primary transition-colors">
                Features
              </Link>
              <Link href="#comparison" className="block text-sm font-medium hover:text-primary transition-colors">
                vs Competitors
              </Link>
              <Link href="#pricing" className="block text-sm font-medium hover:text-primary transition-colors">
                Pricing
              </Link>
              <Link href="#testimonials" className="block text-sm font-medium hover:text-primary transition-colors">
                Testimonials
              </Link>
              <Link href="/docs" className="block text-sm font-medium hover:text-primary transition-colors">
                Docs
              </Link>
              <div className="flex flex-col space-y-2 pt-4 border-t">
                <Button variant="ghost" asChild>
                  <Link href="/auth/signin">Sign In</Link>
                </Button>
                <Button asChild className="bg-gradient-to-r from-indigo-600 to-purple-600">
                  <Link href="/auth/signup">Start Free Trial</Link>
                </Button>
              </div>
            </div>
          </motion.div>
        )}
      </header>

      {/* Hero Section with 3D Animation */}
      <section className="relative overflow-hidden py-20 sm:py-32">
        <motion.div
          style={{ y, opacity }}
          className="absolute inset-0 bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 dark:from-indigo-950/20 dark:via-purple-950/20 dark:to-pink-950/20"
        />
        
        {/* Animated Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-32 w-80 h-80 bg-gradient-to-r from-indigo-400 to-purple-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
          <div className="absolute -bottom-40 -left-32 w-80 h-80 bg-gradient-to-r from-pink-400 to-indigo-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-1000"></div>
        </div>
        
        <div className="container relative mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mx-auto max-w-5xl text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <Badge variant="secondary" className="mb-6 bg-gradient-to-r from-indigo-100 to-purple-100 dark:from-indigo-900/50 dark:to-purple-900/50 border-indigo-200 dark:border-indigo-800">
                <Sparkles className="w-3 h-3 mr-1" />
                🚀 Production Ready • Trusted by 10,000+ Organizations
              </Badge>
              
              <h1 className="text-4xl font-bold tracking-tight sm:text-6xl lg:text-7xl mb-6">
                The AI Orchestration Platform{' '}
                <span className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                  That Rivals Giants
                </span>
              </h1>
              
              <p className="text-xl leading-8 text-muted-foreground sm:text-2xl mb-8 max-w-4xl mx-auto">
                Build, deploy, and scale AI agents, tools, and workflows with enterprise-grade security. 
                <strong className="text-foreground"> Compete with Zapier, Microsoft Power Automate, and n8n.io</strong> using our revolutionary click-based platform.
              </p>

              {/* Value Proposition Cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-10 max-w-4xl mx-auto">
                <div className="bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm rounded-lg p-4 border border-white/20 dark:border-slate-700/50">
                  <div className="flex items-center justify-center mb-2">
                    <TrendingUp className="w-5 h-5 text-green-600 mr-2" />
                    <span className="font-semibold text-green-600">90% Faster</span>
                  </div>
                  <p className="text-sm text-muted-foreground">Deploy AI solutions 10x faster than traditional development</p>
                </div>
                <div className="bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm rounded-lg p-4 border border-white/20 dark:border-slate-700/50">
                  <div className="flex items-center justify-center mb-2">
                    <DollarSign className="w-5 h-5 text-blue-600 mr-2" />
                    <span className="font-semibold text-blue-600">85% Cost Savings</span>
                  </div>
                  <p className="text-sm text-muted-foreground">Reduce operational costs with intelligent automation</p>
                </div>
                <div className="bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm rounded-lg p-4 border border-white/20 dark:border-slate-700/50">
                  <div className="flex items-center justify-center mb-2">
                    <Shield className="w-5 h-5 text-purple-600 mr-2" />
                    <span className="font-semibold text-purple-600">Enterprise Ready</span>
                  </div>
                  <p className="text-sm text-muted-foreground">SOC 2 compliant with bank-grade security</p>
                </div>
              </div>
              
              <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-12">
                <Button size="lg" className="w-full sm:w-auto bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-8 py-4 text-lg" asChild>
                  <Link href="/auth/signup">
                    Start Free Trial <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
                <Button size="lg" variant="outline" className="w-full sm:w-auto px-8 py-4 text-lg" asChild>
                  <Link href="/demo">
                    <Play className="mr-2 h-5 w-5" />
                    Watch Demo
                  </Link>
                </Button>
              </div>

              {/* Trust Indicators */}
              <div className="flex flex-col items-center space-y-4">
                <p className="text-sm text-muted-foreground">Trusted by innovative companies worldwide</p>
                <div className="flex items-center justify-center space-x-8 opacity-60">
                  <div className="w-24 h-10 bg-slate-300 dark:bg-slate-600 rounded flex items-center justify-center">
                    <span className="text-xs font-medium">TechFlow</span>
                  </div>
                  <div className="w-20 h-10 bg-slate-300 dark:bg-slate-600 rounded flex items-center justify-center">
                    <span className="text-xs font-medium">DataCorp</span>
                  </div>
                  <div className="w-28 h-10 bg-slate-300 dark:bg-slate-600 rounded flex items-center justify-center">
                    <span className="text-xs font-medium">InnovateLab</span>
                  </div>
                  <div className="w-22 h-10 bg-slate-300 dark:bg-slate-600 rounded flex items-center justify-center">
                    <span className="text-xs font-medium">ScaleUp</span>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="flex items-center justify-center mb-2">
                  <stat.icon className="w-8 h-8 text-indigo-600 mb-2" />
                </div>
                <div className="text-3xl font-bold text-foreground mb-1">{stat.value}</div>
                <div className="text-sm text-muted-foreground">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Revolutionary Features Section */}
      <section id="features" className="py-20 sm:py-32">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mx-auto max-w-3xl text-center mb-16">
            <Badge variant="secondary" className="mb-4">
              <Target className="w-3 h-3 mr-1" />
              Revolutionary Platform
            </Badge>
            <h2 className="text-3xl font-bold tracking-tight sm:text-5xl mb-6">
              Everything you need to <span className="text-indigo-600">dominate</span> the AI automation market
            </h2>
            <p className="text-xl text-muted-foreground">
              Built to compete with industry giants. Our platform combines the power of Zapier, 
              the intelligence of Microsoft Power Automate, and the flexibility of n8n.io - all in one unified solution.
            </p>
          </div>
          
          <div className="mx-auto max-w-7xl">
            <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
              {features.map((feature, index) => (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <Card className="h-full border-0 bg-gradient-to-br from-background to-muted/20 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                    <CardContent className="p-8">
                      <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-r from-indigo-600 to-purple-600 mb-6">
                        <feature.icon className="h-6 w-6 text-white" />
                      </div>
                      <h3 className="text-xl font-semibold mb-3">{feature.title}</h3>
                      <p className="text-muted-foreground mb-4">{feature.description}</p>
                      <Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
                        {feature.stats}
                      </Badge>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Competitor Comparison Section */}
      <section id="comparison" className="py-20 sm:py-32 bg-muted/30">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mx-auto max-w-3xl text-center mb-16">
            <Badge variant="secondary" className="mb-4">
              <Award className="w-3 h-3 mr-1" />
              Market Comparison
            </Badge>
            <h2 className="text-3xl font-bold tracking-tight sm:text-5xl mb-6">
              See how we <span className="text-indigo-600">outperform</span> the competition
            </h2>
            <p className="text-xl text-muted-foreground">
              Direct comparison with industry leaders. SynapseAI delivers more features, 
              better performance, and superior value than Zapier, Microsoft, and n8n.io combined.
            </p>
          </div>

          <div className="overflow-x-auto">
            <div className="min-w-full">
              <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                {competitors.map((competitor, index) => (
                  <motion.div
                    key={competitor.name}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                  >
                    <Card className={`h-full ${competitor.highlight ? 'ring-2 ring-indigo-500 shadow-xl scale-105' : ''}`}>
                      <CardContent className="p-6">
                        <div className="text-center mb-6">
                          <div className={`w-12 h-12 mx-auto mb-3 rounded flex items-center justify-center ${
                            competitor.highlight 
                              ? 'bg-gradient-to-r from-indigo-600 to-purple-600' 
                              : 'bg-muted'
                          }`}>
                            <competitor.logo className={`w-6 h-6 ${competitor.highlight ? 'text-white' : 'text-muted-foreground'}`} />
                          </div>
                          <h3 className="font-bold text-lg">{competitor.name}</h3>
                          <p className="text-sm text-muted-foreground">{competitor.pricing}</p>
                          {competitor.highlight && (
                            <Badge className="mt-2 bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400">
                              Best Value
                            </Badge>
                          )}
                        </div>
                        
                        <div className="space-y-3">
                          {Object.entries(competitor.features).map(([feature, available]) => (
                            <div key={feature} className="flex items-center justify-between">
                              <span className="text-sm">{feature}</span>
                              {available ? (
                                <CheckCircle className="w-4 h-4 text-green-500" />
                              ) : (
                                <XCircle className="w-4 h-4 text-red-500" />
                              )}
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>

          <div className="text-center mt-12">
            <Button size="lg" className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700" asChild>
              <Link href="/auth/signup">
                Choose the Winner <ChevronRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-20 sm:py-32">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mx-auto max-w-3xl text-center mb-16">
            <Badge variant="secondary" className="mb-4">
              <DollarSign className="w-3 h-3 mr-1" />
              Transparent Pricing
            </Badge>
            <h2 className="text-3xl font-bold tracking-tight sm:text-5xl mb-6">
              Simple pricing that <span className="text-indigo-600">scales</span> with your success
            </h2>
            <p className="text-xl text-muted-foreground">
              No hidden fees, no surprises. Choose the plan that fits your needs and upgrade as you grow.
              All plans include our core AI orchestration features.
            </p>
          </div>
          
          <div className="mx-auto max-w-7xl">
            <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
              {pricing.map((plan, index) => (
                <motion.div
                  key={plan.name}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <Card className={`relative h-full ${plan.popular ? 'border-indigo-500 shadow-xl scale-105 ring-2 ring-indigo-500' : ''}`}>
                    {plan.popular && (
                      <Badge className="absolute -top-3 left-1/2 -translate-x-1/2 bg-gradient-to-r from-indigo-600 to-purple-600">
                        Most Popular
                      </Badge>
                    )}
                    <CardContent className="p-8">
                      <div className="text-center mb-8">
                        <h3 className="text-2xl font-bold mb-2">{plan.name}</h3>
                        <div className="flex items-baseline justify-center mb-2">
                          <span className="text-5xl font-bold">{plan.price}</span>
                          <span className="text-muted-foreground ml-1">{plan.period}</span>
                        </div>
                        {plan.savings && (
                          <Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
                            {plan.savings}
                          </Badge>
                        )}
                        <p className="text-muted-foreground mt-3">{plan.description}</p>
                      </div>
                      
                      <ul className="space-y-4 mb-8">
                        {plan.features.map((feature) => (
                          <li key={feature} className="flex items-start">
                            <Check className="h-5 w-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                            <span className="text-sm">{feature}</span>
                          </li>
                        ))}
                      </ul>
                      
                      <Button 
                        className={`w-full ${plan.popular 
                          ? 'bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700' 
                          : ''
                        }`}
                        variant={plan.popular ? 'default' : 'outline'}
                        size="lg"
                        asChild
                      >
                        <Link href="/auth/signup">
                          {plan.name === 'Enterprise' ? 'Contact Sales' : 'Start Free Trial'}
                        </Link>
                      </Button>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>

          <div className="text-center mt-12">
            <p className="text-muted-foreground mb-4">
              All plans include 14-day free trial • No credit card required • Cancel anytime
            </p>
            <div className="flex items-center justify-center space-x-6 text-sm text-muted-foreground">
              <div className="flex items-center">
                <Shield className="w-4 h-4 mr-1" />
                SOC 2 Compliant
              </div>
              <div className="flex items-center">
                <Clock className="w-4 h-4 mr-1" />
                99.9% Uptime SLA
              </div>
              <div className="flex items-center">
                <Users className="w-4 h-4 mr-1" />
                24/7 Support
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="py-20 sm:py-32 bg-muted/30">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mx-auto max-w-3xl text-center mb-16">
            <Badge variant="secondary" className="mb-4">
              <Star className="w-3 h-3 mr-1" />
              Customer Success
            </Badge>
            <h2 className="text-3xl font-bold tracking-tight sm:text-5xl mb-6">
              Trusted by <span className="text-indigo-600">10,000+</span> organizations worldwide
            </h2>
            <p className="text-xl text-muted-foreground">
              See how leading companies are transforming their operations with SynapseAI. 
              Real results from real customers.
            </p>
          </div>
          
          <div className="mx-auto max-w-7xl">
            <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
              {testimonials.slice(0, 3).map((testimonial, index) => (
                <motion.div
                  key={testimonial.name}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <Card className="h-full hover:shadow-lg transition-shadow">
                    <CardContent className="p-8">
                      <div className="flex items-center mb-4">
                        {[...Array(testimonial.rating)].map((_, i) => (
                          <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        ))}
                      </div>
                      <blockquote className="text-muted-foreground mb-6 italic">
                        "{testimonial.content}"
                      </blockquote>
                      <div className="flex items-center mb-4">
                        <img
                          src={testimonial.avatar}
                          alt={testimonial.name}
                          className="h-12 w-12 rounded-full mr-4"
                        />
                        <div>
                          <p className="font-semibold text-foreground">{testimonial.name}</p>
                          <p className="text-sm text-muted-foreground">{testimonial.role}</p>
                          <p className="text-xs text-muted-foreground">{testimonial.company}</p>
                        </div>
                      </div>
                      <Badge variant="secondary" className="bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                        {testimonial.metrics}
                      </Badge>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>

            {/* Additional testimonials row */}
            <div className="grid grid-cols-1 gap-8 lg:grid-cols-3 mt-8">
              {testimonials.slice(3, 6).map((testimonial, index) => (
                <motion.div
                  key={testimonial.name}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <Card className="h-full hover:shadow-lg transition-shadow">
                    <CardContent className="p-8">
                      <div className="flex items-center mb-4">
                        {[...Array(testimonial.rating)].map((_, i) => (
                          <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        ))}
                      </div>
                      <blockquote className="text-muted-foreground mb-6 italic">
                        "{testimonial.content}"
                      </blockquote>
                      <div className="flex items-center mb-4">
                        <img
                          src={testimonial.avatar}
                          alt={testimonial.name}
                          className="h-12 w-12 rounded-full mr-4"
                        />
                        <div>
                          <p className="font-semibold text-foreground">{testimonial.name}</p>
                          <p className="text-sm text-muted-foreground">{testimonial.role}</p>
                          <p className="text-xs text-muted-foreground">{testimonial.company}</p>
                        </div>
                      </div>
                      <Badge variant="secondary" className="bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                        {testimonial.metrics}
                      </Badge>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Newsletter Subscription Section */}
      <section className="py-16 bg-gradient-to-r from-indigo-600 to-purple-600">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl mb-4">
              Stay ahead of the AI revolution
            </h2>
            <p className="text-lg text-indigo-100 mb-8">
              Get the latest updates on AI automation, new features, and industry insights delivered to your inbox.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 rounded-lg border-0 bg-white/10 backdrop-blur-sm text-white placeholder-indigo-200 focus:outline-none focus:ring-2 focus:ring-white/50"
              />
              <Button className="bg-white text-indigo-600 hover:bg-indigo-50 px-6 py-3">
                Subscribe
              </Button>
            </div>
            <p className="text-xs text-indigo-200 mt-4">
              No spam, unsubscribe at any time. We respect your privacy.
            </p>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 sm:py-32 bg-gradient-to-r from-slate-900 to-indigo-900 text-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="text-4xl font-bold tracking-tight sm:text-6xl mb-6">
              Ready to dominate the AI automation market?
            </h2>
            <p className="text-xl text-indigo-100 mb-8">
              Join 10,000+ organizations already using SynapseAI to build the future of intelligent automation. 
              Start your free trial today and see why we're the fastest-growing AI orchestration platform.
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-8">
              <Button size="lg" className="w-full sm:w-auto bg-white text-indigo-900 hover:bg-indigo-50 px-8 py-4 text-lg font-semibold" asChild>
                <Link href="/auth/signup">
                  Start Free Trial <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="w-full sm:w-auto border-white text-white hover:bg-white hover:text-indigo-900 px-8 py-4 text-lg" asChild>
                <Link href="/contact">
                  Contact Sales
                </Link>
              </Button>
            </div>
            <div className="flex items-center justify-center space-x-6 text-sm text-indigo-200">
              <div className="flex items-center">
                <CheckCircle className="w-4 h-4 mr-1" />
                14-day free trial
              </div>
              <div className="flex items-center">
                <CheckCircle className="w-4 h-4 mr-1" />
                No credit card required
              </div>
              <div className="flex items-center">
                <CheckCircle className="w-4 h-4 mr-1" />
                Cancel anytime
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t bg-background">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-8">
            <div className="space-y-4 md:col-span-2">
              <div className="flex items-center space-x-2">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-r from-indigo-600 to-purple-600">
                  <Brain className="h-5 w-5 text-white" />
                </div>
                <span className="text-xl font-bold">SynapseAI</span>
              </div>
              <p className="text-muted-foreground max-w-md">
                The universal AI orchestration platform that empowers organizations to build, deploy, 
                and scale intelligent automation with enterprise-grade security and reliability.
              </p>
              <div className="flex space-x-4">
                <Button variant="ghost" size="sm" asChild>
                  <Link href="https://github.com/synapseai">
                    <Github className="h-4 w-4" />
                  </Link>
                </Button>
                <Button variant="ghost" size="sm" asChild>
                  <Link href="https://twitter.com/synapseai">
                    <Twitter className="h-4 w-4" />
                  </Link>
                </Button>
                <Button variant="ghost" size="sm" asChild>
                  <Link href="https://linkedin.com/company/synapseai">
                    <Linkedin className="h-4 w-4" />
                  </Link>
                </Button>
              </div>
            </div>
            
            <div>
              <h3 className="font-semibold mb-4">Product</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li><Link href="/features" className="hover:text-foreground transition-colors">Features</Link></li>
                <li><Link href="/pricing" className="hover:text-foreground transition-colors">Pricing</Link></li>
                <li><Link href="/integrations" className="hover:text-foreground transition-colors">Integrations</Link></li>
                <li><Link href="/security" className="hover:text-foreground transition-colors">Security</Link></li>
                <li><Link href="/changelog" className="hover:text-foreground transition-colors">Changelog</Link></li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold mb-4">Resources</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li><Link href="/docs" className="hover:text-foreground transition-colors">Documentation</Link></li>
                <li><Link href="/guides" className="hover:text-foreground transition-colors">Guides</Link></li>
                <li><Link href="/api" className="hover:text-foreground transition-colors">API Reference</Link></li>
                <li><Link href="/templates" className="hover:text-foreground transition-colors">Templates</Link></li>
                <li><Link href="/support" className="hover:text-foreground transition-colors">Support</Link></li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold mb-4">Company</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li><Link href="/about" className="hover:text-foreground transition-colors">About</Link></li>
                <li><Link href="/careers" className="hover:text-foreground transition-colors">Careers</Link></li>
                <li><Link href="/contact" className="hover:text-foreground transition-colors">Contact</Link></li>
                <li><Link href="/privacy" className="hover:text-foreground transition-colors">Privacy</Link></li>
                <li><Link href="/terms" className="hover:text-foreground transition-colors">Terms</Link></li>
              </ul>
            </div>
          </div>
          
          <div className="mt-12 pt-8 border-t text-center text-sm text-muted-foreground">
            <p>&copy; 2024 SynapseAI. All rights reserved. Built to compete with the giants.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}