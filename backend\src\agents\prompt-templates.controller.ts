import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Delete, 
  Body, 
  Param, 
  Query, 
  UseGuards,
  Request,
  HttpStatus
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { PermissionGuard } from '../auth/permission.guard';
import { Permissions } from '../auth/permissions.decorator';
import { PromptTemplatesService } from './prompt-templates.service';
import { 
  CreatePromptTemplateDto, 
  UpdatePromptTemplateDto,
  CreatePromptReviewDto 
} from './dto/agent.dto';

@Controller('api/v1/prompt-templates')
@UseGuards(JwtAuthGuard, PermissionGuard)
export class PromptTemplatesController {
  constructor(private readonly promptTemplatesService: PromptTemplatesService) {}

  @Post()
  @Permissions('prompt_templates:create')
  async create(@Request() req, @Body() createDto: CreatePromptTemplateDto) {
    try {
      const result = await this.promptTemplatesService.create(
        req.user.id,
        req.user.organizationId,
        createDto
      );
      return {
        statusCode: HttpStatus.CREATED,
        ...result
      };
    } catch (error) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        success: false,
        message: error.message
      };
    }
  }

  @Get()
  @Permissions('prompt_templates:read')
  async findAll(
    @Request() req,
    @Query('category') category?: string,
    @Query('isPublic') isPublic?: string,
    @Query('search') search?: string,
    @Query('agentTemplateId') agentTemplateId?: string,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('sortBy') sortBy?: 'name' | 'usage' | 'rating' | 'createdAt',
    @Query('sortOrder') sortOrder?: 'asc' | 'desc'
  ) {
    try {
      const result = await this.promptTemplatesService.findAll(
        req.user.organizationId,
        {
          category,
          isPublic: isPublic === 'true',
          search,
          agentTemplateId,
          page: page ? parseInt(page) : undefined,
          limit: limit ? parseInt(limit) : undefined,
          sortBy,
          sortOrder
        }
      );
      return {
        statusCode: HttpStatus.OK,
        ...result
      };
    } catch (error) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        success: false,
        message: error.message
      };
    }
  }

  @Get('categories')
  @Permissions('prompt_templates:read')
  async getCategories(@Request() req) {
    try {
      const result = await this.promptTemplatesService.getCategories(req.user.organizationId);
      return {
        statusCode: HttpStatus.OK,
        ...result
      };
    } catch (error) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        success: false,
        message: error.message
      };
    }
  }

  @Get(':id')
  @Permissions('prompt_templates:read')
  async findOne(@Request() req, @Param('id') id: string) {
    try {
      const result = await this.promptTemplatesService.findOne(id, req.user.organizationId);
      return {
        statusCode: HttpStatus.OK,
        ...result
      };
    } catch (error) {
      return {
        statusCode: HttpStatus.NOT_FOUND,
        success: false,
        message: error.message
      };
    }
  }

  @Put(':id')
  @Permissions('prompt_templates:update')
  async update(
    @Request() req,
    @Param('id') id: string,
    @Body() updateDto: UpdatePromptTemplateDto
  ) {
    try {
      const result = await this.promptTemplatesService.update(
        id,
        req.user.id,
        req.user.organizationId,
        updateDto
      );
      return {
        statusCode: HttpStatus.OK,
        ...result
      };
    } catch (error) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        success: false,
        message: error.message
      };
    }
  }

  @Delete(':id')
  @Permissions('prompt_templates:delete')
  async delete(@Request() req, @Param('id') id: string) {
    try {
      const result = await this.promptTemplatesService.delete(
        id,
        req.user.id,
        req.user.organizationId
      );
      return {
        statusCode: HttpStatus.OK,
        ...result
      };
    } catch (error) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        success: false,
        message: error.message
      };
    }
  }

  @Post(':id/duplicate')
  @Permissions('prompt_templates:create')
  async duplicate(
    @Request() req,
    @Param('id') id: string,
    @Body('name') name?: string
  ) {
    try {
      const result = await this.promptTemplatesService.duplicate(
        id,
        req.user.id,
        req.user.organizationId,
        name
      );
      return {
        statusCode: HttpStatus.CREATED,
        ...result
      };
    } catch (error) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        success: false,
        message: error.message
      };
    }
  }

  @Post(':id/reviews')
  @Permissions('prompt_templates:review')
  async addReview(
    @Request() req,
    @Param('id') id: string,
    @Body() reviewDto: CreatePromptReviewDto
  ) {
    try {
      const result = await this.promptTemplatesService.addReview(
        id,
        req.user.id,
        req.user.organizationId,
        reviewDto
      );
      return {
        statusCode: HttpStatus.CREATED,
        ...result
      };
    } catch (error) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        success: false,
        message: error.message
      };
    }
  }

  @Post(':id/optimize')
  @Permissions('prompt_templates:optimize')
  async optimizePrompt(@Request() req, @Param('id') id: string) {
    try {
      const result = await this.promptTemplatesService.optimizePrompt(
        id,
        req.user.id,
        req.user.organizationId
      );
      return {
        statusCode: HttpStatus.OK,
        ...result
      };
    } catch (error) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        success: false,
        message: error.message
      };
    }
  }

  @Post(':id/validate')
  @Permissions('prompt_templates:read')
  async validateVariables(
    @Request() req,
    @Param('id') id: string,
    @Body() body: { content: string; variables: any[] }
  ) {
    try {
      const result = await this.promptTemplatesService.validateVariables(
        body.content,
        body.variables
      );
      return {
        statusCode: HttpStatus.OK,
        ...result
      };
    } catch (error) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        success: false,
        message: error.message
      };
    }
  }
}