import { ProviderType } from '../database/entities/ai-provider.entity';
export declare class AIModelDto {
    id: string;
    name: string;
    description?: string;
    version: string;
    capabilities: {
        chat: boolean;
        completion: boolean;
        embedding: boolean;
        vision: boolean;
        functionCalling: boolean;
        codeGeneration: boolean;
        analysis: boolean;
        multimodal: boolean;
        streaming: boolean;
    };
    fineTuned?: boolean;
    contextLength?: number;
    costPer1KTokens: {
        input: number;
        output: number;
    };
}
export declare class CreateAIProviderDto {
    name: string;
    type: ProviderType;
    config: {
        apiKey?: string;
        baseUrl?: string;
        organizationId?: string;
        projectId?: string;
        region?: string;
        customHeaders?: Record<string, string>;
        timeout?: number;
        maxRetries?: number;
    };
    models?: AIModelDto[];
    isActive?: boolean;
    quotaLimits?: {
        dailyRequests?: number;
        monthlyRequests?: number;
        dailyCostCents?: number;
        monthlyCostCents?: number;
    };
}
export declare class UpdateAIProviderDto {
    name?: string;
    config?: {
        apiKey?: string;
        baseUrl?: string;
        organizationId?: string;
        projectId?: string;
        region?: string;
        customHeaders?: Record<string, string>;
        timeout?: number;
        maxRetries?: number;
    };
    models?: AIModelDto[];
    isActive?: boolean;
    quotaLimits?: {
        dailyRequests?: number;
        monthlyRequests?: number;
        dailyCostCents?: number;
        monthlyCostCents?: number;
    };
}
export declare class AIRequestDto {
    requestId: string;
    providerId?: string;
    modelId?: string;
    messages: Array<{
        role: 'system' | 'user' | 'assistant';
        content: string;
    }>;
    temperature?: number;
    maxTokens?: number;
    stream?: boolean;
    capabilities?: string[];
}
export declare class ProviderSelectionDto {
    capabilities: string[];
    maxCost?: number;
    maxLatency?: number;
    preferredProviders?: string[];
    excludeProviders?: string[];
}
export declare class ProviderTestDto {
    testMessage?: string;
    maxTokens?: number;
}
