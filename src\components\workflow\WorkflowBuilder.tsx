"use client";

import React, { useState, useCallback } from "react";
import React<PERSON><PERSON>, {
  Background,
  Controls,
  MiniMap,
  Panel,
  useNodesState,
  useEdgesState,
  addEdge,
  Connection,
  Edge,
  Node,
  NodeChange,
  EdgeChange,
  ConnectionLineType,
  MarkerType,
} from "reactflow";
import "reactflow/dist/style.css";

import { Card } from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

import NodePanel from "./NodePanel";
import WorkflowControls from "./WorkflowControls";

// Define node types for the workflow
const nodeTypes = {};

interface WorkflowBuilderProps {
  workflowId?: string;
  initialNodes?: Node[];
  initialEdges?: Edge[];
  readOnly?: boolean;
}

const WorkflowBuilder = ({
  workflowId,
  initialNodes = [],
  initialEdges = [],
  readOnly = false,
}: WorkflowBuilderProps) => {
  // State for workflow data
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);
  const [workflowName, setWorkflowName] = useState("New Workflow");
  const [isModified, setIsModified] = useState(false);
  const [activeTab, setActiveTab] = useState("nodes");

  // Handle node selection
  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    setSelectedNode(node);
  }, []);

  // Handle edge connections
  const onConnect = useCallback(
    (connection: Connection) => {
      setEdges((eds) =>
        addEdge(
          {
            ...connection,
            type: "smoothstep",
            animated: true,
            markerEnd: {
              type: MarkerType.ArrowClosed,
            },
          },
          eds,
        ),
      );
      setIsModified(true);
    },
    [setEdges],
  );

  // Handle node changes
  const handleNodesChange = useCallback(
    (changes: NodeChange[]) => {
      onNodesChange(changes);
      setIsModified(true);
    },
    [onNodesChange],
  );

  // Handle edge changes
  const handleEdgesChange = useCallback(
    (changes: EdgeChange[]) => {
      onEdgesChange(changes);
      setIsModified(true);
    },
    [onEdgesChange],
  );

  // Add a new node to the canvas
  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = "move";
  }, []);

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();

      const nodeData = event.dataTransfer.getData("application/reactflow");
      if (!nodeData) return;

      const data = JSON.parse(nodeData);
      const position = {
        x: event.clientX - data.offsetX,
        y: event.clientY - data.offsetY,
      };

      const newNode = {
        id: `${data.type}-${Date.now()}`,
        type: data.type,
        position,
        data: { label: data.label, ...data.properties },
      };

      setNodes((nds) => nds.concat(newNode));
      setIsModified(true);
    },
    [setNodes],
  );

  // Handle workflow save
  const handleSaveWorkflow = () => {
    // In a real implementation, this would save to the backend
    console.log("Saving workflow:", { workflowId, workflowName, nodes, edges });
    setIsModified(false);
  };

  // Handle workflow execution
  const handleRunWorkflow = () => {
    // In a real implementation, this would trigger workflow execution
    console.log("Running workflow:", { workflowId });
  };

  return (
    <div className="flex flex-col h-full w-full bg-background">
      {/* Workflow Controls */}
      <WorkflowControls
        workflowName={workflowName}
        setWorkflowName={setWorkflowName}
        isModified={isModified}
        onSave={handleSaveWorkflow}
        onRun={handleRunWorkflow}
        readOnly={readOnly}
      />

      <div className="flex flex-1 overflow-hidden">
        {/* Left Sidebar - Node Panel */}
        <div className="w-72 border-r bg-card">
          <Tabs
            defaultValue="nodes"
            value={activeTab}
            onValueChange={setActiveTab}
          >
            <div className="p-4 border-b">
              <TabsList className="w-full">
                <TabsTrigger value="nodes" className="flex-1">
                  Nodes
                </TabsTrigger>
                <TabsTrigger value="settings" className="flex-1">
                  Settings
                </TabsTrigger>
              </TabsList>
            </div>

            <ScrollArea className="h-[calc(100vh-160px)]">
              <TabsContent value="nodes" className="m-0">
                <NodePanel />
              </TabsContent>

              <TabsContent value="settings" className="p-4 m-0">
                <div className="space-y-4">
                  <div>
                    <h3 className="text-sm font-medium mb-2">
                      Workflow Settings
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      Configure global workflow settings and properties.
                    </p>
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Execution</h4>
                    <div className="grid gap-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Timeout (seconds)</span>
                        <input
                          type="number"
                          className="w-20 h-8 rounded-md border border-input bg-background px-3 py-1 text-sm"
                          defaultValue={60}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Max Retries</span>
                        <input
                          type="number"
                          className="w-20 h-8 rounded-md border border-input bg-background px-3 py-1 text-sm"
                          defaultValue={3}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </ScrollArea>
          </Tabs>
        </div>

        {/* Main Flow Canvas */}
        <div className="flex-1 h-full">
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={handleNodesChange}
            onEdgesChange={handleEdgesChange}
            onConnect={onConnect}
            onNodeClick={onNodeClick}
            nodeTypes={nodeTypes}
            onDragOver={onDragOver}
            onDrop={onDrop}
            connectionLineType={ConnectionLineType.SmoothStep}
            fitView
            snapToGrid
            snapGrid={[15, 15]}
          >
            <Background color="#aaa" gap={16} />
            <Controls />
            <MiniMap nodeStrokeWidth={3} zoomable pannable />

            <Panel
              position="top-right"
              className="bg-card border rounded-md shadow-sm p-2"
            >
              <TooltipProvider>
                <div className="flex gap-2">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant="outline" size="sm">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="mr-1"
                        >
                          <rect width="18" height="18" x="3" y="3" rx="2" />
                          <path d="M9 9h.01" />
                          <path d="M15 9h.01" />
                          <path d="M9 15h.01" />
                          <path d="M15 15h.01" />
                        </svg>
                        Grid
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Toggle grid display</p>
                    </TooltipContent>
                  </Tooltip>

                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant="outline" size="sm">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="mr-1"
                        >
                          <path d="M3 3v18h18" />
                          <path d="m19 9-5 5-4-4-3 3" />
                        </svg>
                        Analytics
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>View workflow analytics</p>
                    </TooltipContent>
                  </Tooltip>
                </div>
              </TooltipProvider>
            </Panel>
          </ReactFlow>
        </div>

        {/* Right Sidebar - Node Configuration (conditionally rendered) */}
        {selectedNode && (
          <div className="w-80 border-l bg-card">
            <div className="p-4 border-b flex justify-between items-center">
              <h3 className="font-medium">Node Configuration</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSelectedNode(null)}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M18 6 6 18" />
                  <path d="m6 6 12 12" />
                </svg>
                <span className="sr-only">Close</span>
              </Button>
            </div>

            <ScrollArea className="h-[calc(100vh-160px)] p-4">
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Node Name</label>
                  <input
                    type="text"
                    className="w-full h-9 mt-1.5 rounded-md border border-input bg-background px-3 py-1 text-sm"
                    value={selectedNode.data.label}
                    onChange={(e) => {
                      // In a real implementation, this would update the node data
                      console.log("Updating node name:", e.target.value);
                    }}
                  />
                </div>

                <Separator />

                <div>
                  <h4 className="text-sm font-medium mb-2">Node Properties</h4>
                  <Card className="p-4">
                    <p className="text-sm text-muted-foreground">
                      Node-specific configuration options would appear here
                      based on the selected node type.
                    </p>
                  </Card>
                </div>

                <Separator />

                <div className="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedNode(null)}
                  >
                    Cancel
                  </Button>
                  <Button size="sm">Apply</Button>
                </div>
              </div>
            </ScrollArea>
          </div>
        )}
      </div>
    </div>
  );
};

export default WorkflowBuilder;
