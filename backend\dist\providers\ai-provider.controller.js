"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIProviderController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/jwt-auth.guard");
const ai_provider_manager_service_1 = require("./ai-provider-manager.service");
const ai_provider_selector_service_1 = require("./ai-provider-selector.service");
const ai_provider_integration_service_1 = require("./ai-provider-integration.service");
const ai_provider_dto_1 = require("./dto/ai-provider.dto");
let AIProviderController = class AIProviderController {
    constructor(providerManager, providerSelector, providerIntegration) {
        this.providerManager = providerManager;
        this.providerSelector = providerSelector;
        this.providerIntegration = providerIntegration;
    }
    async createProvider(createProviderDto, req) {
        try {
            const provider = await this.providerManager.createProvider(createProviderDto, req.user.organizationId, req.user.id);
            return {
                success: true,
                data: provider,
                message: 'AI provider created successfully',
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message,
                message: 'Failed to create AI provider',
            };
        }
    }
    async getProviders(req) {
        try {
            const providers = await this.providerManager.getProvidersByOrganization(req.user.organizationId);
            return {
                success: true,
                data: providers,
                message: 'AI providers retrieved successfully',
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message,
                message: 'Failed to retrieve AI providers',
            };
        }
    }
    async getActiveProviders(req) {
        try {
            const providers = await this.providerManager.getActiveProviders(req.user.organizationId);
            return {
                success: true,
                data: providers,
                message: 'Active AI providers retrieved successfully',
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message,
                message: 'Failed to retrieve active AI providers',
            };
        }
    }
    async getProviderRankings(req) {
        try {
            const rankings = await this.providerSelector.getProviderRankings(req.user.organizationId);
            return {
                success: true,
                data: rankings,
                message: 'Provider rankings retrieved successfully',
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message,
                message: 'Failed to retrieve provider rankings',
            };
        }
    }
    async getProvider(id, req) {
        try {
            const provider = await this.providerManager.getProviderById(id, req.user.organizationId);
            return {
                success: true,
                data: provider,
                message: 'AI provider retrieved successfully',
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message,
                message: 'Failed to retrieve AI provider',
            };
        }
    }
    async updateProvider(id, updateProviderDto, req) {
        try {
            const provider = await this.providerManager.updateProvider(id, updateProviderDto, req.user.organizationId);
            return {
                success: true,
                data: provider,
                message: 'AI provider updated successfully',
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message,
                message: 'Failed to update AI provider',
            };
        }
    }
    async deleteProvider(id, req) {
        try {
            await this.providerManager.deleteProvider(id, req.user.organizationId);
            return {
                success: true,
                message: 'AI provider deleted successfully',
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message,
                message: 'Failed to delete AI provider',
            };
        }
    }
    async selectProvider(selectionDto, req) {
        try {
            const selection = await this.providerSelector.selectOptimalProvider(Object.assign(Object.assign({}, selectionDto), { organizationId: req.user.organizationId }));
            return {
                success: true,
                data: selection,
                message: 'Optimal provider selected successfully',
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message,
                message: 'Failed to select optimal provider',
            };
        }
    }
    async processRequest(requestDto, req) {
        try {
            const response = await this.providerIntegration.processRequest(Object.assign(Object.assign({}, requestDto), { organizationId: req.user.organizationId }));
            return {
                success: true,
                data: response,
                message: 'AI request processed successfully',
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message,
                message: 'Failed to process AI request',
            };
        }
    }
    async testProvider(id, testDto, req) {
        try {
            const result = await this.providerIntegration.testProvider(id, req.user.organizationId);
            return {
                success: true,
                data: result,
                message: 'AI provider tested successfully',
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message,
                message: 'Failed to test AI provider',
            };
        }
    }
    async getUsageStats(startDate, endDate, req) {
        try {
            const stats = await this.providerManager.getUsageStats(req.user.organizationId, startDate ? new Date(startDate) : undefined, endDate ? new Date(endDate) : undefined);
            return {
                success: true,
                data: stats,
                message: 'Usage statistics retrieved successfully',
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message,
                message: 'Failed to retrieve usage statistics',
            };
        }
    }
};
exports.AIProviderController = AIProviderController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new AI provider' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Provider created successfully' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [ai_provider_dto_1.CreateAIProviderDto, Object]),
    __metadata("design:returntype", Promise)
], AIProviderController.prototype, "createProvider", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all AI providers for organization' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Providers retrieved successfully' }),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AIProviderController.prototype, "getProviders", null);
__decorate([
    (0, common_1.Get)('active'),
    (0, swagger_1.ApiOperation)({ summary: 'Get active AI providers' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Active providers retrieved successfully' }),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AIProviderController.prototype, "getActiveProviders", null);
__decorate([
    (0, common_1.Get)('rankings'),
    (0, swagger_1.ApiOperation)({ summary: 'Get provider performance rankings' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Provider rankings retrieved successfully' }),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AIProviderController.prototype, "getProviderRankings", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get AI provider by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Provider retrieved successfully' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AIProviderController.prototype, "getProvider", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update AI provider' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Provider updated successfully' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, ai_provider_dto_1.UpdateAIProviderDto, Object]),
    __metadata("design:returntype", Promise)
], AIProviderController.prototype, "updateProvider", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete AI provider' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Provider deleted successfully' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AIProviderController.prototype, "deleteProvider", null);
__decorate([
    (0, common_1.Post)('select'),
    (0, swagger_1.ApiOperation)({ summary: 'Select optimal provider for request' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Provider selected successfully' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [ai_provider_dto_1.ProviderSelectionDto, Object]),
    __metadata("design:returntype", Promise)
], AIProviderController.prototype, "selectProvider", null);
__decorate([
    (0, common_1.Post)('request'),
    (0, swagger_1.ApiOperation)({ summary: 'Process AI request' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Request processed successfully' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [ai_provider_dto_1.AIRequestDto, Object]),
    __metadata("design:returntype", Promise)
], AIProviderController.prototype, "processRequest", null);
__decorate([
    (0, common_1.Post)(':id/test'),
    (0, swagger_1.ApiOperation)({ summary: 'Test AI provider connection' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Provider tested successfully' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, ai_provider_dto_1.ProviderTestDto, Object]),
    __metadata("design:returntype", Promise)
], AIProviderController.prototype, "testProvider", null);
__decorate([
    (0, common_1.Get)('usage/stats'),
    (0, swagger_1.ApiOperation)({ summary: 'Get usage statistics' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Usage stats retrieved successfully' }),
    __param(0, (0, common_1.Query)('startDate')),
    __param(1, (0, common_1.Query)('endDate')),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], AIProviderController.prototype, "getUsageStats", null);
exports.AIProviderController = AIProviderController = __decorate([
    (0, swagger_1.ApiTags)('AI Providers'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('api/v1/providers'),
    __metadata("design:paramtypes", [ai_provider_manager_service_1.AIProviderManagerService,
        ai_provider_selector_service_1.AIProviderSelectorService,
        ai_provider_integration_service_1.AIProviderIntegrationService])
], AIProviderController);
//# sourceMappingURL=ai-provider.controller.js.map