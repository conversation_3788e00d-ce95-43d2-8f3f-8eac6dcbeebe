import { AIProviderManagerService } from './ai-provider-manager.service';
import { AIProviderSelectorService } from './ai-provider-selector.service';
import { ApixGateway } from '../websocket/apix.gateway';
export interface AIRequest {
    requestId: string;
    providerId?: string;
    modelId?: string;
    messages: Array<{
        role: 'system' | 'user' | 'assistant';
        content: string;
    }>;
    temperature?: number;
    maxTokens?: number;
    stream?: boolean;
    organizationId: string;
}
export interface AIResponse {
    requestId: string;
    providerId: string;
    modelId: string;
    content: string;
    usage: {
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
    };
    cost: number;
    latency: number;
    metadata?: Record<string, any>;
}
export declare class AIProviderIntegrationService {
    private providerManager;
    private providerSelector;
    private apixGateway;
    private readonly logger;
    private readonly providerClients;
    constructor(providerManager: AIProviderManagerService, providerSelector: AIProviderSelectorService, apixGateway: ApixGateway);
    processRequest(request: AIRequest): Promise<AIResponse>;
    private callProvider;
    private callOpenAI;
    private callClaude;
    private callGemini;
    private callMistral;
    private callGroq;
    private getOrCreateClient;
    private executeWithRetry;
    private isRetryableError;
    private calculateCost;
    streamRequest(request: AIRequest): Promise<AsyncIterable<string>>;
    testProvider(providerId: string, organizationId: string): Promise<{
        success: boolean;
        latency: number;
        error?: string;
    }>;
}
