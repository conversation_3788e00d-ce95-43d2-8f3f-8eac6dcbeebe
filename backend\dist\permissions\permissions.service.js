"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PermissionService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const apix_gateway_1 = require("../websocket/apix.gateway");
let PermissionService = class PermissionService {
    constructor(prisma, apixGateway) {
        this.prisma = prisma;
        this.apixGateway = apixGateway;
    }
    async createPermission(createPermissionDto, createdById) {
        const { name, description, resource, action, organizationId } = createPermissionDto;
        const existingPermission = await this.prisma.permission.findUnique({
            where: {
                name_organizationId: {
                    name,
                    organizationId,
                },
            },
        });
        if (existingPermission) {
            throw new common_1.ConflictException('Permission with this name already exists in organization');
        }
        const organization = await this.prisma.organization.findUnique({
            where: { id: organizationId },
        });
        if (!organization) {
            throw new common_1.NotFoundException('Organization not found');
        }
        const result = await this.prisma.$transaction(async (tx) => {
            const permission = await tx.permission.create({
                data: {
                    name,
                    description,
                    resource,
                    action,
                    organizationId,
                },
                include: {
                    organization: true,
                },
            });
            await tx.auditLog.create({
                data: {
                    userId: createdById,
                    organizationId,
                    action: 'CREATE',
                    resource: 'permission',
                    resourceId: permission.id,
                    details: {
                        action: 'permission_created',
                        name: permission.name,
                        description: permission.description,
                        resource: permission.resource,
                        permissionAction: permission.action,
                    },
                },
            });
            return permission;
        });
        await this.apixGateway.publishToOrganization(organizationId, {
            type: 'permission.created',
            payload: {
                permissionId: result.id,
                name: result.name,
                description: result.description,
                resource: result.resource,
                action: result.action,
                createdBy: createdById,
            },
        });
        return result;
    }
    async updatePermission(permissionId, updatePermissionDto, updatedById) {
        const { name, description, resource, action } = updatePermissionDto;
        const existingPermission = await this.prisma.permission.findUnique({
            where: { id: permissionId },
            include: { organization: true },
        });
        if (!existingPermission) {
            throw new common_1.NotFoundException('Permission not found');
        }
        if (existingPermission.isSystem) {
            throw new common_1.BadRequestException('Cannot modify system permission');
        }
        if (name && name !== existingPermission.name) {
            const nameExists = await this.prisma.permission.findUnique({
                where: {
                    name_organizationId: {
                        name,
                        organizationId: existingPermission.organizationId,
                    },
                },
            });
            if (nameExists) {
                throw new common_1.ConflictException('Permission name already exists in organization');
            }
        }
        const result = await this.prisma.$transaction(async (tx) => {
            const permission = await tx.permission.update({
                where: { id: permissionId },
                data: Object.assign(Object.assign(Object.assign(Object.assign({}, (name && { name })), (description !== undefined && { description })), (resource && { resource })), (action && { action })),
                include: {
                    organization: true,
                },
            });
            await tx.auditLog.create({
                data: {
                    userId: updatedById,
                    organizationId: existingPermission.organizationId,
                    action: 'UPDATE',
                    resource: 'permission',
                    resourceId: permissionId,
                    details: {
                        action: 'permission_updated',
                        changes: updatePermissionDto,
                    },
                },
            });
            return permission;
        });
        await this.apixGateway.publishToOrganization(existingPermission.organizationId, {
            type: 'permission.updated',
            payload: {
                permissionId,
                changes: updatePermissionDto,
                updatedBy: updatedById,
            },
        });
        return result;
    }
    async deletePermission(permissionId, deletedById) {
        const permission = await this.prisma.permission.findUnique({
            where: { id: permissionId },
            include: {
                organization: true,
                rolePermissions: true,
                userPermissions: true,
            },
        });
        if (!permission) {
            throw new common_1.NotFoundException('Permission not found');
        }
        if (permission.isSystem) {
            throw new common_1.BadRequestException('Cannot delete system permission');
        }
        if (permission.rolePermissions.length > 0 || permission.userPermissions.length > 0) {
            throw new common_1.BadRequestException('Cannot delete permission that is assigned to roles or users');
        }
        await this.prisma.$transaction(async (tx) => {
            await tx.permission.delete({
                where: { id: permissionId },
            });
            await tx.auditLog.create({
                data: {
                    userId: deletedById,
                    organizationId: permission.organizationId,
                    action: 'DELETE',
                    resource: 'permission',
                    resourceId: permissionId,
                    details: {
                        action: 'permission_deleted',
                        name: permission.name,
                        description: permission.description,
                        resource: permission.resource,
                        permissionAction: permission.action,
                    },
                },
            });
        });
        await this.apixGateway.publishToOrganization(permission.organizationId, {
            type: 'permission.deleted',
            payload: {
                permissionId,
                name: permission.name,
                deletedBy: deletedById,
            },
        });
        return { message: 'Permission deleted successfully' };
    }
    async getPermissions(filters) {
        const { search, resource, organizationId, page = 1, limit = 20, sortBy = 'createdAt', sortOrder = 'desc', } = filters;
        const skip = (page - 1) * limit;
        const where = {};
        if (organizationId) {
            where.organizationId = organizationId;
        }
        if (search) {
            where.OR = [
                { name: { contains: search, mode: 'insensitive' } },
                { description: { contains: search, mode: 'insensitive' } },
                { resource: { contains: search, mode: 'insensitive' } },
                { action: { contains: search, mode: 'insensitive' } },
            ];
        }
        if (resource) {
            where.resource = resource;
        }
        const [permissions, total] = await Promise.all([
            this.prisma.permission.findMany({
                where,
                include: {
                    organization: true,
                    rolePermissions: {
                        include: {
                            role: {
                                select: {
                                    id: true,
                                    name: true,
                                },
                            },
                        },
                    },
                    userPermissions: {
                        include: {
                            user: {
                                select: {
                                    id: true,
                                    name: true,
                                    email: true,
                                },
                            },
                        },
                    },
                },
                orderBy: { [sortBy]: sortOrder },
                skip,
                take: limit,
            }),
            this.prisma.permission.count({ where }),
        ]);
        return {
            permissions,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit),
            },
        };
    }
    async getPermissionById(permissionId) {
        const permission = await this.prisma.permission.findUnique({
            where: { id: permissionId },
            include: {
                organization: true,
                rolePermissions: {
                    include: {
                        role: {
                            select: {
                                id: true,
                                name: true,
                                description: true,
                                isActive: true,
                            },
                        },
                    },
                },
                userPermissions: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                name: true,
                                email: true,
                                systemRole: true,
                                isActive: true,
                            },
                        },
                    },
                },
            },
        });
        if (!permission) {
            throw new common_1.NotFoundException('Permission not found');
        }
        return permission;
    }
    async assignPermissionToUser(permissionId, userId, assignedById) {
        const permission = await this.prisma.permission.findUnique({
            where: { id: permissionId },
            include: { organization: true },
        });
        if (!permission) {
            throw new common_1.NotFoundException('Permission not found');
        }
        const user = await this.prisma.user.findUnique({
            where: { id: userId, organizationId: permission.organizationId },
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found in organization');
        }
        const existingAssignment = await this.prisma.userPermission.findUnique({
            where: {
                userId_permissionId: {
                    userId,
                    permissionId,
                },
            },
        });
        if (existingAssignment) {
            throw new common_1.ConflictException('User already has this permission');
        }
        await this.prisma.$transaction(async (tx) => {
            await tx.userPermission.create({
                data: {
                    userId,
                    permissionId,
                    organizationId: permission.organizationId,
                },
            });
            await tx.auditLog.create({
                data: {
                    userId: assignedById,
                    organizationId: permission.organizationId,
                    action: 'CREATE',
                    resource: 'user_permission',
                    resourceId: `${userId}-${permissionId}`,
                    details: {
                        action: 'permission_assigned',
                        userId,
                        permissionId,
                        permissionName: permission.name,
                        userEmail: user.email,
                    },
                },
            });
        });
        await this.apixGateway.publishToOrganization(permission.organizationId, {
            type: 'permission.assigned',
            payload: {
                userId,
                permissionId,
                permissionName: permission.name,
                userEmail: user.email,
                assignedBy: assignedById,
            },
        });
        return { message: 'Permission assigned successfully' };
    }
    async removePermissionFromUser(permissionId, userId, removedById) {
        const assignment = await this.prisma.userPermission.findUnique({
            where: {
                userId_permissionId: {
                    userId,
                    permissionId,
                },
            },
            include: {
                permission: true,
                user: true,
            },
        });
        if (!assignment) {
            throw new common_1.NotFoundException('Permission assignment not found');
        }
        await this.prisma.$transaction(async (tx) => {
            await tx.userPermission.delete({
                where: {
                    userId_permissionId: {
                        userId,
                        permissionId,
                    },
                },
            });
            await tx.auditLog.create({
                data: {
                    userId: removedById,
                    organizationId: assignment.organizationId,
                    action: 'DELETE',
                    resource: 'user_permission',
                    resourceId: `${userId}-${permissionId}`,
                    details: {
                        action: 'permission_removed',
                        userId,
                        permissionId,
                        permissionName: assignment.permission.name,
                        userEmail: assignment.user.email,
                    },
                },
            });
        });
        await this.apixGateway.publishToOrganization(assignment.organizationId, {
            type: 'permission.removed',
            payload: {
                userId,
                permissionId,
                permissionName: assignment.permission.name,
                userEmail: assignment.user.email,
                removedBy: removedById,
            },
        });
        return { message: 'Permission removed successfully' };
    }
    async getResourceList(organizationId) {
        const resources = await this.prisma.permission.findMany({
            where: { organizationId },
            select: { resource: true },
            distinct: ['resource'],
            orderBy: { resource: 'asc' },
        });
        return resources.map(r => r.resource);
    }
    async getActionList(organizationId, resource) {
        const where = { organizationId };
        if (resource) {
            where.resource = resource;
        }
        const actions = await this.prisma.permission.findMany({
            where,
            select: { action: true },
            distinct: ['action'],
            orderBy: { action: 'asc' },
        });
        return actions.map(a => a.action);
    }
};
exports.PermissionService = PermissionService;
exports.PermissionService = PermissionService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        apix_gateway_1.ApixGateway])
], PermissionService);
//# sourceMappingURL=permissions.service.js.map