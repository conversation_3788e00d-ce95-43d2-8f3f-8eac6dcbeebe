import { Injectable, Logger } from '@nestjs/common';
import { InjectRedis } from '@nestjs-modules/ioredis';
import Redis from 'ioredis';
import { AIProviderManagerService } from './ai-provider-manager.service';
import { ApixGateway } from '../websocket/apix.gateway';

export interface ProviderScore {
  providerId: string;
  score: number;
  latency: number;
  cost: number;
  reliability: number;
  capabilities: string[];
}

export interface ProviderSelectionRequest {
  organizationId: string;
  capabilities: string[];
  maxCost?: number;
  maxLatency?: number;
  preferredProviders?: string[];
  excludeProviders?: string[];
}

@Injectable()
export class AIProviderSelectorService {
  private readonly logger = new Logger(AIProviderSelectorService.name);
  private readonly PERFORMANCE_CACHE_TTL = 300; // 5 minutes
  private readonly HEALTH_CHECK_INTERVAL = 60000; // 1 minute

  constructor(
    private providerManager: AIProviderManagerService,
    @InjectRedis() private readonly redis: Redis,
    private apixGateway: ApixGateway,
  ) {
    // Start health check interval
    setInterval(() => this.performHealthChecks(), this.HEALTH_CHECK_INTERVAL);
  }

  async selectOptimalProvider(request: ProviderSelectionRequest): Promise<{
    providerId: string;
    modelId: string;
    fallbackChain: string[];
    reasoning: string;
  }> {
    try {
      const activeProviders = await this.providerManager.getActiveProviders(request.organizationId);
      
      if (activeProviders.length === 0) {
        throw new Error('No active providers available');
      }

      // Filter providers based on capabilities
      const capableProviders = activeProviders.filter(provider => 
        this.hasRequiredCapabilities(provider, request.capabilities)
      );

      if (capableProviders.length === 0) {
        throw new Error('No providers support the required capabilities');
      }

      // Score providers
      const scoredProviders = await Promise.all(
        capableProviders.map(provider => this.scoreProvider(provider, request))
      );

      // Sort by score (highest first)
      scoredProviders.sort((a, b) => b.score - a.score);

      // Apply preferences and exclusions
      const filteredProviders = this.applyPreferences(scoredProviders, request);

      if (filteredProviders.length === 0) {
        throw new Error('No providers match the selection criteria');
      }

      const selectedProvider = filteredProviders[0];
      const fallbackChain = filteredProviders.slice(1, 4).map(p => p.providerId);

      // Select best model for the provider
      const provider = capableProviders.find(p => p.id === selectedProvider.providerId);
      const selectedModel = this.selectBestModel(provider, request.capabilities);

      const reasoning = this.generateSelectionReasoning(selectedProvider, request);

      this.logger.log(`Selected provider ${selectedProvider.providerId} for organization ${request.organizationId}`);

      return {
        providerId: selectedProvider.providerId,
        modelId: selectedModel.id,
        fallbackChain,
        reasoning,
      };
    } catch (error) {
      this.logger.error(`Provider selection failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  private async scoreProvider(provider: any, request: ProviderSelectionRequest): Promise<ProviderScore> {
    const performanceData = await this.getProviderPerformance(provider.id);
    
    // Base scoring factors
    const latencyScore = this.calculateLatencyScore(performanceData.averageLatency, request.maxLatency);
    const costScore = this.calculateCostScore(performanceData.averageCost, request.maxCost);
    const reliabilityScore = performanceData.reliability || 0.95;
    const capabilityScore = this.calculateCapabilityScore(provider, request.capabilities);

    // Weighted final score
    const score = (
      latencyScore * 0.25 +
      costScore * 0.25 +
      reliabilityScore * 0.3 +
      capabilityScore * 0.2
    );

    return {
      providerId: provider.id,
      score,
      latency: performanceData.averageLatency,
      cost: performanceData.averageCost,
      reliability: reliabilityScore,
      capabilities: this.getProviderCapabilities(provider),
    };
  }

  private calculateLatencyScore(latency: number, maxLatency?: number): number {
    if (maxLatency && latency > maxLatency) {
      return 0;
    }
    
    // Score based on latency (lower is better)
    // Normalize to 0-1 scale where 100ms = 1.0, 2000ms = 0.0
    return Math.max(0, Math.min(1, (2000 - latency) / 1900));
  }

  private calculateCostScore(cost: number, maxCost?: number): number {
    if (maxCost && cost > maxCost) {
      return 0;
    }
    
    // Score based on cost (lower is better)
    // Normalize to 0-1 scale where $0.001 = 1.0, $0.1 = 0.0
    return Math.max(0, Math.min(1, (0.1 - cost) / 0.099));
  }

  private calculateCapabilityScore(provider: any, requiredCapabilities: string[]): number {
    const providerCapabilities = this.getProviderCapabilities(provider);
    const matchedCapabilities = requiredCapabilities.filter(cap => 
      providerCapabilities.includes(cap)
    );
    
    return matchedCapabilities.length / requiredCapabilities.length;
  }

  private hasRequiredCapabilities(provider: any, requiredCapabilities: string[]): boolean {
    const providerCapabilities = this.getProviderCapabilities(provider);
    return requiredCapabilities.every(cap => providerCapabilities.includes(cap));
  }

  private getProviderCapabilities(provider: any): string[] {
    const baseCapabilities = ['chat', 'completion'];
    
    switch (provider.type) {
      case 'OPENAI':
        return [...baseCapabilities, 'function-calling', 'vision', 'code-generation'];
      case 'CLAUDE':
        return [...baseCapabilities, 'function-calling', 'vision', 'analysis'];
      case 'GEMINI':
        return [...baseCapabilities, 'vision', 'multimodal', 'code-generation'];
      case 'MISTRAL':
        return [...baseCapabilities, 'function-calling', 'code-generation'];
      case 'GROQ':
        return [...baseCapabilities, 'fast-inference'];
      default:
        return baseCapabilities;
    }
  }

  private selectBestModel(provider: any, capabilities: string[]): any {
    if (!provider.models || provider.models.length === 0) {
      throw new Error(`No models available for provider ${provider.id}`);
    }

    // Select model based on capabilities and performance
    const suitableModels = provider.models.filter(model => 
      this.modelSupportsCapabilities(model, capabilities)
    );

    if (suitableModels.length === 0) {
      return provider.models[0]; // Fallback to first model
    }

    // Return the most capable model
    return suitableModels.reduce((best, current) => 
      this.compareModels(best, current) > 0 ? best : current
    );
  }

  private modelSupportsCapabilities(model: any, capabilities: string[]): boolean {
    const modelCapabilities = model.capabilities || {};
    return capabilities.every(cap => modelCapabilities[cap] === true);
  }

  private compareModels(modelA: any, modelB: any): number {
    // Simple comparison based on model version/name
    const scoreA = this.getModelScore(modelA);
    const scoreB = this.getModelScore(modelB);
    return scoreA - scoreB;
  }

  private getModelScore(model: any): number {
    // Score models based on their capabilities and performance
    const capabilityCount = Object.keys(model.capabilities || {}).length;
    const versionScore = model.version ? parseFloat(model.version) || 1 : 1;
    return capabilityCount * versionScore;
  }

  private applyPreferences(
    scoredProviders: ProviderScore[],
    request: ProviderSelectionRequest
  ): ProviderScore[] {
    let filtered = [...scoredProviders];

    // Apply exclusions
    if (request.excludeProviders && request.excludeProviders.length > 0) {
      filtered = filtered.filter(p => !request.excludeProviders.includes(p.providerId));
    }

    // Apply preferences (boost scores)
    if (request.preferredProviders && request.preferredProviders.length > 0) {
      filtered = filtered.map(p => ({
        ...p,
        score: request.preferredProviders.includes(p.providerId) ? p.score * 1.2 : p.score
      }));
      
      // Re-sort after preference boost
      filtered.sort((a, b) => b.score - a.score);
    }

    return filtered;
  }

  private generateSelectionReasoning(provider: ProviderScore, request: ProviderSelectionRequest): string {
    const reasons = [];
    
    if (provider.score > 0.8) {
      reasons.push('High overall performance score');
    }
    
    if (provider.latency < 500) {
      reasons.push('Low latency response time');
    }
    
    if (provider.reliability > 0.95) {
      reasons.push('High reliability rating');
    }
    
    if (provider.cost < 0.01) {
      reasons.push('Cost-effective pricing');
    }

    if (request.preferredProviders && request.preferredProviders.includes(provider.providerId)) {
      reasons.push('Matches user preferences');
    }

    return reasons.length > 0 ? reasons.join(', ') : 'Best available option';
  }

  private async getProviderPerformance(providerId: string): Promise<{
    averageLatency: number;
    averageCost: number;
    reliability: number;
    lastUpdated: Date;
  }> {
    const cacheKey = `provider:performance:${providerId}`;
    const cached = await this.redis.get(cacheKey);
    
    if (cached) {
      return JSON.parse(cached);
    }

    // Default performance data if not cached
    const defaultPerformance = {
      averageLatency: 1000,
      averageCost: 0.01,
      reliability: 0.95,
      lastUpdated: new Date(),
    };

    // Cache the default data
    await this.redis.setex(cacheKey, this.PERFORMANCE_CACHE_TTL, JSON.stringify(defaultPerformance));
    
    return defaultPerformance;
  }

  async updateProviderPerformance(
    providerId: string,
    metrics: {
      latency: number;
      cost: number;
      success: boolean;
    }
  ): Promise<void> {
    try {
      const cacheKey = `provider:performance:${providerId}`;
      const existing = await this.getProviderPerformance(providerId);
      
      // Calculate moving averages
      const alpha = 0.1; // Smoothing factor
      const newLatency = existing.averageLatency * (1 - alpha) + metrics.latency * alpha;
      const newCost = existing.averageCost * (1 - alpha) + metrics.cost * alpha;
      const newReliability = existing.reliability * (1 - alpha) + (metrics.success ? 1 : 0) * alpha;

      const updated = {
        averageLatency: newLatency,
        averageCost: newCost,
        reliability: newReliability,
        lastUpdated: new Date(),
      };

      await this.redis.setex(cacheKey, this.PERFORMANCE_CACHE_TTL, JSON.stringify(updated));
      
      this.logger.debug(`Updated performance metrics for provider ${providerId}`);
    } catch (error) {
      this.logger.error(`Failed to update provider performance: ${error.message}`, error.stack);
    }
  }

  private async performHealthChecks(): Promise<void> {
    try {
      // This would perform actual health checks on providers
      // For now, we'll simulate health check results
      this.logger.debug('Performing provider health checks');
      
      // In a real implementation, this would:
      // 1. Make test requests to each provider
      // 2. Update reliability scores
      // 3. Mark providers as active/inactive
      // 4. Emit health status events
    } catch (error) {
      this.logger.error(`Health check failed: ${error.message}`, error.stack);
    }
  }

  async getProviderRankings(organizationId: string): Promise<ProviderScore[]> {
    const activeProviders = await this.providerManager.getActiveProviders(organizationId);
    
    const scoredProviders = await Promise.all(
      activeProviders.map(provider => this.scoreProvider(provider, {
        organizationId,
        capabilities: ['chat'], // Default capability
      }))
    );

    return scoredProviders.sort((a, b) => b.score - a.score);
  }
}