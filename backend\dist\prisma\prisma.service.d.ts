import { OnModuleInit } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
export declare class PrismaService extends PrismaClient implements OnModuleInit {
    constructor();
    onModuleInit(): Promise<void>;
    onModuleDestroy(): Promise<void>;
    findManyWithOrganization<T>(model: any, organizationId: string, args?: any): Promise<T[]>;
    findUniqueWithOrganization<T>(model: any, organizationId: string, args: any): Promise<T | null>;
    createWithOrganization<T>(model: any, organizationId: string, data: any): Promise<T>;
    updateWithOrganization<T>(model: any, organizationId: string, where: any, data: any): Promise<T>;
    deleteWithOrganization<T>(model: any, organizationId: string, where: any): Promise<T>;
}
