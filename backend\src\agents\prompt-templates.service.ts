import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { ApixGateway } from '../websocket/apix.gateway';
import { 
  CreatePromptTemplateDto, 
  UpdatePromptTemplateDto,
  CreatePromptReviewDto 
} from './dto/agent.dto';

@Injectable()
export class PromptTemplatesService {
  constructor(
    private prisma: PrismaService,
    private apixGateway: ApixGateway,
  ) {}

  async create(userId: string, organizationId: string, dto: CreatePromptTemplateDto) {
    try {
      const template = await this.prisma.promptTemplate.create({
        data: {
          ...dto,
          organizationId,
          createdById: userId,
        },
        include: {
          createdBy: {
            select: { id: true, name: true, email: true }
          },
          agentTemplate: {
            select: { id: true, name: true }
          },
          reviews: {
            include: {
              user: {
                select: { id: true, name: true }
              }
            }
          },
          analytics: {
            orderBy: { date: 'desc' },
            take: 30
          }
        }
      });

      // Emit APIX event
      this.apixGateway.emitToOrganization(organizationId, 'prompt_template_created', {
        template,
        userId,
        timestamp: new Date().toISOString()
      });

      return {
        success: true,
        data: template,
        message: 'Prompt template created successfully'
      };
    } catch (error) {
      throw new BadRequestException(`Failed to create prompt template: ${error.message}`);
    }
  }

  async findAll(organizationId: string, options?: {
    category?: string;
    isPublic?: boolean;
    search?: string;
    agentTemplateId?: string;
    page?: number;
    limit?: number;
    sortBy?: 'name' | 'usage' | 'rating' | 'createdAt';
    sortOrder?: 'asc' | 'desc';
  }) {
    const {
      category,
      isPublic,
      search,
      agentTemplateId,
      page = 1,
      limit = 20,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = options || {};

    const where: any = {
      OR: [
        { organizationId },
        { isPublic: true }
      ]
    };

    if (category) {
      where.category = category;
    }

    if (isPublic !== undefined) {
      where.isPublic = isPublic;
    }

    if (agentTemplateId) {
      where.agentTemplateId = agentTemplateId;
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { content: { contains: search, mode: 'insensitive' } },
        { tags: { hasSome: [search] } }
      ];
    }

    const [templates, total] = await Promise.all([
      this.prisma.promptTemplate.findMany({
        where,
        include: {
          createdBy: {
            select: { id: true, name: true, email: true }
          },
          agentTemplate: {
            select: { id: true, name: true }
          },
          reviews: {
            include: {
              user: {
                select: { id: true, name: true }
              }
            }
          },
          analytics: {
            orderBy: { date: 'desc' },
            take: 7
          }
        },
        orderBy: { [sortBy]: sortOrder },
        skip: (page - 1) * limit,
        take: limit,
      }),
      this.prisma.promptTemplate.count({ where })
    ]);

    return {
      success: true,
      data: {
        templates,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    };
  }

  async findOne(id: string, organizationId: string) {
    const template = await this.prisma.promptTemplate.findFirst({
      where: {
        id,
        OR: [
          { organizationId },
          { isPublic: true }
        ]
      },
      include: {
        createdBy: {
          select: { id: true, name: true, email: true }
        },
        agentTemplate: {
          select: { id: true, name: true }
        },
        reviews: {
          include: {
            user: {
              select: { id: true, name: true }
            }
          },
          orderBy: { createdAt: 'desc' }
        },
        analytics: {
          orderBy: { date: 'desc' },
          take: 30
        },
        optimizations: {
          orderBy: { createdAt: 'desc' },
          take: 10
        },
        parent: {
          select: { id: true, name: true, version: true }
        },
        children: {
          select: { id: true, name: true, version: true }
        }
      }
    });

    if (!template) {
      throw new NotFoundException('Prompt template not found');
    }

    return {
      success: true,
      data: template
    };
  }

  async update(id: string, userId: string, organizationId: string, dto: UpdatePromptTemplateDto) {
    const template = await this.prisma.promptTemplate.findFirst({
      where: { id, organizationId }
    });

    if (!template) {
      throw new NotFoundException('Prompt template not found');
    }

    if (template.createdById !== userId) {
      throw new ForbiddenException('You can only update your own templates');
    }

    try {
      const updatedTemplate = await this.prisma.promptTemplate.update({
        where: { id },
        data: {
          ...dto,
          version: template.version + 1,
        },
        include: {
          createdBy: {
            select: { id: true, name: true, email: true }
          },
          agentTemplate: {
            select: { id: true, name: true }
          },
          reviews: {
            include: {
              user: {
                select: { id: true, name: true }
              }
            }
          },
          analytics: {
            orderBy: { date: 'desc' },
            take: 30
          }
        }
      });

      // Emit APIX event
      this.apixGateway.emitToOrganization(organizationId, 'prompt_template_updated', {
        template: updatedTemplate,
        userId,
        timestamp: new Date().toISOString()
      });

      return {
        success: true,
        data: updatedTemplate,
        message: 'Prompt template updated successfully'
      };
    } catch (error) {
      throw new BadRequestException(`Failed to update prompt template: ${error.message}`);
    }
  }

  async delete(id: string, userId: string, organizationId: string) {
    const template = await this.prisma.promptTemplate.findFirst({
      where: { id, organizationId }
    });

    if (!template) {
      throw new NotFoundException('Prompt template not found');
    }

    if (template.createdById !== userId) {
      throw new ForbiddenException('You can only delete your own templates');
    }

    try {
      await this.prisma.promptTemplate.delete({
        where: { id }
      });

      // Emit APIX event
      this.apixGateway.emitToOrganization(organizationId, 'prompt_template_deleted', {
        templateId: id,
        userId,
        timestamp: new Date().toISOString()
      });

      return {
        success: true,
        message: 'Prompt template deleted successfully'
      };
    } catch (error) {
      throw new BadRequestException(`Failed to delete prompt template: ${error.message}`);
    }
  }

  async duplicate(id: string, userId: string, organizationId: string, name?: string) {
    const originalTemplate = await this.prisma.promptTemplate.findFirst({
      where: {
        id,
        OR: [
          { organizationId },
          { isPublic: true }
        ]
      }
    });

    if (!originalTemplate) {
      throw new NotFoundException('Prompt template not found');
    }

    try {
      const duplicatedTemplate = await this.prisma.promptTemplate.create({
        data: {
          name: name || `${originalTemplate.name} (Copy)`,
          description: originalTemplate.description,
          content: originalTemplate.content,
          variables: originalTemplate.variables,
          category: originalTemplate.category,
          tags: originalTemplate.tags,
          isPublic: false,
          parentId: originalTemplate.id,
          organizationId,
          createdById: userId,
        },
        include: {
          createdBy: {
            select: { id: true, name: true, email: true }
          },
          agentTemplate: {
            select: { id: true, name: true }
          },
          reviews: {
            include: {
              user: {
                select: { id: true, name: true }
              }
            }
          }
        }
      });

      // Emit APIX event
      this.apixGateway.emitToOrganization(organizationId, 'prompt_template_duplicated', {
        originalId: id,
        template: duplicatedTemplate,
        userId,
        timestamp: new Date().toISOString()
      });

      return {
        success: true,
        data: duplicatedTemplate,
        message: 'Prompt template duplicated successfully'
      };
    } catch (error) {
      throw new BadRequestException(`Failed to duplicate prompt template: ${error.message}`);
    }
  }

  async addReview(templateId: string, userId: string, organizationId: string, dto: CreatePromptReviewDto) {
    const template = await this.prisma.promptTemplate.findFirst({
      where: {
        id: templateId,
        OR: [
          { organizationId },
          { isPublic: true }
        ]
      }
    });

    if (!template) {
      throw new NotFoundException('Prompt template not found');
    }

    try {
      const review = await this.prisma.promptReview.upsert({
        where: {
          userId_promptTemplateId: {
            userId,
            promptTemplateId: templateId
          }
        },
        update: {
          rating: dto.rating,
          comment: dto.comment,
        },
        create: {
          rating: dto.rating,
          comment: dto.comment,
          userId,
          promptTemplateId: templateId,
          organizationId,
        },
        include: {
          user: {
            select: { id: true, name: true }
          }
        }
      });

      // Update template rating
      const reviews = await this.prisma.promptReview.findMany({
        where: { promptTemplateId: templateId }
      });

      const avgRating = reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length;

      await this.prisma.promptTemplate.update({
        where: { id: templateId },
        data: { rating: avgRating }
      });

      // Emit APIX event
      this.apixGateway.emitToOrganization(organizationId, 'prompt_template_reviewed', {
        templateId,
        review,
        avgRating,
        userId,
        timestamp: new Date().toISOString()
      });

      return {
        success: true,
        data: review,
        message: 'Review added successfully'
      };
    } catch (error) {
      throw new BadRequestException(`Failed to add review: ${error.message}`);
    }
  }

  async optimizePrompt(id: string, userId: string, organizationId: string) {
    const template = await this.prisma.promptTemplate.findFirst({
      where: { id, organizationId }
    });

    if (!template) {
      throw new NotFoundException('Prompt template not found');
    }

    try {
      // Create optimization record
      const optimization = await this.prisma.promptOptimization.create({
        data: {
          originalContent: template.content,
          optimizedContent: await this.generateOptimizedPrompt(template.content),
          improvement: {
            clarity: 0.15,
            effectiveness: 0.12,
            tokenReduction: 0.08
          },
          status: 'PENDING',
          promptTemplateId: id,
          organizationId,
        }
      });

      // Emit APIX event
      this.apixGateway.emitToOrganization(organizationId, 'prompt_optimized', {
        templateId: id,
        optimization,
        userId,
        timestamp: new Date().toISOString()
      });

      return {
        success: true,
        data: optimization,
        message: 'Prompt optimization generated successfully'
      };
    } catch (error) {
      throw new BadRequestException(`Failed to optimize prompt: ${error.message}`);
    }
  }

  async getCategories(organizationId: string) {
    const categories = await this.prisma.promptTemplate.findMany({
      where: {
        OR: [
          { organizationId },
          { isPublic: true }
        ]
      },
      select: { category: true },
      distinct: ['category']
    });

    return {
      success: true,
      data: categories.map(c => c.category).sort()
    };
  }

  async validateVariables(content: string, variables: any[]) {
    const variablePattern = /\{\{(\w+)\}\}/g;
    const usedVariables = [];
    let match;

    while ((match = variablePattern.exec(content)) !== null) {
      usedVariables.push(match[1]);
    }

    const definedVariables = variables.map(v => v.name);
    const missingVariables = usedVariables.filter(v => !definedVariables.includes(v));
    const unusedVariables = definedVariables.filter(v => !usedVariables.includes(v));

    return {
      success: true,
      data: {
        isValid: missingVariables.length === 0,
        usedVariables,
        definedVariables,
        missingVariables,
        unusedVariables
      }
    };
  }

  async incrementUsage(id: string) {
    try {
      await this.prisma.promptTemplate.update({
        where: { id },
        data: {
          usage: {
            increment: 1
          }
        }
      });
    } catch (error) {
      // Silently fail - usage tracking is not critical
      console.error('Failed to increment prompt template usage:', error);
    }
  }

  private async generateOptimizedPrompt(content: string): Promise<string> {
    // This would integrate with AI providers for actual optimization
    // For now, return a placeholder optimized version
    return `${content}\n\n[Optimized for clarity and effectiveness]`;
  }
}