import { AgentTemplatesService } from './agent-templates.service';
import { CreateAgentTemplateDto } from './dto/agent.dto';
export declare class AgentTemplatesController {
    private readonly templatesService;
    constructor(templatesService: AgentTemplatesService);
    createTemplate(createTemplateDto: CreateAgentTemplateDto, req: any): Promise<import("../database/entities/agent.entity").AgentTemplate>;
    getTemplates(req: any): Promise<import("../database/entities/agent.entity").AgentTemplate[]>;
    getPublicTemplates(): Promise<import("../database/entities/agent.entity").AgentTemplate[]>;
    getTemplatesByCategory(category: string, req: any): Promise<import("../database/entities/agent.entity").AgentTemplate[]>;
    getTemplate(id: string, req: any): Promise<import("../database/entities/agent.entity").AgentTemplate>;
    updateTemplate(id: string, updateData: Partial<CreateAgentTemplateDto>, req: any): Promise<import("../database/entities/agent.entity").AgentTemplate>;
    deleteTemplate(id: string, req: any): Promise<{
        message: string;
    }>;
}
