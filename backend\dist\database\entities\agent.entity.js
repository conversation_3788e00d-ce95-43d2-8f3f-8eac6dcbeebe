"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentCollaboration = exports.Agent = exports.AgentTemplate = exports.ProviderType = exports.AgentStatus = exports.AgentType = void 0;
const typeorm_1 = require("typeorm");
const user_entity_1 = require("../../users/user.entity");
const organization_entity_1 = require("../../organizations/organization.entity");
const agent_execution_entity_1 = require("./agent-execution.entity");
var AgentType;
(function (AgentType) {
    AgentType["BASIC"] = "BASIC";
    AgentType["TOOL_DRIVEN"] = "TOOL_DRIVEN";
    AgentType["HYBRID"] = "HYBRID";
    AgentType["MULTI_TASK"] = "MULTI_TASK";
    AgentType["MULTI_PROVIDER"] = "MULTI_PROVIDER";
    AgentType["COLLABORATIVE"] = "COLLABORATIVE";
})(AgentType || (exports.AgentType = AgentType = {}));
var AgentStatus;
(function (AgentStatus) {
    AgentStatus["ACTIVE"] = "ACTIVE";
    AgentStatus["INACTIVE"] = "INACTIVE";
    AgentStatus["ERROR"] = "ERROR";
    AgentStatus["PAUSED"] = "PAUSED";
})(AgentStatus || (exports.AgentStatus = AgentStatus = {}));
var ProviderType;
(function (ProviderType) {
    ProviderType["OPENAI"] = "OPENAI";
    ProviderType["CLAUDE"] = "CLAUDE";
    ProviderType["GEMINI"] = "GEMINI";
    ProviderType["MISTRAL"] = "MISTRAL";
    ProviderType["GROQ"] = "GROQ";
})(ProviderType || (exports.ProviderType = ProviderType = {}));
let AgentTemplate = class AgentTemplate {
};
exports.AgentTemplate = AgentTemplate;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], AgentTemplate.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], AgentTemplate.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], AgentTemplate.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.Column)('text'),
    __metadata("design:type", String)
], AgentTemplate.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)('jsonb'),
    __metadata("design:type", Object)
], AgentTemplate.prototype, "config", void 0);
__decorate([
    (0, typeorm_1.Column)('text', { array: true }),
    __metadata("design:type", Array)
], AgentTemplate.prototype, "skills", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], AgentTemplate.prototype, "isPublic", void 0);
__decorate([
    (0, typeorm_1.Column)('text'),
    __metadata("design:type", String)
], AgentTemplate.prototype, "promptTemplate", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: AgentType,
        default: AgentType.BASIC
    }),
    __metadata("design:type", String)
], AgentTemplate.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)('enum', { enum: ProviderType, array: true }),
    __metadata("design:type", Array)
], AgentTemplate.prototype, "supportedProviders", void 0);
__decorate([
    (0, typeorm_1.Column)('jsonb', { nullable: true }),
    __metadata("design:type", Object)
], AgentTemplate.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)('uuid'),
    __metadata("design:type", String)
], AgentTemplate.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'createdBy' }),
    __metadata("design:type", user_entity_1.User)
], AgentTemplate.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.Column)('uuid'),
    __metadata("design:type", String)
], AgentTemplate.prototype, "organizationId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => organization_entity_1.Organization),
    (0, typeorm_1.JoinColumn)({ name: 'organizationId' }),
    __metadata("design:type", typeof (_a = typeof organization_entity_1.Organization !== "undefined" && organization_entity_1.Organization) === "function" ? _a : Object)
], AgentTemplate.prototype, "organization", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => Agent, agent => agent.template),
    __metadata("design:type", Array)
], AgentTemplate.prototype, "instances", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], AgentTemplate.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], AgentTemplate.prototype, "updatedAt", void 0);
exports.AgentTemplate = AgentTemplate = __decorate([
    (0, typeorm_1.Entity)('agent_templates')
], AgentTemplate);
let Agent = class Agent {
};
exports.Agent = Agent;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Agent.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Agent.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)('uuid'),
    __metadata("design:type", String)
], Agent.prototype, "templateId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => AgentTemplate, template => template.instances),
    (0, typeorm_1.JoinColumn)({ name: 'templateId' }),
    __metadata("design:type", AgentTemplate)
], Agent.prototype, "template", void 0);
__decorate([
    (0, typeorm_1.Column)('jsonb'),
    __metadata("design:type", Object)
], Agent.prototype, "config", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: AgentType,
        default: AgentType.BASIC
    }),
    __metadata("design:type", String)
], Agent.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: AgentStatus,
        default: AgentStatus.ACTIVE
    }),
    __metadata("design:type", String)
], Agent.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ProviderType
    }),
    __metadata("design:type", String)
], Agent.prototype, "primaryProvider", void 0);
__decorate([
    (0, typeorm_1.Column)('enum', { enum: ProviderType, array: true, nullable: true }),
    __metadata("design:type", Array)
], Agent.prototype, "fallbackProviders", void 0);
__decorate([
    (0, typeorm_1.Column)('jsonb', { nullable: true }),
    __metadata("design:type", Object)
], Agent.prototype, "memoryConfig", void 0);
__decorate([
    (0, typeorm_1.Column)('text', { array: true, nullable: true }),
    __metadata("design:type", Array)
], Agent.prototype, "skills", void 0);
__decorate([
    (0, typeorm_1.Column)('jsonb', { nullable: true }),
    __metadata("design:type", Object)
], Agent.prototype, "performanceMetrics", void 0);
__decorate([
    (0, typeorm_1.Column)('uuid'),
    __metadata("design:type", String)
], Agent.prototype, "organizationId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => organization_entity_1.Organization),
    (0, typeorm_1.JoinColumn)({ name: 'organizationId' }),
    __metadata("design:type", typeof (_b = typeof organization_entity_1.Organization !== "undefined" && organization_entity_1.Organization) === "function" ? _b : Object)
], Agent.prototype, "organization", void 0);
__decorate([
    (0, typeorm_1.Column)('uuid'),
    __metadata("design:type", String)
], Agent.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'createdBy' }),
    __metadata("design:type", user_entity_1.User)
], Agent.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => agent_execution_entity_1.AgentExecution, execution => execution.agent),
    __metadata("design:type", Array)
], Agent.prototype, "executions", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], Agent.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], Agent.prototype, "updatedAt", void 0);
exports.Agent = Agent = __decorate([
    (0, typeorm_1.Entity)('agents')
], Agent);
let AgentCollaboration = class AgentCollaboration {
};
exports.AgentCollaboration = AgentCollaboration;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], AgentCollaboration.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], AgentCollaboration.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)('uuid', { array: true }),
    __metadata("design:type", Array)
], AgentCollaboration.prototype, "agentIds", void 0);
__decorate([
    (0, typeorm_1.Column)('uuid'),
    __metadata("design:type", String)
], AgentCollaboration.prototype, "coordinatorId", void 0);
__decorate([
    (0, typeorm_1.Column)('jsonb'),
    __metadata("design:type", Object)
], AgentCollaboration.prototype, "workflow", void 0);
__decorate([
    (0, typeorm_1.Column)('jsonb', { nullable: true }),
    __metadata("design:type", Object)
], AgentCollaboration.prototype, "sharedContext", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['ACTIVE', 'PAUSED', 'COMPLETED', 'FAILED'],
        default: 'ACTIVE'
    }),
    __metadata("design:type", String)
], AgentCollaboration.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)('uuid'),
    __metadata("design:type", String)
], AgentCollaboration.prototype, "organizationId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => organization_entity_1.Organization),
    (0, typeorm_1.JoinColumn)({ name: 'organizationId' }),
    __metadata("design:type", typeof (_c = typeof organization_entity_1.Organization !== "undefined" && organization_entity_1.Organization) === "function" ? _c : Object)
], AgentCollaboration.prototype, "organization", void 0);
__decorate([
    (0, typeorm_1.Column)('uuid'),
    __metadata("design:type", String)
], AgentCollaboration.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'createdBy' }),
    __metadata("design:type", user_entity_1.User)
], AgentCollaboration.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], AgentCollaboration.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], AgentCollaboration.prototype, "updatedAt", void 0);
exports.AgentCollaboration = AgentCollaboration = __decorate([
    (0, typeorm_1.Entity)('agent_collaborations')
], AgentCollaboration);
//# sourceMappingURL=agent.entity.js.map