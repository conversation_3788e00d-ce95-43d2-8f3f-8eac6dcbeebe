import { Repository } from 'typeorm';
import Redis from 'ioredis';
import { AIProvider, AIModel, ProviderUsage } from '../database/entities/ai-provider.entity';
import { ApixGateway } from '../websocket/apix.gateway';
import { CreateAIProviderDto, UpdateAIProviderDto } from './dto/ai-provider.dto';
export declare class AIProviderManagerService {
    private providerRepository;
    private modelRepository;
    private usageRepository;
    private readonly redis;
    private apixGateway;
    private readonly logger;
    constructor(providerRepository: Repository<AIProvider>, modelRepository: Repository<AIModel>, usageRepository: Repository<ProviderUsage>, redis: Redis, apixGateway: ApixGateway);
    createProvider(createProviderDto: CreateAIProviderDto, organizationId: string, userId: string): Promise<AIProvider>;
    updateProvider(providerId: string, updateProviderDto: UpdateAIProviderDto, organizationId: string): Promise<AIProvider>;
    deleteProvider(providerId: string, organizationId: string): Promise<void>;
    getProvidersByOrganization(organizationId: string): Promise<AIProvider[]>;
    getProviderById(providerId: string, organizationId: string): Promise<AIProvider>;
    getActiveProviders(organizationId: string): Promise<AIProvider[]>;
    trackUsage(providerId: string, usage: {
        requests: number;
        tokensUsed: number;
        costInCents: number;
    }): Promise<void>;
    getUsageStats(organizationId: string, startDate?: Date, endDate?: Date): Promise<{
        totalRequests: number;
        totalTokens: number;
        totalCostInCents: number;
        providerBreakdown: Array<{
            providerId: string;
            providerName: string;
            requests: number;
            tokens: number;
            costInCents: number;
        }>;
    }>;
    private validateProviderConfig;
    private cacheProviderConfig;
    getCachedProvider(providerId: string): Promise<any>;
}
