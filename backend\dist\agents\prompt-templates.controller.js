"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PromptTemplatesController = void 0;
const common_1 = require("@nestjs/common");
const jwt_auth_guard_1 = require("../auth/jwt-auth.guard");
const permission_guard_1 = require("../auth/permission.guard");
const permissions_decorator_1 = require("../auth/permissions.decorator");
const prompt_templates_service_1 = require("./prompt-templates.service");
const agent_dto_1 = require("./dto/agent.dto");
let PromptTemplatesController = class PromptTemplatesController {
    constructor(promptTemplatesService) {
        this.promptTemplatesService = promptTemplatesService;
    }
    async create(req, createDto) {
        try {
            const result = await this.promptTemplatesService.create(req.user.id, req.user.organizationId, createDto);
            return Object.assign({ statusCode: common_1.HttpStatus.CREATED }, result);
        }
        catch (error) {
            return {
                statusCode: common_1.HttpStatus.BAD_REQUEST,
                success: false,
                message: error.message
            };
        }
    }
    async findAll(req, category, isPublic, search, agentTemplateId, page, limit, sortBy, sortOrder) {
        try {
            const result = await this.promptTemplatesService.findAll(req.user.organizationId, {
                category,
                isPublic: isPublic === 'true',
                search,
                agentTemplateId,
                page: page ? parseInt(page) : undefined,
                limit: limit ? parseInt(limit) : undefined,
                sortBy,
                sortOrder
            });
            return Object.assign({ statusCode: common_1.HttpStatus.OK }, result);
        }
        catch (error) {
            return {
                statusCode: common_1.HttpStatus.BAD_REQUEST,
                success: false,
                message: error.message
            };
        }
    }
    async getCategories(req) {
        try {
            const result = await this.promptTemplatesService.getCategories(req.user.organizationId);
            return Object.assign({ statusCode: common_1.HttpStatus.OK }, result);
        }
        catch (error) {
            return {
                statusCode: common_1.HttpStatus.BAD_REQUEST,
                success: false,
                message: error.message
            };
        }
    }
    async findOne(req, id) {
        try {
            const result = await this.promptTemplatesService.findOne(id, req.user.organizationId);
            return Object.assign({ statusCode: common_1.HttpStatus.OK }, result);
        }
        catch (error) {
            return {
                statusCode: common_1.HttpStatus.NOT_FOUND,
                success: false,
                message: error.message
            };
        }
    }
    async update(req, id, updateDto) {
        try {
            const result = await this.promptTemplatesService.update(id, req.user.id, req.user.organizationId, updateDto);
            return Object.assign({ statusCode: common_1.HttpStatus.OK }, result);
        }
        catch (error) {
            return {
                statusCode: common_1.HttpStatus.BAD_REQUEST,
                success: false,
                message: error.message
            };
        }
    }
    async delete(req, id) {
        try {
            const result = await this.promptTemplatesService.delete(id, req.user.id, req.user.organizationId);
            return Object.assign({ statusCode: common_1.HttpStatus.OK }, result);
        }
        catch (error) {
            return {
                statusCode: common_1.HttpStatus.BAD_REQUEST,
                success: false,
                message: error.message
            };
        }
    }
    async duplicate(req, id, name) {
        try {
            const result = await this.promptTemplatesService.duplicate(id, req.user.id, req.user.organizationId, name);
            return Object.assign({ statusCode: common_1.HttpStatus.CREATED }, result);
        }
        catch (error) {
            return {
                statusCode: common_1.HttpStatus.BAD_REQUEST,
                success: false,
                message: error.message
            };
        }
    }
    async addReview(req, id, reviewDto) {
        try {
            const result = await this.promptTemplatesService.addReview(id, req.user.id, req.user.organizationId, reviewDto);
            return Object.assign({ statusCode: common_1.HttpStatus.CREATED }, result);
        }
        catch (error) {
            return {
                statusCode: common_1.HttpStatus.BAD_REQUEST,
                success: false,
                message: error.message
            };
        }
    }
    async optimizePrompt(req, id) {
        try {
            const result = await this.promptTemplatesService.optimizePrompt(id, req.user.id, req.user.organizationId);
            return Object.assign({ statusCode: common_1.HttpStatus.OK }, result);
        }
        catch (error) {
            return {
                statusCode: common_1.HttpStatus.BAD_REQUEST,
                success: false,
                message: error.message
            };
        }
    }
    async validateVariables(req, id, body) {
        try {
            const result = await this.promptTemplatesService.validateVariables(body.content, body.variables);
            return Object.assign({ statusCode: common_1.HttpStatus.OK }, result);
        }
        catch (error) {
            return {
                statusCode: common_1.HttpStatus.BAD_REQUEST,
                success: false,
                message: error.message
            };
        }
    }
};
exports.PromptTemplatesController = PromptTemplatesController;
__decorate([
    (0, common_1.Post)(),
    (0, permissions_decorator_1.Permissions)('prompt_templates:create'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, typeof (_a = typeof agent_dto_1.CreatePromptTemplateDto !== "undefined" && agent_dto_1.CreatePromptTemplateDto) === "function" ? _a : Object]),
    __metadata("design:returntype", Promise)
], PromptTemplatesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, permissions_decorator_1.Permissions)('prompt_templates:read'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)('category')),
    __param(2, (0, common_1.Query)('isPublic')),
    __param(3, (0, common_1.Query)('search')),
    __param(4, (0, common_1.Query)('agentTemplateId')),
    __param(5, (0, common_1.Query)('page')),
    __param(6, (0, common_1.Query)('limit')),
    __param(7, (0, common_1.Query)('sortBy')),
    __param(8, (0, common_1.Query)('sortOrder')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String, String, String, String, String, String, String]),
    __metadata("design:returntype", Promise)
], PromptTemplatesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('categories'),
    (0, permissions_decorator_1.Permissions)('prompt_templates:read'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PromptTemplatesController.prototype, "getCategories", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, permissions_decorator_1.Permissions)('prompt_templates:read'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], PromptTemplatesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, permissions_decorator_1.Permissions)('prompt_templates:update'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, typeof (_b = typeof agent_dto_1.UpdatePromptTemplateDto !== "undefined" && agent_dto_1.UpdatePromptTemplateDto) === "function" ? _b : Object]),
    __metadata("design:returntype", Promise)
], PromptTemplatesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, permissions_decorator_1.Permissions)('prompt_templates:delete'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], PromptTemplatesController.prototype, "delete", null);
__decorate([
    (0, common_1.Post)(':id/duplicate'),
    (0, permissions_decorator_1.Permissions)('prompt_templates:create'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Body)('name')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String]),
    __metadata("design:returntype", Promise)
], PromptTemplatesController.prototype, "duplicate", null);
__decorate([
    (0, common_1.Post)(':id/reviews'),
    (0, permissions_decorator_1.Permissions)('prompt_templates:review'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, typeof (_c = typeof agent_dto_1.CreatePromptReviewDto !== "undefined" && agent_dto_1.CreatePromptReviewDto) === "function" ? _c : Object]),
    __metadata("design:returntype", Promise)
], PromptTemplatesController.prototype, "addReview", null);
__decorate([
    (0, common_1.Post)(':id/optimize'),
    (0, permissions_decorator_1.Permissions)('prompt_templates:optimize'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], PromptTemplatesController.prototype, "optimizePrompt", null);
__decorate([
    (0, common_1.Post)(':id/validate'),
    (0, permissions_decorator_1.Permissions)('prompt_templates:read'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Object]),
    __metadata("design:returntype", Promise)
], PromptTemplatesController.prototype, "validateVariables", null);
exports.PromptTemplatesController = PromptTemplatesController = __decorate([
    (0, common_1.Controller)('api/v1/prompt-templates'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, permission_guard_1.PermissionGuard),
    __metadata("design:paramtypes", [prompt_templates_service_1.PromptTemplatesService])
], PromptTemplatesController);
//# sourceMappingURL=prompt-templates.controller.js.map