"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolExecution = exports.ExecutionStatus = void 0;
const typeorm_1 = require("typeorm");
const tool_entity_1 = require("./tool.entity");
const session_entity_1 = require("./session.entity");
var ExecutionStatus;
(function (ExecutionStatus) {
    ExecutionStatus["PENDING"] = "PENDING";
    ExecutionStatus["RUNNING"] = "RUNNING";
    ExecutionStatus["COMPLETED"] = "COMPLETED";
    ExecutionStatus["FAILED"] = "FAILED";
    ExecutionStatus["TIMEOUT"] = "TIMEOUT";
})(ExecutionStatus || (exports.ExecutionStatus = ExecutionStatus = {}));
let ToolExecution = class ToolExecution {
};
exports.ToolExecution = ToolExecution;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ToolExecution.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'enum', enum: ExecutionStatus, default: ExecutionStatus.PENDING }),
    __metadata("design:type", String)
], ToolExecution.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb' }),
    __metadata("design:type", Object)
], ToolExecution.prototype, "input", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ToolExecution.prototype, "output", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', default: {} }),
    __metadata("design:type", Object)
], ToolExecution.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ToolExecution.prototype, "error", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Date)
], ToolExecution.prototype, "startedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Date)
], ToolExecution.prototype, "completedAt", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ToolExecution.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => tool_entity_1.Tool, tool => tool.executions),
    __metadata("design:type", tool_entity_1.Tool)
], ToolExecution.prototype, "tool", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], ToolExecution.prototype, "toolId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => session_entity_1.Session),
    __metadata("design:type", session_entity_1.Session)
], ToolExecution.prototype, "session", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], ToolExecution.prototype, "sessionId", void 0);
exports.ToolExecution = ToolExecution = __decorate([
    (0, typeorm_1.Entity)('tool_executions')
], ToolExecution);
//# sourceMappingURL=tool-execution.entity.js.map