"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PrismaService = void 0;
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
let PrismaService = class PrismaService extends client_1.PrismaClient {
    constructor() {
        super({
            log: ['query', 'info', 'warn', 'error'],
        });
    }
    async onModuleInit() {
        await this.$connect();
    }
    async onModuleDestroy() {
        await this.$disconnect();
    }
    async findManyWithOrganization(model, organizationId, args) {
        return model.findMany(Object.assign(Object.assign({}, args), { where: Object.assign(Object.assign({}, args === null || args === void 0 ? void 0 : args.where), { organizationId }) }));
    }
    async findUniqueWithOrganization(model, organizationId, args) {
        return model.findUnique(Object.assign(Object.assign({}, args), { where: Object.assign(Object.assign({}, args.where), { organizationId }) }));
    }
    async createWithOrganization(model, organizationId, data) {
        return model.create({
            data: Object.assign(Object.assign({}, data), { organizationId }),
        });
    }
    async updateWithOrganization(model, organizationId, where, data) {
        return model.update({
            where: Object.assign(Object.assign({}, where), { organizationId }),
            data,
        });
    }
    async deleteWithOrganization(model, organizationId, where) {
        return model.delete({
            where: Object.assign(Object.assign({}, where), { organizationId }),
        });
    }
};
exports.PrismaService = PrismaService;
exports.PrismaService = PrismaService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], PrismaService);
//# sourceMappingURL=prisma.service.js.map