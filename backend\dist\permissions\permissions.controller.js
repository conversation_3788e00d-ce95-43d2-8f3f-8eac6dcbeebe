"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PermissionController = void 0;
const common_1 = require("@nestjs/common");
const permissions_service_1 = require("./permissions.service");
const jwt_auth_guard_1 = require("../auth/jwt-auth.guard");
const permission_guard_1 = require("../auth/permission.guard");
const permissions_decorator_1 = require("../auth/permissions.decorator");
const swagger_1 = require("@nestjs/swagger");
let PermissionController = class PermissionController {
    constructor(permissionService) {
        this.permissionService = permissionService;
    }
    async createPermission(createPermissionDto, req) {
        createPermissionDto.organizationId = req.user.organizationId;
        return this.permissionService.createPermission(createPermissionDto, req.user.userId);
    }
    async getPermissions(filters, req) {
        filters.organizationId = req.user.organizationId;
        return this.permissionService.getPermissions(filters);
    }
    async getResourceList(req) {
        return this.permissionService.getResourceList(req.user.organizationId);
    }
    async getActionList(resource, req) {
        return this.permissionService.getActionList(req.user.organizationId, resource);
    }
    async getPermissionById(id) {
        return this.permissionService.getPermissionById(id);
    }
    async updatePermission(id, updatePermissionDto, req) {
        return this.permissionService.updatePermission(id, updatePermissionDto, req.user.userId);
    }
    async deletePermission(id, req) {
        return this.permissionService.deletePermission(id, req.user.userId);
    }
    async assignPermissionToUser(permissionId, userId, req) {
        return this.permissionService.assignPermissionToUser(permissionId, userId, req.user.userId);
    }
    async removePermissionFromUser(permissionId, userId, req) {
        return this.permissionService.removePermissionFromUser(permissionId, userId, req.user.userId);
    }
};
exports.PermissionController = PermissionController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(permission_guard_1.PermissionGuard),
    (0, permissions_decorator_1.RequirePermissions)('permissions.write'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new permission' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], PermissionController.prototype, "createPermission", null);
__decorate([
    (0, common_1.Get)(),
    (0, common_1.UseGuards)(permission_guard_1.PermissionGuard),
    (0, permissions_decorator_1.RequirePermissions)('permissions.read'),
    (0, swagger_1.ApiOperation)({ summary: 'Get permissions with filtering and pagination' }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], PermissionController.prototype, "getPermissions", null);
__decorate([
    (0, common_1.Get)('resources'),
    (0, common_1.UseGuards)(permission_guard_1.PermissionGuard),
    (0, permissions_decorator_1.RequirePermissions)('permissions.read'),
    (0, swagger_1.ApiOperation)({ summary: 'Get list of resources' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PermissionController.prototype, "getResourceList", null);
__decorate([
    (0, common_1.Get)('actions'),
    (0, common_1.UseGuards)(permission_guard_1.PermissionGuard),
    (0, permissions_decorator_1.RequirePermissions)('permissions.read'),
    (0, swagger_1.ApiOperation)({ summary: 'Get list of actions' }),
    __param(0, (0, common_1.Query)('resource')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], PermissionController.prototype, "getActionList", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, common_1.UseGuards)(permission_guard_1.PermissionGuard),
    (0, permissions_decorator_1.RequirePermissions)('permissions.read'),
    (0, swagger_1.ApiOperation)({ summary: 'Get permission by ID' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PermissionController.prototype, "getPermissionById", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, common_1.UseGuards)(permission_guard_1.PermissionGuard),
    (0, permissions_decorator_1.RequirePermissions)('permissions.write'),
    (0, swagger_1.ApiOperation)({ summary: 'Update permission' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], PermissionController.prototype, "updatePermission", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(permission_guard_1.PermissionGuard),
    (0, permissions_decorator_1.RequirePermissions)('permissions.write'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete permission' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], PermissionController.prototype, "deletePermission", null);
__decorate([
    (0, common_1.Post)(':permissionId/users/:userId'),
    (0, common_1.UseGuards)(permission_guard_1.PermissionGuard),
    (0, permissions_decorator_1.RequirePermissions)('permissions.write'),
    (0, swagger_1.ApiOperation)({ summary: 'Assign permission to user' }),
    __param(0, (0, common_1.Param)('permissionId')),
    __param(1, (0, common_1.Param)('userId')),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], PermissionController.prototype, "assignPermissionToUser", null);
__decorate([
    (0, common_1.Delete)(':permissionId/users/:userId'),
    (0, common_1.UseGuards)(permission_guard_1.PermissionGuard),
    (0, permissions_decorator_1.RequirePermissions)('permissions.write'),
    (0, swagger_1.ApiOperation)({ summary: 'Remove permission from user' }),
    __param(0, (0, common_1.Param)('permissionId')),
    __param(1, (0, common_1.Param)('userId')),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], PermissionController.prototype, "removePermissionFromUser", null);
exports.PermissionController = PermissionController = __decorate([
    (0, swagger_1.ApiTags)('permissions'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('api/permissions'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [permissions_service_1.PermissionService])
], PermissionController);
//# sourceMappingURL=permissions.controller.js.map