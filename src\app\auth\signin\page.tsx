'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { 
  Eye, 
  EyeOff, 
  Mail, 
  Lock, 
  ArrowLeft, 
  Sun, 
  Moon, 
  Monitor,
  Chrome,
  Github,
  Loader2,
  AlertCircle,
  CheckCircle2,
  Brain,
  Shield,
  Zap
} from 'lucide-react';
import { toast } from 'sonner';

export default function SignInPage() {
  const router = useRouter();
  const { login } = useAuth();
  const { theme, setTheme } = useTheme();
  
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false,
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Email validation
    if (!formData.email.trim()) {
      newErrors.email = 'Email address is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters long';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Clear previous errors
    setErrors({});
    
    if (!validateForm()) {
      toast.error('Please fix the errors below', {
        description: 'Check your email and password fields'
      });
      return;
    }

    setLoading(true);
    try {
      await login(formData.email, formData.password);
      
      toast.success('Welcome back!', {
        description: 'Successfully signed in to your account'
      });
      
      // Small delay for better UX
      setTimeout(() => {
        router.push('/dashboard');
      }, 500);
      
    } catch (error: any) {
      console.error('Login error:', error);
      
      // Handle specific error cases
      if (error.message.includes('Invalid credentials') || error.message.includes('Unauthorized')) {
        setErrors({ 
          email: 'Invalid email or password',
          password: 'Invalid email or password'
        });
        toast.error('Invalid credentials', {
          description: 'Please check your email and password'
        });
      } else if (error.message.includes('Account not found')) {
        setErrors({ email: 'No account found with this email address' });
        toast.error('Account not found', {
          description: 'Please check your email or sign up for a new account'
        });
      } else if (error.message.includes('Account suspended')) {
        toast.error('Account suspended', {
          description: 'Please contact support for assistance'
        });
      } else {
        toast.error('Sign in failed', {
          description: error.message || 'An unexpected error occurred. Please try again.'
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSocialLogin = async (provider: 'google' | 'github') => {
    toast.info(`${provider.charAt(0).toUpperCase() + provider.slice(1)} sign in`, {
      description: 'Social login will be available soon'
    });
  };

  const cycleTheme = () => {
    const themes: Array<'light' | 'dark' | 'system'> = ['light', 'dark', 'system'];
    const currentIndex = themes.indexOf(theme);
    const nextIndex = (currentIndex + 1) % themes.length;
    setTheme(themes[nextIndex]);
  };

  const getThemeIcon = () => {
    switch (theme) {
      case 'light': return <Sun className="w-4 h-4" />;
      case 'dark': return <Moon className="w-4 h-4" />;
      case 'system': return <Monitor className="w-4 h-4" />;
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear field-specific error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <Link 
            href="/" 
            className="flex items-center space-x-2 text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-100 transition-colors group"
          >
            <ArrowLeft className="w-4 h-4 group-hover:-translate-x-1 transition-transform" />
            <span>Back to Home</span>
          </Link>
          
          <Button
            variant="outline"
            size="sm"
            onClick={cycleTheme}
            className="backdrop-blur-sm bg-white/50 dark:bg-slate-800/50 border-white/20 dark:border-slate-700/50 hover:bg-white/70 dark:hover:bg-slate-800/70"
          >
            {getThemeIcon()}
            <span className="ml-2 capitalize">{theme}</span>
          </Button>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
          {/* Left Column - Branding */}
          <div className="flex flex-col justify-center space-y-8 lg:pr-8">
            <div className="space-y-6">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Brain className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                    SynapseAI
                  </h1>
                  <p className="text-slate-600 dark:text-slate-400">Universal AI Orchestration Platform</p>
                </div>
              </div>

              <div className="space-y-4">
                <h2 className="text-2xl font-semibold text-slate-900 dark:text-slate-100">
                  Welcome back to the future of AI automation
                </h2>
                <p className="text-lg text-slate-600 dark:text-slate-400 leading-relaxed">
                  Sign in to access your AI agents, tools, and workflows. Continue building 
                  intelligent automation that transforms your business operations and competes 
                  with industry giants like Zapier and Microsoft Power Automate.
                </p>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="flex items-center space-x-3 p-4 rounded-lg backdrop-blur-sm bg-white/30 dark:bg-slate-800/30 border border-white/20 dark:border-slate-700/50 hover:bg-white/40 dark:hover:bg-slate-800/40 transition-colors">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                    <Shield className="w-4 h-4 text-white" />
                  </div>
                  <div>
                    <p className="font-medium text-slate-900 dark:text-slate-100">Enterprise Security</p>
                    <p className="text-sm text-slate-600 dark:text-slate-400">SOC 2 Type II compliant</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3 p-4 rounded-lg backdrop-blur-sm bg-white/30 dark:bg-slate-800/30 border border-white/20 dark:border-slate-700/50 hover:bg-white/40 dark:hover:bg-slate-800/40 transition-colors">
                  <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                    <Zap className="w-4 h-4 text-white" />
                  </div>
                  <div>
                    <p className="font-medium text-slate-900 dark:text-slate-100">Real-time Sync</p>
                    <p className="text-sm text-slate-600 dark:text-slate-400">APIX Protocol</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3 p-4 rounded-lg backdrop-blur-sm bg-white/30 dark:bg-slate-800/30 border border-white/20 dark:border-slate-700/50 hover:bg-white/40 dark:hover:bg-slate-800/40 transition-colors">
                  <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                    <CheckCircle2 className="w-4 h-4 text-white" />
                  </div>
                  <div>
                    <p className="font-medium text-slate-900 dark:text-slate-100">99.9% Uptime</p>
                    <p className="text-sm text-slate-600 dark:text-slate-400">Enterprise SLA</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3 p-4 rounded-lg backdrop-blur-sm bg-white/30 dark:bg-slate-800/30 border border-white/20 dark:border-slate-700/50 hover:bg-white/40 dark:hover:bg-slate-800/40 transition-colors">
                  <div className="w-8 h-8 bg-indigo-500 rounded-full flex items-center justify-center">
                    <CheckCircle2 className="w-4 h-4 text-white" />
                  </div>
                  <div>
                    <p className="font-medium text-slate-900 dark:text-slate-100">Multi-tenant</p>
                    <p className="text-sm text-slate-600 dark:text-slate-400">Isolated & secure</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <p className="text-sm text-slate-500 dark:text-slate-400">Trusted by 10,000+ organizations worldwide</p>
              <div className="flex items-center space-x-6 opacity-60">
                <div className="w-20 h-8 bg-slate-300 dark:bg-slate-600 rounded flex items-center justify-center">
                  <span className="text-xs font-medium">TechFlow</span>
                </div>
                <div className="w-16 h-8 bg-slate-300 dark:bg-slate-600 rounded flex items-center justify-center">
                  <span className="text-xs font-medium">DataCorp</span>
                </div>
                <div className="w-24 h-8 bg-slate-300 dark:bg-slate-600 rounded flex items-center justify-center">
                  <span className="text-xs font-medium">InnovateLab</span>
                </div>
                <div className="w-18 h-8 bg-slate-300 dark:bg-slate-600 rounded flex items-center justify-center">
                  <span className="text-xs font-medium">ScaleUp</span>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Sign In Form */}
          <div className="flex flex-col justify-center">
            <Card className="backdrop-blur-xl bg-white/80 dark:bg-slate-800/80 border-white/20 dark:border-slate-700/50 shadow-2xl">
              <CardHeader className="space-y-1 pb-6">
                <CardTitle className="text-2xl font-bold text-center">Sign in to your account</CardTitle>
                <CardDescription className="text-center">
                  Enter your credentials to access your SynapseAI workspace
                </CardDescription>
              </CardHeader>

              <CardContent className="space-y-6">
                {/* Social Login Buttons */}
                <div className="grid grid-cols-2 gap-3">
                  <Button
                    variant="outline"
                    onClick={() => handleSocialLogin('google')}
                    className="backdrop-blur-sm bg-white/50 dark:bg-slate-800/50 border-white/20 dark:border-slate-700/50 hover:bg-white/70 dark:hover:bg-slate-800/70"
                    disabled={loading}
                  >
                    <Chrome className="w-4 h-4 mr-2" />
                    Google
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => handleSocialLogin('github')}
                    className="backdrop-blur-sm bg-white/50 dark:bg-slate-800/50 border-white/20 dark:border-slate-700/50 hover:bg-white/70 dark:hover:bg-slate-800/70"
                    disabled={loading}
                  >
                    <Github className="w-4 h-4 mr-2" />
                    GitHub
                  </Button>
                </div>

                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <Separator className="w-full" />
                  </div>
                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="bg-white dark:bg-slate-800 px-2 text-slate-500 dark:text-slate-400">
                      Or continue with email
                    </span>
                  </div>
                </div>

                {/* Sign In Form */}
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="email" className="text-sm font-medium">
                      Email address
                    </Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
                      <Input
                        id="email"
                        type="email"
                        placeholder="Enter your email address"
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        className={`pl-10 backdrop-blur-sm bg-white/50 dark:bg-slate-900/50 transition-colors ${
                          errors.email 
                            ? 'border-red-500 focus:border-red-500 focus:ring-red-500/20' 
                            : 'focus:border-indigo-500 focus:ring-indigo-500/20'
                        }`}
                        disabled={loading}
                        autoComplete="email"
                      />
                    </div>
                    {errors.email && (
                      <div className="flex items-center space-x-1 text-red-600 text-sm animate-in slide-in-from-left-1">
                        <AlertCircle className="w-3 h-3" />
                        <span>{errors.email}</span>
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="password" className="text-sm font-medium">
                      Password
                    </Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
                      <Input
                        id="password"
                        type={showPassword ? 'text' : 'password'}
                        placeholder="Enter your password"
                        value={formData.password}
                        onChange={(e) => handleInputChange('password', e.target.value)}
                        className={`pl-10 pr-10 backdrop-blur-sm bg-white/50 dark:bg-slate-900/50 transition-colors ${
                          errors.password 
                            ? 'border-red-500 focus:border-red-500 focus:ring-red-500/20' 
                            : 'focus:border-indigo-500 focus:ring-indigo-500/20'
                        }`}
                        disabled={loading}
                        autoComplete="current-password"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 hover:bg-transparent"
                        onClick={() => setShowPassword(!showPassword)}
                        disabled={loading}
                      >
                        {showPassword ? (
                          <EyeOff className="w-4 h-4 text-slate-400" />
                        ) : (
                          <Eye className="w-4 h-4 text-slate-400" />
                        )}
                      </Button>
                    </div>
                    {errors.password && (
                      <div className="flex items-center space-x-1 text-red-600 text-sm animate-in slide-in-from-left-1">
                        <AlertCircle className="w-3 h-3" />
                        <span>{errors.password}</span>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <input
                        id="remember"
                        type="checkbox"
                        checked={formData.rememberMe}
                        onChange={(e) => handleInputChange('rememberMe', e.target.checked)}
                        className="w-4 h-4 text-indigo-600 bg-white dark:bg-slate-800 border-slate-300 dark:border-slate-600 rounded focus:ring-indigo-500 focus:ring-2"
                        disabled={loading}
                      />
                      <Label htmlFor="remember" className="text-sm text-slate-600 dark:text-slate-400 cursor-pointer">
                        Remember me for 30 days
                      </Label>
                    </div>
                    <Link
                      href="/auth/forgot-password"
                      className="text-sm text-indigo-600 dark:text-indigo-400 hover:text-indigo-500 dark:hover:text-indigo-300 transition-colors"
                    >
                      Forgot password?
                    </Link>
                  </div>

                  <Button
                    type="submit"
                    className="w-full bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white font-medium py-2.5 transition-all duration-200"
                    disabled={loading}
                  >
                    {loading ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Signing in...
                      </>
                    ) : (
                      'Sign in to SynapseAI'
                    )}
                  </Button>
                </form>

                <div className="text-center">
                  <p className="text-sm text-slate-600 dark:text-slate-400">
                    Don't have an account?{' '}
                    <Link
                      href="/auth/signup"
                      className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-500 dark:hover:text-indigo-300 font-medium transition-colors"
                    >
                      Sign up for free
                    </Link>
                  </p>
                </div>

                <div className="pt-4 border-t border-slate-200 dark:border-slate-700">
                  <p className="text-xs text-center text-slate-500 dark:text-slate-400">
                    By signing in, you agree to our{' '}
                    <Link href="/terms" className="text-indigo-600 dark:text-indigo-400 hover:underline">
                      Terms of Service
                    </Link>{' '}
                    and{' '}
                    <Link href="/privacy" className="text-indigo-600 dark:text-indigo-400 hover:underline">
                      Privacy Policy
                    </Link>
                    . Protected by enterprise-grade security.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}