import { AgentOrchestratorService } from './agent-orchestrator.service';
import { AgentAnalyticsService } from './agent-analytics.service';
import { SessionMemoryService } from './session-memory.service';
import { CreateAgentInstanceDto, UpdateAgentInstanceDto, ExecuteAgentDto } from './dto/agent.dto';
export declare class AgentsController {
    private readonly orchestratorService;
    private readonly analyticsService;
    private readonly sessionMemoryService;
    constructor(orchestratorService: AgentOrchestratorService, analyticsService: AgentAnalyticsService, sessionMemoryService: SessionMemoryService);
    createAgent(createAgentDto: CreateAgentInstanceDto, req: any): Promise<{
        success: boolean;
        data: import("../database/entities/agent.entity").Agent;
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        message: string;
        data?: undefined;
    }>;
    getAgents(req: any): Promise<{
        success: boolean;
        data: import("../database/entities/agent.entity").Agent[];
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        message: string;
        data?: undefined;
    }>;
    getAgent(id: string, req: any): Promise<{
        success: boolean;
        data: import("../database/entities/agent.entity").Agent;
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        message: string;
        data?: undefined;
    }>;
    updateAgent(id: string, updateAgentDto: UpdateAgentInstanceDto, req: any): Promise<{
        success: boolean;
        data: import("../database/entities/agent.entity").Agent;
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        message: string;
        data?: undefined;
    }>;
    deleteAgent(id: string, req: any): Promise<{
        success: boolean;
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        message: string;
    }>;
    executeAgent(id: string, executeDto: ExecuteAgentDto, req: any): Promise<{
        success: boolean;
        data: import("../database/entities/agent-execution.entity").AgentExecution;
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        message: string;
        data?: undefined;
    }>;
    getAgentExecutions(id: string, req: any): Promise<{
        success: boolean;
        data: import("../database/entities/agent-execution.entity").AgentExecution[];
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        message: string;
        data?: undefined;
    }>;
    getAgentAnalytics(id: string, req: any): Promise<{
        success: boolean;
        data: import("./agent-analytics.service").AgentAnalytics;
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        message: string;
        data?: undefined;
    }>;
    getAgentSessions(id: string, req: any): Promise<{
        success: boolean;
        data: import("./session-memory.service").SessionMemory[];
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        message: string;
        data?: undefined;
    }>;
    getSessionHistory(sessionId: string, limit?: number, req: any): Promise<{
        success: boolean;
        data: import("./session-memory.service").SessionMessage[];
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        message: string;
        data?: undefined;
    }>;
    deleteSession(sessionId: string, req: any): Promise<{
        success: boolean;
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        message: string;
    }>;
    getOrganizationAnalytics(req: any): Promise<{
        success: boolean;
        data: {
            totalAgents: number;
            totalExecutions: number;
            totalCost: number;
            averageSuccessRate: number;
            topPerformingAgents: Array<{
                agentId: string;
                name: string;
                executions: number;
                successRate: number;
            }>;
            providerUsage: Record<string, number>;
            dailyExecutions: Array<{
                date: string;
                executions: number;
                cost: number;
            }>;
        };
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        message: string;
        data?: undefined;
    }>;
    getProviderAnalytics(req: any): Promise<{
        success: boolean;
        data: {
            provider: string;
            executions: number;
            successRate: number;
            averageResponseTime: number;
            totalCost: number;
            reliability: number;
        }[];
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        message: string;
        data?: undefined;
    }>;
}
