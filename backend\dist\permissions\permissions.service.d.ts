import { PrismaService } from '../prisma/prisma.service';
import { ApixGateway } from '../websocket/apix.gateway';
export interface CreatePermissionDto {
    name: string;
    description?: string;
    resource: string;
    action: string;
    organizationId: string;
}
export interface UpdatePermissionDto {
    name?: string;
    description?: string;
    resource?: string;
    action?: string;
}
export interface PermissionFilters {
    search?: string;
    resource?: string;
    organizationId?: string;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}
export declare class PermissionService {
    private prisma;
    private apixGateway;
    constructor(prisma: PrismaService, apixGateway: ApixGateway);
    createPermission(createPermissionDto: CreatePermissionDto, createdById: string): Promise<any>;
    updatePermission(permissionId: string, updatePermissionDto: UpdatePermissionDto, updatedById: string): Promise<any>;
    deletePermission(permissionId: string, deletedById: string): Promise<{
        message: string;
    }>;
    getPermissions(filters: PermissionFilters): Promise<{
        permissions: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            pages: number;
        };
    }>;
    getPermissionById(permissionId: string): Promise<any>;
    assignPermissionToUser(permissionId: string, userId: string, assignedById: string): Promise<{
        message: string;
    }>;
    removePermissionFromUser(permissionId: string, userId: string, removedById: string): Promise<{
        message: string;
    }>;
    getResourceList(organizationId: string): Promise<any>;
    getActionList(organizationId: string, resource?: string): Promise<any>;
}
