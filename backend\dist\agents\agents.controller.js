"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/jwt-auth.guard");
const agent_orchestrator_service_1 = require("./agent-orchestrator.service");
const agent_analytics_service_1 = require("./agent-analytics.service");
const session_memory_service_1 = require("./session-memory.service");
const agent_dto_1 = require("./dto/agent.dto");
let AgentsController = class AgentsController {
    constructor(orchestratorService, analyticsService, sessionMemoryService) {
        this.orchestratorService = orchestratorService;
        this.analyticsService = analyticsService;
        this.sessionMemoryService = sessionMemoryService;
    }
    async createAgent(createAgentDto, req) {
        try {
            const agent = await this.orchestratorService.createAgent(createAgentDto, req.user.organizationId, req.user.id);
            return {
                success: true,
                data: agent,
                message: 'Agent created successfully',
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message,
                message: 'Failed to create agent',
            };
        }
    }
    async getAgents(req) {
        try {
            const agents = await this.orchestratorService.getAgentsByOrganization(req.user.organizationId);
            return {
                success: true,
                data: agents,
                message: 'Agents retrieved successfully',
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message,
                message: 'Failed to retrieve agents',
            };
        }
    }
    async getAgent(id, req) {
        try {
            const agent = await this.orchestratorService.getAgentById(id, req.user.organizationId);
            return {
                success: true,
                data: agent,
                message: 'Agent retrieved successfully',
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message,
                message: 'Failed to retrieve agent',
            };
        }
    }
    async updateAgent(id, updateAgentDto, req) {
        try {
            const agent = await this.orchestratorService.updateAgent(id, updateAgentDto, req.user.organizationId);
            return {
                success: true,
                data: agent,
                message: 'Agent updated successfully',
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message,
                message: 'Failed to update agent',
            };
        }
    }
    async deleteAgent(id, req) {
        try {
            await this.orchestratorService.deleteAgent(id, req.user.organizationId);
            return {
                success: true,
                message: 'Agent deleted successfully',
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message,
                message: 'Failed to delete agent',
            };
        }
    }
    async executeAgent(id, executeDto, req) {
        try {
            const execution = await this.orchestratorService.executeAgent(id, executeDto, req.user.organizationId);
            return {
                success: true,
                data: execution,
                message: 'Agent executed successfully',
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message,
                message: 'Failed to execute agent',
            };
        }
    }
    async getAgentExecutions(id, req) {
        try {
            const executions = await this.orchestratorService.getAgentExecutions(id, req.user.organizationId);
            return {
                success: true,
                data: executions,
                message: 'Executions retrieved successfully',
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message,
                message: 'Failed to retrieve executions',
            };
        }
    }
    async getAgentAnalytics(id, req) {
        try {
            const analytics = await this.analyticsService.getAgentAnalytics(id, req.user.organizationId);
            return {
                success: true,
                data: analytics,
                message: 'Analytics retrieved successfully',
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message,
                message: 'Failed to retrieve analytics',
            };
        }
    }
    async getAgentSessions(id, req) {
        try {
            const sessions = await this.sessionMemoryService.getSessionsByAgent(id, req.user.organizationId);
            return {
                success: true,
                data: sessions,
                message: 'Sessions retrieved successfully',
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message,
                message: 'Failed to retrieve sessions',
            };
        }
    }
    async getSessionHistory(sessionId, limit, req) {
        try {
            const history = await this.sessionMemoryService.getSessionHistory(sessionId, req.user.organizationId, limit);
            return {
                success: true,
                data: history,
                message: 'Session history retrieved successfully',
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message,
                message: 'Failed to retrieve session history',
            };
        }
    }
    async deleteSession(sessionId, req) {
        try {
            await this.sessionMemoryService.deleteSession(sessionId, req.user.organizationId);
            return {
                success: true,
                message: 'Session deleted successfully',
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message,
                message: 'Failed to delete session',
            };
        }
    }
    async getOrganizationAnalytics(req) {
        try {
            const analytics = await this.analyticsService.getOrganizationAnalytics(req.user.organizationId);
            return {
                success: true,
                data: analytics,
                message: 'Organization analytics retrieved successfully',
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message,
                message: 'Failed to retrieve organization analytics',
            };
        }
    }
    async getProviderAnalytics(req) {
        try {
            const analytics = await this.analyticsService.getProviderPerformance(req.user.organizationId);
            return {
                success: true,
                data: analytics,
                message: 'Provider analytics retrieved successfully',
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message,
                message: 'Failed to retrieve provider analytics',
            };
        }
    }
};
exports.AgentsController = AgentsController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new agent instance' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Agent created successfully' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [agent_dto_1.CreateAgentInstanceDto, Object]),
    __metadata("design:returntype", Promise)
], AgentsController.prototype, "createAgent", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all agents for organization' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Agents retrieved successfully' }),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AgentsController.prototype, "getAgents", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get agent by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Agent retrieved successfully' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AgentsController.prototype, "getAgent", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update agent' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Agent updated successfully' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, agent_dto_1.UpdateAgentInstanceDto, Object]),
    __metadata("design:returntype", Promise)
], AgentsController.prototype, "updateAgent", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete agent' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Agent deleted successfully' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AgentsController.prototype, "deleteAgent", null);
__decorate([
    (0, common_1.Post)(':id/execute'),
    (0, swagger_1.ApiOperation)({ summary: 'Execute agent with message' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Agent executed successfully' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, agent_dto_1.ExecuteAgentDto, Object]),
    __metadata("design:returntype", Promise)
], AgentsController.prototype, "executeAgent", null);
__decorate([
    (0, common_1.Get)(':id/executions'),
    (0, swagger_1.ApiOperation)({ summary: 'Get agent execution history' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Executions retrieved successfully' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AgentsController.prototype, "getAgentExecutions", null);
__decorate([
    (0, common_1.Get)(':id/analytics'),
    (0, swagger_1.ApiOperation)({ summary: 'Get agent analytics' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Analytics retrieved successfully' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AgentsController.prototype, "getAgentAnalytics", null);
__decorate([
    (0, common_1.Get)(':id/sessions'),
    (0, swagger_1.ApiOperation)({ summary: 'Get agent sessions' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Sessions retrieved successfully' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AgentsController.prototype, "getAgentSessions", null);
__decorate([
    (0, common_1.Get)('sessions/:sessionId/history'),
    (0, swagger_1.ApiOperation)({ summary: 'Get session conversation history' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Session history retrieved successfully' }),
    __param(0, (0, common_1.Param)('sessionId')),
    __param(1, (0, common_1.Query)('limit')),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, Object]),
    __metadata("design:returntype", Promise)
], AgentsController.prototype, "getSessionHistory", null);
__decorate([
    (0, common_1.Delete)('sessions/:sessionId'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete session' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Session deleted successfully' }),
    __param(0, (0, common_1.Param)('sessionId')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AgentsController.prototype, "deleteSession", null);
__decorate([
    (0, common_1.Get)('analytics/organization'),
    (0, swagger_1.ApiOperation)({ summary: 'Get organization-wide agent analytics' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Organization analytics retrieved successfully' }),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AgentsController.prototype, "getOrganizationAnalytics", null);
__decorate([
    (0, common_1.Get)('analytics/providers'),
    (0, swagger_1.ApiOperation)({ summary: 'Get provider performance analytics' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Provider analytics retrieved successfully' }),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AgentsController.prototype, "getProviderAnalytics", null);
exports.AgentsController = AgentsController = __decorate([
    (0, swagger_1.ApiTags)('Agents'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('api/v1/agents'),
    __metadata("design:paramtypes", [agent_orchestrator_service_1.AgentOrchestratorService,
        agent_analytics_service_1.AgentAnalyticsService,
        session_memory_service_1.SessionMemoryService])
], AgentsController);
//# sourceMappingURL=agents.controller.js.map