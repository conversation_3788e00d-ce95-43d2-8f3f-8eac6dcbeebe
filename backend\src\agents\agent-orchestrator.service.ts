import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Agent, AgentTemplate, AgentCollaboration, AgentType, AgentStatus } from '../database/entities/agent.entity';
import { AgentExecution, ExecutionStatus } from '../database/entities/agent-execution.entity';
import { SessionMemoryService } from './session-memory.service';
import { AgentAnalyticsService } from './agent-analytics.service';
import { ApixGateway } from '../websocket/apix.gateway';
import { AIProviderIntegrationService } from '../providers/ai-provider-integration.service';
import { AIProviderSelectorService } from '../providers/ai-provider-selector.service';
import { CreateAgentInstanceDto, UpdateAgentInstanceDto, ExecuteAgentDto } from './dto/agent.dto';

@Injectable()
export class AgentOrchestratorService {
  private readonly logger = new Logger(AgentOrchestratorService.name);

  constructor(
    @InjectRepository(Agent)
    private agentRepository: Repository<Agent>,
    @InjectRepository(AgentExecution)
    private executionRepository: Repository<AgentExecution>,
    @InjectRepository(AgentCollaboration)
    private collaborationRepository: Repository<AgentCollaboration>,
    private sessionMemoryService: SessionMemoryService,
    private analyticsService: AgentAnalyticsService,
    private apixGateway: ApixGateway,
    private aiProviderIntegration: AIProviderIntegrationService,
    private aiProviderSelector: AIProviderSelectorService,
  ) {}

  async createAgent(
    createAgentDto: CreateAgentInstanceDto,
    organizationId: string,
    userId: string,
  ): Promise<Agent> {
    try {
      const agent = this.agentRepository.create({
        ...createAgentDto,
        organizationId,
        createdBy: userId,
        performanceMetrics: {
          totalExecutions: 0,
          successRate: 0,
          averageResponseTime: 0,
          lastExecuted: new Date(),
        },
      });

      const savedAgent = await this.agentRepository.save(agent);

      // Emit APIX event
      this.apixGateway.emitToOrganization(organizationId, 'agent_created', {
        agentId: savedAgent.id,
        name: savedAgent.name,
        type: savedAgent.type,
        timestamp: new Date(),
      });

      this.logger.log(`Agent created: ${savedAgent.id} for organization: ${organizationId}`);
      return savedAgent;
    } catch (error) {
      this.logger.error(`Failed to create agent: ${error.message}`, error.stack);
      throw error;
    }
  }

  async updateAgent(
    agentId: string,
    updateAgentDto: UpdateAgentInstanceDto,
    organizationId: string,
  ): Promise<Agent> {
    try {
      const agent = await this.agentRepository.findOne({
        where: { id: agentId, organizationId },
      });

      if (!agent) {
        throw new Error('Agent not found');
      }

      Object.assign(agent, updateAgentDto);
      const updatedAgent = await this.agentRepository.save(agent);

      // Emit APIX event
      this.apixGateway.emitToOrganization(organizationId, 'agent_updated', {
        agentId: updatedAgent.id,
        changes: updateAgentDto,
        timestamp: new Date(),
      });

      return updatedAgent;
    } catch (error) {
      this.logger.error(`Failed to update agent ${agentId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async deleteAgent(agentId: string, organizationId: string): Promise<void> {
    try {
      const result = await this.agentRepository.delete({
        id: agentId,
        organizationId,
      });

      if (result.affected === 0) {
        throw new Error('Agent not found');
      }

      // Emit APIX event
      this.apixGateway.emitToOrganization(organizationId, 'agent_deleted', {
        agentId,
        timestamp: new Date(),
      });

      this.logger.log(`Agent deleted: ${agentId}`);
    } catch (error) {
      this.logger.error(`Failed to delete agent ${agentId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async executeAgent(
    agentId: string,
    executeDto: ExecuteAgentDto,
    organizationId: string,
  ): Promise<AgentExecution> {
    const startTime = Date.now();
    let execution: AgentExecution;

    try {
      // Get agent
      const agent = await this.agentRepository.findOne({
        where: { id: agentId, organizationId },
        relations: ['template'],
      });

      if (!agent) {
        throw new Error('Agent not found');
      }

      if (agent.status !== AgentStatus.ACTIVE) {
        throw new Error('Agent is not active');
      }

      // Create execution record
      execution = this.executionRepository.create({
        agentId,
        sessionId: executeDto.sessionId,
        input: executeDto.message,
        status: ExecutionStatus.RUNNING,
        context: executeDto.context || {},
        organizationId,
      });

      execution = await this.executionRepository.save(execution);

      // Emit execution started event
      this.apixGateway.emitToOrganization(organizationId, 'agent_execution_started', {
        executionId: execution.id,
        agentId,
        timestamp: new Date(),
      });

      // Get or create session memory
      let sessionMemory = null;
      if (executeDto.sessionId) {
        sessionMemory = await this.sessionMemoryService.getSession(
          executeDto.sessionId,
          organizationId,
        );
      } else {
        sessionMemory = await this.sessionMemoryService.createSession(
          agentId,
          organizationId,
        );
        execution.sessionId = sessionMemory.id;
        await this.executionRepository.save(execution);
      }

      // Execute agent logic with real AI providers
      const result = await this.executeAgentLogic(agent, executeDto.message, sessionMemory, executeDto.context);

      // Update execution with results
      const duration = Date.now() - startTime;
      execution.output = result.output;
      execution.status = ExecutionStatus.COMPLETED;
      execution.metadata = {
        provider: result.provider,
        model: result.model,
        tokens: result.tokens,
        cost: result.cost,
        duration,
        retryCount: result.retryCount || 0,
      };
      execution.completedAt = new Date();

      execution = await this.executionRepository.save(execution);

      // Update session memory
      await this.sessionMemoryService.addMessage(sessionMemory.id, {
        role: 'user',
        content: executeDto.message,
        timestamp: new Date(),
      });

      await this.sessionMemoryService.addMessage(sessionMemory.id, {
        role: 'assistant',
        content: result.output,
        timestamp: new Date(),
        metadata: execution.metadata,
      });

      // Update agent performance metrics
      await this.updateAgentMetrics(agentId, duration, true);

      // Emit completion event
      this.apixGateway.emitToOrganization(organizationId, 'agent_execution_completed', {
        executionId: execution.id,
        agentId,
        output: result.output,
        duration,
        timestamp: new Date(),
      });

      // Track analytics
      await this.analyticsService.trackExecution(execution);

      return execution;
    } catch (error) {
      this.logger.error(`Agent execution failed: ${error.message}`, error.stack);

      if (execution) {
        execution.status = ExecutionStatus.FAILED;
        execution.errorMessage = error.message;
        execution.errorDetails = { stack: error.stack };
        execution.completedAt = new Date();
        await this.executionRepository.save(execution);

        // Update metrics for failure
        await this.updateAgentMetrics(agentId, Date.now() - startTime, false);

        // Emit failure event
        this.apixGateway.emitToOrganization(organizationId, 'agent_execution_failed', {
          executionId: execution.id,
          agentId,
          error: error.message,
          timestamp: new Date(),
        });
      }

      throw error;
    }
  }

  private async executeAgentLogic(
    agent: Agent,
    message: string,
    sessionMemory: any,
    context: Record<string, any> = {},
  ): Promise<{
    output: string;
    provider: string;
    model: string;
    tokens: { input: number; output: number; total: number };
    cost: number;
    retryCount?: number;
  }> {
    try {
      // Build conversation messages
      const messages = [];
      
      // Add system message from template
      if (agent.template?.promptTemplate) {
        messages.push({
          role: 'system' as const,
          content: this.buildSystemPrompt(agent.template.promptTemplate, agent, context),
        });
      }

      // Add conversation history
      if (sessionMemory.messages && sessionMemory.messages.length > 0) {
        const recentMessages = sessionMemory.messages.slice(-10); // Keep last 10 messages
        messages.push(...recentMessages.map(msg => ({
          role: msg.role as 'user' | 'assistant',
          content: msg.content,
        })));
      }

      // Add current message
      messages.push({
        role: 'user' as const,
        content: message,
      });

      // Determine required capabilities based on agent type and skills
      const capabilities = this.getRequiredCapabilities(agent);

      // Create AI request
      const aiRequest = {
        requestId: `agent_${agent.id}_${Date.now()}`,
        providerId: agent.primaryProvider ? this.mapProviderType(agent.primaryProvider) : undefined,
        messages,
        temperature: agent.config?.temperature || 0.7,
        maxTokens: agent.config?.maxTokens || 1000,
        organizationId: agent.organizationId,
      };

      // Process request through AI provider integration
      const aiResponse = await this.aiProviderIntegration.processRequest(aiRequest);

      return {
        output: aiResponse.content,
        provider: aiResponse.providerId,
        model: aiResponse.modelId,
        tokens: {
          input: aiResponse.usage.promptTokens,
          output: aiResponse.usage.completionTokens,
          total: aiResponse.usage.totalTokens,
        },
        cost: aiResponse.cost,
      };

    } catch (error) {
      this.logger.error(`Agent logic execution failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  private buildSystemPrompt(template: string, agent: Agent, context: Record<string, any>): string {
    let prompt = template;

    // Replace agent-specific variables
    prompt = prompt.replace(/\{agent\.name\}/g, agent.name);
    prompt = prompt.replace(/\{agent\.type\}/g, agent.type);
    
    // Replace skills
    if (agent.skills && agent.skills.length > 0) {
      prompt = prompt.replace(/\{agent\.skills\}/g, agent.skills.join(', '));
    }

    // Replace context variables
    Object.entries(context).forEach(([key, value]) => {
      const regex = new RegExp(`\\{context\\.${key}\\}`, 'g');
      prompt = prompt.replace(regex, String(value));
    });

    // Add current timestamp
    prompt = prompt.replace(/\{timestamp\}/g, new Date().toISOString());

    return prompt;
  }

  private getRequiredCapabilities(agent: Agent): string[] {
    const capabilities = ['chat'];

    // Add capabilities based on agent type
    switch (agent.type) {
      case AgentType.TOOL_DRIVEN:
        capabilities.push('function-calling');
        break;
      case AgentType.MULTI_TASK:
        capabilities.push('analysis', 'code-generation');
        break;
      case AgentType.COLLABORATIVE:
        capabilities.push('function-calling', 'analysis');
        break;
    }

    // Add capabilities based on skills
    if (agent.skills) {
      if (agent.skills.includes('code_generation')) {
        capabilities.push('code-generation');
      }
      if (agent.skills.includes('image_analysis')) {
        capabilities.push('vision');
      }
      if (agent.skills.includes('function_calling')) {
        capabilities.push('function-calling');
      }
    }

    return [...new Set(capabilities)]; // Remove duplicates
  }

  private mapProviderType(providerType: any): string {
    // Map agent provider enum to provider service format
    const mapping = {
      OPENAI: 'openai',
      CLAUDE: 'claude',
      GEMINI: 'gemini',
      MISTRAL: 'mistral',
      GROQ: 'groq',
    };
    return mapping[providerType] || providerType.toLowerCase();
  }

  private async updateAgentMetrics(agentId: string, duration: number, success: boolean): Promise<void> {
    const agent = await this.agentRepository.findOne({ where: { id: agentId } });
    if (!agent) return;

    const metrics = agent.performanceMetrics || {
      totalExecutions: 0,
      successRate: 0,
      averageResponseTime: 0,
      lastExecuted: new Date(),
    };

    metrics.totalExecutions += 1;
    metrics.successRate = success 
      ? (metrics.successRate * (metrics.totalExecutions - 1) + 1) / metrics.totalExecutions
      : (metrics.successRate * (metrics.totalExecutions - 1)) / metrics.totalExecutions;
    
    metrics.averageResponseTime = 
      (metrics.averageResponseTime * (metrics.totalExecutions - 1) + duration) / metrics.totalExecutions;
    
    metrics.lastExecuted = new Date();

    agent.performanceMetrics = metrics;
    await this.agentRepository.save(agent);
  }

  // Agent Collaboration Methods
  async createCollaboration(
    name: string,
    agentIds: string[],
    coordinatorId: string,
    workflow: Record<string, any>,
    organizationId: string,
    userId: string,
  ): Promise<AgentCollaboration> {
    try {
      // Validate all agents exist and belong to organization
      const agents = await this.agentRepository.find({
        where: { id: agentIds as any, organizationId },
      });

      if (agents.length !== agentIds.length) {
        throw new Error('One or more agents not found');
      }

      const collaboration = this.collaborationRepository.create({
        name,
        agentIds,
        coordinatorId,
        workflow,
        organizationId,
        createdBy: userId,
        status: 'ACTIVE',
        sharedContext: {},
      });

      const savedCollaboration = await this.collaborationRepository.save(collaboration);

      // Emit collaboration created event
      this.apixGateway.emitToOrganization(organizationId, 'agent_collaboration_created', {
        collaborationId: savedCollaboration.id,
        name: savedCollaboration.name,
        agentIds,
        timestamp: new Date(),
      });

      this.logger.log(`Agent collaboration created: ${savedCollaboration.id}`);
      return savedCollaboration;
    } catch (error) {
      this.logger.error(`Failed to create agent collaboration: ${error.message}`, error.stack);
      throw error;
    }
  }

  async executeCollaboration(
    collaborationId: string,
    input: string,
    organizationId: string,
  ): Promise<{
    collaborationId: string;
    results: Array<{
      agentId: string;
      output: string;
      executionId: string;
    }>;
    finalOutput: string;
  }> {
    try {
      const collaboration = await this.collaborationRepository.findOne({
        where: { id: collaborationId, organizationId },
      });

      if (!collaboration) {
        throw new Error('Collaboration not found');
      }

      if (collaboration.status !== 'ACTIVE') {
        throw new Error('Collaboration is not active');
      }

      // Get coordinator agent
      const coordinator = await this.agentRepository.findOne({
        where: { id: collaboration.coordinatorId, organizationId },
      });

      if (!coordinator) {
        throw new Error('Coordinator agent not found');
      }

      // Execute workflow based on collaboration workflow definition
      const results = await this.executeCollaborationWorkflow(
        collaboration,
        input,
        organizationId,
      );

      // Emit collaboration completed event
      this.apixGateway.emitToOrganization(organizationId, 'agent_collaboration_completed', {
        collaborationId,
        results: results.map(r => ({ agentId: r.agentId, executionId: r.executionId })),
        timestamp: new Date(),
      });

      return {
        collaborationId,
        results,
        finalOutput: results[results.length - 1]?.output || 'No output generated',
      };
    } catch (error) {
      this.logger.error(`Collaboration execution failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  private async executeCollaborationWorkflow(
    collaboration: AgentCollaboration,
    input: string,
    organizationId: string,
  ): Promise<Array<{
    agentId: string;
    output: string;
    executionId: string;
  }>> {
    const results = [];
    let currentInput = input;
    let sharedContext = collaboration.sharedContext || {};

    // Execute agents in sequence (simplified workflow)
    for (const agentId of collaboration.agentIds) {
      try {
        const execution = await this.executeAgent(
          agentId,
          {
            message: currentInput,
            context: {
              ...sharedContext,
              collaborationId: collaboration.id,
              previousResults: results,
            },
          },
          organizationId,
        );

        results.push({
          agentId,
          output: execution.output,
          executionId: execution.id,
        });

        // Update shared context
        sharedContext = {
          ...sharedContext,
          [`agent_${agentId}_output`]: execution.output,
          [`agent_${agentId}_metadata`]: execution.metadata,
        };

        // Use output as input for next agent
        currentInput = execution.output;

      } catch (error) {
        this.logger.error(`Agent ${agentId} failed in collaboration: ${error.message}`);
        
        results.push({
          agentId,
          output: `Error: ${error.message}`,
          executionId: 'failed',
        });
      }
    }

    // Update collaboration shared context
    collaboration.sharedContext = sharedContext;
    await this.collaborationRepository.save(collaboration);

    return results;
  }

  async getAgentsByOrganization(organizationId: string): Promise<Agent[]> {
    return this.agentRepository.find({
      where: { organizationId },
      relations: ['template', 'executions'],
      order: { createdAt: 'DESC' },
    });
  }

  async getAgentById(agentId: string, organizationId: string): Promise<Agent> {
    const agent = await this.agentRepository.findOne({
      where: { id: agentId, organizationId },
      relations: ['template', 'executions'],
    });

    if (!agent) {
      throw new Error('Agent not found');
    }

    return agent;
  }

  async getAgentExecutions(agentId: string, organizationId: string): Promise<AgentExecution[]> {
    return this.executionRepository.find({
      where: { agentId, organizationId },
      order: { createdAt: 'DESC' },
      take: 100,
    });
  }

  async getCollaborationsByOrganization(organizationId: string): Promise<AgentCollaboration[]> {
    return this.collaborationRepository.find({
      where: { organizationId },
      order: { createdAt: 'DESC' },
    });
  }
}