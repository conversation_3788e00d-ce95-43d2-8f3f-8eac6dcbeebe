import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AgentTemplate } from '../database/entities/agent.entity';
import { CreateAgentTemplateDto } from './dto/agent.dto';

@Injectable()
export class AgentTemplatesService {
  private readonly logger = new Logger(AgentTemplatesService.name);

  constructor(
    @InjectRepository(AgentTemplate)
    private templateRepository: Repository<AgentTemplate>,
  ) {}

  async createTemplate(
    createTemplateDto: CreateAgentTemplateDto,
    organizationId: string,
    userId: string,
  ): Promise<AgentTemplate> {
    try {
      const template = this.templateRepository.create({
        ...createTemplateDto,
        organizationId,
        createdBy: userId,
      });

      const savedTemplate = await this.templateRepository.save(template);
      this.logger.log(`Agent template created: ${savedTemplate.id}`);
      return savedTemplate;
    } catch (error) {
      this.logger.error(`Failed to create agent template: ${error.message}`);
      throw error;
    }
  }

  async getTemplatesByOrganization(organizationId: string): Promise<AgentTemplate[]> {
    return this.templateRepository.find({
      where: [
        { organizationId },
        { isPublic: true },
      ],
      order: { createdAt: 'DESC' },
    });
  }

  async getTemplateById(templateId: string, organizationId: string): Promise<AgentTemplate> {
    const template = await this.templateRepository.findOne({
      where: [
        { id: templateId, organizationId },
        { id: templateId, isPublic: true },
      ],
    });

    if (!template) {
      throw new Error('Template not found');
    }

    return template;
  }

  async updateTemplate(
    templateId: string,
    updateData: Partial<CreateAgentTemplateDto>,
    organizationId: string,
  ): Promise<AgentTemplate> {
    const template = await this.templateRepository.findOne({
      where: { id: templateId, organizationId },
    });

    if (!template) {
      throw new Error('Template not found');
    }

    Object.assign(template, updateData);
    return this.templateRepository.save(template);
  }

  async deleteTemplate(templateId: string, organizationId: string): Promise<void> {
    const result = await this.templateRepository.delete({
      id: templateId,
      organizationId,
    });

    if (result.affected === 0) {
      throw new Error('Template not found');
    }
  }

  async getPublicTemplates(): Promise<AgentTemplate[]> {
    return this.templateRepository.find({
      where: { isPublic: true },
      order: { createdAt: 'DESC' },
    });
  }

  async getTemplatesByCategory(category: string, organizationId: string): Promise<AgentTemplate[]> {
    return this.templateRepository.find({
      where: [
        { category, organizationId },
        { category, isPublic: true },
      ],
      order: { createdAt: 'DESC' },
    });
  }

  // Pre-built templates
  async seedDefaultTemplates(): Promise<void> {
    const defaultTemplates = [
      {
        name: 'Customer Support Agent',
        category: 'Customer Service',
        description: 'Handles customer inquiries with empathy and efficiency',
        config: {
          personality: 'helpful',
          responseStyle: 'professional',
          maxResponseLength: 500,
        },
        skills: ['customer_support', 'product_knowledge', 'escalation'],
        isPublic: true,
        promptTemplate: `You are a helpful customer support agent. Always be polite, empathetic, and solution-focused. 
        If you cannot resolve an issue, escalate it appropriately.`,
        type: 'BASIC',
        supportedProviders: ['OPENAI', 'CLAUDE'],
        createdBy: 'system',
        organizationId: 'system',
      },
      {
        name: 'Sales Assistant',
        category: 'Sales',
        description: 'Helps qualify leads and provide product information',
        config: {
          personality: 'persuasive',
          responseStyle: 'engaging',
          maxResponseLength: 300,
        },
        skills: ['lead_qualification', 'product_demo', 'objection_handling'],
        isPublic: true,
        promptTemplate: `You are a knowledgeable sales assistant. Help qualify leads, provide product information, 
        and guide prospects through the sales process.`,
        type: 'TOOL_DRIVEN',
        supportedProviders: ['OPENAI', 'CLAUDE', 'GEMINI'],
        createdBy: 'system',
        organizationId: 'system',
      },
      {
        name: 'Content Creator',
        category: 'Marketing',
        description: 'Creates engaging content for various platforms',
        config: {
          personality: 'creative',
          responseStyle: 'engaging',
          maxResponseLength: 1000,
        },
        skills: ['content_writing', 'seo_optimization', 'social_media'],
        isPublic: true,
        promptTemplate: `You are a creative content creator. Generate engaging, original content 
        optimized for the target platform and audience.`,
        type: 'HYBRID',
        supportedProviders: ['OPENAI', 'CLAUDE', 'GEMINI'],
        createdBy: 'system',
        organizationId: 'system',
      },
      {
        name: 'Data Analyst',
        category: 'Analytics',
        description: 'Analyzes data and provides insights',
        config: {
          personality: 'analytical',
          responseStyle: 'detailed',
          maxResponseLength: 800,
        },
        skills: ['data_analysis', 'visualization', 'reporting'],
        isPublic: true,
        promptTemplate: `You are a data analyst. Analyze data thoroughly, identify patterns and trends, 
        and provide actionable insights with clear explanations.`,
        type: 'TOOL_DRIVEN',
        supportedProviders: ['OPENAI', 'CLAUDE'],
        createdBy: 'system',
        organizationId: 'system',
      },
      {
        name: 'Project Manager',
        category: 'Management',
        description: 'Helps manage projects and coordinate teams',
        config: {
          personality: 'organized',
          responseStyle: 'structured',
          maxResponseLength: 600,
        },
        skills: ['project_planning', 'team_coordination', 'risk_management'],
        isPublic: true,
        promptTemplate: `You are an experienced project manager. Help plan projects, coordinate teams, 
        manage timelines, and identify potential risks.`,
        type: 'MULTI_TASK',
        supportedProviders: ['OPENAI', 'CLAUDE', 'GEMINI'],
        createdBy: 'system',
        organizationId: 'system',
      },
    ];

    for (const template of defaultTemplates) {
      const exists = await this.templateRepository.findOne({
        where: { name: template.name, organizationId: 'system' },
      });

      if (!exists) {
        await this.templateRepository.save(template);
        this.logger.log(`Seeded template: ${template.name}`);
      }
    }
  }
}