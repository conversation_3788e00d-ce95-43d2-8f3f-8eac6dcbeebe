import { PrismaService } from '../prisma/prisma.service';
import { ApixGateway } from '../websocket/apix.gateway';
export interface CreateRoleDto {
    name: string;
    description?: string;
    organizationId: string;
    permissionIds?: string[];
}
export interface UpdateRoleDto {
    name?: string;
    description?: string;
    isActive?: boolean;
    permissionIds?: string[];
}
export interface RoleFilters {
    search?: string;
    organizationId?: string;
    isActive?: boolean;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}
export declare class RoleService {
    private prisma;
    private apixGateway;
    constructor(prisma: PrismaService, apixGateway: ApixGateway);
    createRole(createRoleDto: CreateRoleDto, createdById: string): Promise<any>;
    updateRole(roleId: string, updateRoleDto: UpdateRoleDto, updatedById: string): Promise<any>;
    deleteRole(roleId: string, deletedById: string): Promise<{
        message: string;
    }>;
    getRoles(filters: RoleFilters): Promise<{
        roles: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            pages: number;
        };
    }>;
    getRoleById(roleId: string): Promise<any>;
    assignRoleToUser(roleId: string, userId: string, assignedById: string): Promise<{
        message: string;
    }>;
    removeRoleFromUser(roleId: string, userId: string, removedById: string): Promise<{
        message: string;
    }>;
}
