import { User } from '../../users/user.entity';
import { Organization } from '../../organizations/organization.entity';
export declare enum ProviderType {
    OPENAI = "OPENAI",
    CLAUDE = "CLAUDE",
    GEMINI = "GEMINI",
    MISTRAL = "MISTRAL",
    GROQ = "GROQ",
    DEEPSEEK = "DEEPSEEK",
    HUGGING_FACE = "HUGGING_FACE",
    LOCAL_AI = "LOCAL_AI",
    CUSTOM = "CUSTOM"
}
export declare class AIProvider {
    id: string;
    name: string;
    type: ProviderType;
    config: {
        apiKey?: string;
        baseUrl?: string;
        organizationId?: string;
        projectId?: string;
        region?: string;
        customHeaders?: Record<string, string>;
        timeout?: number;
        maxRetries?: number;
    };
    models: Array<{
        id: string;
        name: string;
        description?: string;
        version: string;
        capabilities: Record<string, boolean>;
        fineTuned: boolean;
        contextLength: number;
        costPer1KTokens: {
            input: number;
            output: number;
        };
    }>;
    isActive: boolean;
    quotaLimits: {
        dailyRequests?: number;
        monthlyRequests?: number;
        dailyCostCents?: number;
        monthlyCostCents?: number;
    };
    performanceMetrics: {
        averageLatency: number;
        reliability: number;
        totalRequests: number;
        successfulRequests: number;
        lastHealthCheck: Date;
        healthStatus: 'healthy' | 'degraded' | 'unhealthy';
    };
    organizationId: string;
    organization: Organization;
    createdBy: string;
    creator: User;
    aiModels: AIModel[];
    usageRecords: ProviderUsage[];
    createdAt: Date;
    updatedAt: Date;
}
export declare class AIModel {
    id: string;
    providerId: string;
    provider: AIProvider;
    name: string;
    description: string;
    version: string;
    capabilities: {
        chat: boolean;
        completion: boolean;
        embedding: boolean;
        vision: boolean;
        functionCalling: boolean;
        codeGeneration: boolean;
        analysis: boolean;
        multimodal: boolean;
        streaming: boolean;
    };
    fineTuned: boolean;
    contextLength: number;
    costPer1KTokens: {
        input: number;
        output: number;
    };
    metadata: {
        trainingData?: string;
        releaseDate?: Date;
        deprecated?: boolean;
        replacedBy?: string;
        tags?: string[];
    };
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
}
export declare class ProviderUsage {
    id: string;
    providerId: string;
    provider: AIProvider;
    date: Date;
    requests: number;
    tokensUsed: number;
    costInCents: number;
    successfulRequests: number;
    failedRequests: number;
    averageLatency: number;
    breakdown: {
        byModel: Record<string, {
            requests: number;
            tokens: number;
            cost: number;
        }>;
        byCapability: Record<string, {
            requests: number;
            tokens: number;
            cost: number;
        }>;
    };
    createdAt: Date;
}
export declare class ProviderHealthCheck {
    id: string;
    providerId: string;
    provider: AIProvider;
    checkedAt: Date;
    status: 'healthy' | 'degraded' | 'unhealthy';
    responseTime: number;
    isAvailable: boolean;
    errorMessage: string;
    metrics: {
        uptime: number;
        errorRate: number;
        averageResponseTime: number;
        throughput: number;
    };
    createdAt: Date;
}
