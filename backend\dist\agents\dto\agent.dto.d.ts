export declare enum AgentType {
    BASIC = "BASIC",
    TOOL_DRIVEN = "TOOL_DRIVEN",
    HYBRID = "HYBRID",
    MULTI_TASK = "MULTI_TASK",
    MULTI_PROVIDER = "MULTI_PROVIDER",
    COLLABORATIVE = "COLLABORATIVE"
}
export declare enum AgentStatus {
    ACTIVE = "ACTIVE",
    INACTIVE = "INACTIVE",
    ERROR = "ERROR",
    PAUSED = "PAUSED"
}
export declare enum ProviderType {
    OPENAI = "OPENAI",
    CLAUDE = "CLAUDE",
    GEMINI = "GEMINI",
    MISTRAL = "MISTRAL",
    GROQ = "GROQ"
}
export declare class CreateAgentTemplateDto {
    name: string;
    category: string;
    description: string;
    config: Record<string, any>;
    skills: string[];
    isPublic?: boolean;
    promptTemplate: string;
    type: AgentType;
    supportedProviders: ProviderType[];
}
export declare class CreateAgentInstanceDto {
    templateId: string;
    name: string;
    config: Record<string, any>;
    type: AgentType;
    primaryProvider: ProviderType;
    fallbackProviders?: ProviderType[];
    memoryConfig?: {
        maxTokens: number;
        retentionDays: number;
        enableLongTerm: boolean;
    };
    skills?: string[];
}
export declare class UpdateAgentInstanceDto {
    name?: string;
    config?: Record<string, any>;
    status?: AgentStatus;
    primaryProvider?: ProviderType;
    fallbackProviders?: ProviderType[];
}
export declare class ExecuteAgentDto {
    message: string;
    sessionId?: string;
    context?: Record<string, any>;
    streamResponse?: boolean;
}
export declare class AgentCollaborationDto {
    name: string;
    agentIds: string[];
    coordinatorId: string;
    workflow: Record<string, any>;
    sharedContext?: Record<string, any>;
}
