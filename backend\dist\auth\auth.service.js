"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const prisma_service_1 = require("../prisma/prisma.service");
const bcrypt = require("bcryptjs");
const ioredis_1 = require("ioredis");
const client_1 = require("@prisma/client");
let AuthService = class AuthService {
    constructor(prisma, jwtService) {
        this.prisma = prisma;
        this.jwtService = jwtService;
        this.redis = new ioredis_1.default({
            host: process.env.REDIS_HOST || 'localhost',
            port: parseInt(process.env.REDIS_PORT) || 6379,
            password: process.env.REDIS_PASSWORD,
        });
    }
    async register(registerDto) {
        const { email, password, name, organizationName } = registerDto;
        const existingUser = await this.prisma.user.findUnique({ where: { email } });
        if (existingUser) {
            throw new common_1.ConflictException('User with this email already exists');
        }
        const passwordHash = await bcrypt.hash(password, 12);
        const result = await this.prisma.$transaction(async (tx) => {
            const organization = await tx.organization.create({
                data: {
                    name: organizationName,
                    slug: organizationName.toLowerCase().replace(/[^a-z0-9]/g, '-'),
                    quotas: {
                        agents: 10,
                        tools: 50,
                        executions: 1000,
                        storage: 1024 * 1024 * 100,
                    },
                    billing: {
                        plan: 'starter',
                        status: 'active',
                        usage: {},
                    },
                },
            });
            const user = await tx.user.create({
                data: {
                    email,
                    name,
                    passwordHash,
                    role: client_1.UserRole.ORG_ADMIN,
                    permissions: this.getDefaultPermissions(client_1.UserRole.ORG_ADMIN),
                    organizationId: organization.id,
                },
                include: {
                    organization: true,
                },
            });
            return { user, organization };
        });
        const tokens = await this.generateTokens(result.user);
        await this.redis.setex(`refresh_token:${result.user.id}`, 7 * 24 * 60 * 60, tokens.refreshToken);
        await this.logAuditEvent(result.user.id, result.organization.id, 'CREATE', 'user', result.user.id, {
            action: 'user_registered',
            email: result.user.email,
        });
        return Object.assign({ user: this.sanitizeUser(result.user), organization: result.organization }, tokens);
    }
    async login(loginDto) {
        const { email, password } = loginDto;
        const user = await this.prisma.user.findUnique({
            where: { email, isActive: true },
            include: { organization: true },
        });
        if (!user || !user.passwordHash) {
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
        const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
        if (!isPasswordValid) {
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
        await this.prisma.user.update({
            where: { id: user.id },
            data: { lastLoginAt: new Date() },
        });
        const tokens = await this.generateTokens(user);
        await this.redis.setex(`refresh_token:${user.id}`, 7 * 24 * 60 * 60, tokens.refreshToken);
        await this.logAuditEvent(user.id, user.organizationId, 'LOGIN', 'user', user.id, {
            action: 'user_login',
            email: user.email,
        });
        return Object.assign({ user: this.sanitizeUser(user), organization: user.organization }, tokens);
    }
    async refreshToken(refreshToken) {
        try {
            const payload = this.jwtService.verify(refreshToken);
            const { userId } = payload;
            const storedToken = await this.redis.get(`refresh_token:${userId}`);
            if (storedToken !== refreshToken) {
                throw new common_1.UnauthorizedException('Invalid refresh token');
            }
            const user = await this.prisma.user.findUnique({
                where: { id: userId, isActive: true },
                include: { organization: true },
            });
            if (!user) {
                throw new common_1.UnauthorizedException('User not found');
            }
            const tokens = await this.generateTokens(user);
            await this.redis.setex(`refresh_token:${user.id}`, 7 * 24 * 60 * 60, tokens.refreshToken);
            return Object.assign({ user: this.sanitizeUser(user), organization: user.organization }, tokens);
        }
        catch (error) {
            throw new common_1.UnauthorizedException('Invalid refresh token');
        }
    }
    async logout(userId) {
        await this.redis.del(`refresh_token:${userId}`);
        const user = await this.prisma.user.findUnique({ where: { id: userId } });
        if (user) {
            await this.logAuditEvent(userId, user.organizationId, 'LOGOUT', 'user', userId, {
                action: 'user_logout',
            });
        }
        return { message: 'Logged out successfully' };
    }
    async validateUser(payload) {
        const user = await this.prisma.user.findUnique({
            where: { id: payload.userId, isActive: true },
            include: { organization: true },
        });
        if (!user || user.organizationId !== payload.organizationId) {
            throw new common_1.UnauthorizedException('Invalid token');
        }
        return user;
    }
    hasPermission(user, permission) {
        return user.permissions.includes(permission) || user.role === client_1.UserRole.SUPER_ADMIN;
    }
    hasAnyPermission(user, permissions) {
        return permissions.some(permission => this.hasPermission(user, permission));
    }
    hasAllPermissions(user, permissions) {
        return permissions.every(permission => this.hasPermission(user, permission));
    }
    async enforceOrganizationAccess(userId, organizationId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });
        if (!user || user.organizationId !== organizationId) {
            throw new common_1.ForbiddenException('Access denied to organization resources');
        }
        return user;
    }
    async createApiKey(userId, name, permissions = []) {
        const user = await this.prisma.user.findUnique({ where: { id: userId } });
        if (!user) {
            throw new common_1.UnauthorizedException('User not found');
        }
        const key = `sk_${Math.random().toString(36).substr(2, 32)}`;
        const apiKey = await this.prisma.apiKey.create({
            data: {
                name,
                key: await bcrypt.hash(key, 10),
                permissions,
                organizationId: user.organizationId,
            },
        });
        await this.logAuditEvent(userId, user.organizationId, 'CREATE', 'api_key', apiKey.id, {
            action: 'api_key_created',
            name,
            permissions,
        });
        return Object.assign(Object.assign({}, apiKey), { key });
    }
    async validateApiKey(key) {
        const apiKeys = await this.prisma.apiKey.findMany({
            where: { isActive: true },
            include: { organization: true },
        });
        for (const apiKey of apiKeys) {
            const isValid = await bcrypt.compare(key, apiKey.key);
            if (isValid) {
                await this.prisma.apiKey.update({
                    where: { id: apiKey.id },
                    data: { lastUsedAt: new Date() },
                });
                return apiKey;
            }
        }
        throw new common_1.UnauthorizedException('Invalid API key');
    }
    async generateTokens(user) {
        const payload = {
            userId: user.id,
            organizationId: user.organizationId,
            role: user.role,
            permissions: user.permissions,
        };
        const accessToken = this.jwtService.sign(payload, {
            expiresIn: '15m',
        });
        const refreshToken = this.jwtService.sign({ userId: user.id }, { expiresIn: '7d' });
        return {
            accessToken,
            refreshToken,
            expiresIn: 15 * 60,
        };
    }
    sanitizeUser(user) {
        const { passwordHash } = user, sanitized = __rest(user, ["passwordHash"]);
        return sanitized;
    }
    getDefaultPermissions(role) {
        const permissions = {
            [client_1.UserRole.SUPER_ADMIN]: [
                'system:read',
                'system:write',
                'org:read',
                'org:write',
                'user:read',
                'user:write',
                'agent:read',
                'agent:write',
                'tool:read',
                'tool:write',
                'session:read',
                'session:write',
                'billing:read',
                'billing:write',
            ],
            [client_1.UserRole.ORG_ADMIN]: [
                'org:read',
                'org:write',
                'user:read',
                'user:write',
                'agent:read',
                'agent:write',
                'tool:read',
                'tool:write',
                'session:read',
                'session:write',
                'billing:read',
            ],
            [client_1.UserRole.DEVELOPER]: [
                'agent:read',
                'agent:write',
                'tool:read',
                'tool:write',
                'session:read',
                'session:write',
            ],
            [client_1.UserRole.VIEWER]: [
                'agent:read',
                'tool:read',
                'session:read',
            ],
        };
        return permissions[role] || [];
    }
    async logAuditEvent(userId, organizationId, action, resource, resourceId, details) {
        await this.prisma.auditLog.create({
            data: {
                userId,
                organizationId,
                action,
                resource,
                resourceId,
                details,
            },
        });
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        jwt_1.JwtService])
], AuthService);
//# sourceMappingURL=auth.service.js.map