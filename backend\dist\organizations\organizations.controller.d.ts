import { OrganizationService, CreateOrganizationDto, UpdateOrganizationDto, OrganizationFilters } from './organizations.service';
export declare class OrganizationController {
    private readonly organizationService;
    constructor(organizationService: OrganizationService);
    createOrganization(createOrganizationDto: CreateOrganizationDto, req: any): Promise<any>;
    getOrganizations(filters: OrganizationFilters): Promise<{
        organizations: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            pages: number;
        };
    }>;
    getCurrentOrganization(req: any): Promise<any>;
    getOrganizationById(id: string): Promise<any>;
    getOrganizationStats(id: string): Promise<{
        users: any;
        agents: any;
        tools: any;
        workflows: any;
        sessions: any;
        recentActivity: any;
        quotas: any;
        billing: any;
    }>;
    updateOrganization(id: string, updateOrganizationDto: UpdateOrganizationDto, req: any): Promise<any>;
    deleteOrganization(id: string, req: any): Promise<{
        message: string;
    }>;
    suspendOrganization(id: string, body: {
        reason?: string;
    }, req: any): Promise<{
        message: string;
    }>;
    activateOrganization(id: string, req: any): Promise<{
        message: string;
    }>;
}
