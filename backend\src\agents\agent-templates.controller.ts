import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Delete, 
  Body, 
  Param, 
  Query, 
  UseGuards,
  Req
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { AgentTemplatesService } from './agent-templates.service';
import { CreateAgentTemplateDto } from './dto/agent.dto';

@ApiTags('Agent Templates')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('api/v1/agent-templates')
export class AgentTemplatesController {
  constructor(private readonly templatesService: AgentTemplatesService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new agent template' })
  @ApiResponse({ status: 201, description: 'Template created successfully' })
  async createTemplate(
    @Body() createTemplateDto: CreateAgentTemplateDto,
    @Req() req: any,
  ) {
    return this.templatesService.createTemplate(
      createTemplateDto,
      req.user.organizationId,
      req.user.id,
    );
  }

  @Get()
  @ApiOperation({ summary: 'Get all templates for organization' })
  @ApiResponse({ status: 200, description: 'Templates retrieved successfully' })
  async getTemplates(@Req() req: any) {
    return this.templatesService.getTemplatesByOrganization(req.user.organizationId);
  }

  @Get('public')
  @ApiOperation({ summary: 'Get all public templates' })
  @ApiResponse({ status: 200, description: 'Public templates retrieved successfully' })
  async getPublicTemplates() {
    return this.templatesService.getPublicTemplates();
  }

  @Get('category/:category')
  @ApiOperation({ summary: 'Get templates by category' })
  @ApiResponse({ status: 200, description: 'Templates retrieved successfully' })
  async getTemplatesByCategory(
    @Param('category') category: string,
    @Req() req: any,
  ) {
    return this.templatesService.getTemplatesByCategory(category, req.user.organizationId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get template by ID' })
  @ApiResponse({ status: 200, description: 'Template retrieved successfully' })
  async getTemplate(@Param('id') id: string, @Req() req: any) {
    return this.templatesService.getTemplateById(id, req.user.organizationId);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update template' })
  @ApiResponse({ status: 200, description: 'Template updated successfully' })
  async updateTemplate(
    @Param('id') id: string,
    @Body() updateData: Partial<CreateAgentTemplateDto>,
    @Req() req: any,
  ) {
    return this.templatesService.updateTemplate(id, updateData, req.user.organizationId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete template' })
  @ApiResponse({ status: 200, description: 'Template deleted successfully' })
  async deleteTemplate(@Param('id') id: string, @Req() req: any) {
    await this.templatesService.deleteTemplate(id, req.user.organizationId);
    return { message: 'Template deleted successfully' };
  }
}