import { IsString, IsOptional, IsEnum, IsBoolean, IsObject, IsArray, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum AgentType {
  BASIC = 'BASIC',
  TOOL_DRIVEN = 'TOOL_DRIVEN', 
  HYBRID = 'HYBRID',
  MULTI_TASK = 'MULTI_TASK',
  MULTI_PROVIDER = 'MULTI_PROVIDER',
  COLLABORATIVE = 'COLLABORATIVE'
}

export enum AgentStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  ERROR = 'ERROR',
  PAUSED = 'PAUSED'
}

export enum ProviderType {
  OPENAI = 'OPENAI',
  CLAUDE = 'CLAUDE',
  GEMINI = 'GEMINI',
  MISTRAL = 'MISTRAL',
  GROQ = 'GROQ'
}

export class CreateAgentTemplateDto {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsString()
  category: string;

  @ApiProperty()
  @IsString()
  description: string;

  @ApiProperty()
  @IsObject()
  config: Record<string, any>;

  @ApiProperty()
  @IsArray()
  @IsString({ each: true })
  skills: string[];

  @ApiProperty({ default: false })
  @IsBoolean()
  @IsOptional()
  isPublic?: boolean;

  @ApiProperty()
  @IsString()
  promptTemplate: string;

  @ApiProperty({ enum: AgentType })
  @IsEnum(AgentType)
  type: AgentType;

  @ApiProperty({ enum: ProviderType, isArray: true })
  @IsEnum(ProviderType, { each: true })
  supportedProviders: ProviderType[];
}

export class CreateAgentInstanceDto {
  @ApiProperty()
  @IsString()
  templateId: string;

  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsObject()
  config: Record<string, any>;

  @ApiProperty({ enum: AgentType })
  @IsEnum(AgentType)
  type: AgentType;

  @ApiProperty({ enum: ProviderType })
  @IsEnum(ProviderType)
  primaryProvider: ProviderType;

  @ApiProperty({ enum: ProviderType, isArray: true })
  @IsEnum(ProviderType, { each: true })
  @IsOptional()
  fallbackProviders?: ProviderType[];

  @ApiProperty()
  @IsObject()
  @IsOptional()
  memoryConfig?: {
    maxTokens: number;
    retentionDays: number;
    enableLongTerm: boolean;
  };

  @ApiProperty()
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  skills?: string[];
}

export class UpdateAgentInstanceDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty()
  @IsObject()
  @IsOptional()
  config?: Record<string, any>;

  @ApiProperty({ enum: AgentStatus })
  @IsEnum(AgentStatus)
  @IsOptional()
  status?: AgentStatus;

  @ApiProperty({ enum: ProviderType })
  @IsEnum(ProviderType)
  @IsOptional()
  primaryProvider?: ProviderType;

  @ApiProperty({ enum: ProviderType, isArray: true })
  @IsEnum(ProviderType, { each: true })
  @IsOptional()
  fallbackProviders?: ProviderType[];
}

export class ExecuteAgentDto {
  @ApiProperty()
  @IsString()
  message: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  sessionId?: string;

  @ApiProperty()
  @IsObject()
  @IsOptional()
  context?: Record<string, any>;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  streamResponse?: boolean;
}

export class AgentCollaborationDto {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsArray()
  @IsUUID(4, { each: true })
  agentIds: string[];

  @ApiProperty()
  @IsString()
  coordinatorId: string;

  @ApiProperty()
  @IsObject()
  workflow: Record<string, any>;

  @ApiProperty()
  @IsObject()
  @IsOptional()
  sharedContext?: Record<string, any>;
}