import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { ApixGateway } from '../websocket/apix.gateway';

export interface CreateRoleDto {
  name: string;
  description?: string;
  organizationId: string;
  permissionIds?: string[];
}

export interface UpdateRoleDto {
  name?: string;
  description?: string;
  isActive?: boolean;
  permissionIds?: string[];
}

export interface RoleFilters {
  search?: string;
  organizationId?: string;
  isActive?: boolean;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

@Injectable()
export class RoleService {
  constructor(
    private prisma: PrismaService,
    private apixGateway: ApixGateway,
  ) {}

  async createRole(createRoleDto: CreateRoleDto, createdById: string) {
    const { name, description, organizationId, permissionIds = [] } = createRoleDto;

    // Check if role already exists in organization
    const existingRole = await this.prisma.role.findUnique({
      where: {
        name_organizationId: {
          name,
          organizationId,
        },
      },
    });

    if (existingRole) {
      throw new ConflictException('Role with this name already exists in organization');
    }

    // Verify organization exists
    const organization = await this.prisma.organization.findUnique({
      where: { id: organizationId },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    // Verify permissions exist and belong to organization
    if (permissionIds.length > 0) {
      const permissions = await this.prisma.permission.findMany({
        where: {
          id: { in: permissionIds },
          organizationId,
        },
      });

      if (permissions.length !== permissionIds.length) {
        throw new BadRequestException('Some permissions not found or do not belong to organization');
      }
    }

    const result = await this.prisma.$transaction(async (tx) => {
      // Create role
      const role = await tx.role.create({
        data: {
          name,
          description,
          organizationId,
        },
        include: {
          organization: true,
          rolePermissions: {
            include: {
              permission: true,
            },
          },
        },
      });

      // Assign permissions
      if (permissionIds.length > 0) {
        await tx.rolePermission.createMany({
          data: permissionIds.map(permissionId => ({
            roleId: role.id,
            permissionId,
            organizationId,
          })),
        });
      }

      // Log audit event
      await tx.auditLog.create({
        data: {
          userId: createdById,
          organizationId,
          action: 'CREATE',
          resource: 'role',
          resourceId: role.id,
          details: {
            action: 'role_created',
            name: role.name,
            description: role.description,
            permissionIds,
          },
        },
      });

      return role;
    });

    // Publish APIX event
    await this.apixGateway.publishToOrganization(organizationId, {
      type: 'role.created',
      payload: {
        roleId: result.id,
        name: result.name,
        description: result.description,
        createdBy: createdById,
      },
    });

    return result;
  }

  async updateRole(roleId: string, updateRoleDto: UpdateRoleDto, updatedById: string) {
    const { name, description, isActive, permissionIds } = updateRoleDto;

    const existingRole = await this.prisma.role.findUnique({
      where: { id: roleId },
      include: {
        organization: true,
        rolePermissions: true,
      },
    });

    if (!existingRole) {
      throw new NotFoundException('Role not found');
    }

    if (existingRole.isSystem) {
      throw new BadRequestException('Cannot modify system role');
    }

    // Check name uniqueness if name is being updated
    if (name && name !== existingRole.name) {
      const nameExists = await this.prisma.role.findUnique({
        where: {
          name_organizationId: {
            name,
            organizationId: existingRole.organizationId,
          },
        },
      });

      if (nameExists) {
        throw new ConflictException('Role name already exists in organization');
      }
    }

    // Verify permissions if provided
    if (permissionIds !== undefined && permissionIds.length > 0) {
      const permissions = await this.prisma.permission.findMany({
        where: {
          id: { in: permissionIds },
          organizationId: existingRole.organizationId,
        },
      });

      if (permissions.length !== permissionIds.length) {
        throw new BadRequestException('Some permissions not found or do not belong to organization');
      }
    }

    const result = await this.prisma.$transaction(async (tx) => {
      // Update role
      const role = await tx.role.update({
        where: { id: roleId },
        data: {
          ...(name && { name }),
          ...(description !== undefined && { description }),
          ...(isActive !== undefined && { isActive }),
        },
        include: {
          organization: true,
          rolePermissions: {
            include: {
              permission: true,
            },
          },
        },
      });

      // Update permissions if provided
      if (permissionIds !== undefined) {
        // Remove existing permissions
        await tx.rolePermission.deleteMany({
          where: { roleId },
        });

        // Add new permissions
        if (permissionIds.length > 0) {
          await tx.rolePermission.createMany({
            data: permissionIds.map(permissionId => ({
              roleId,
              permissionId,
              organizationId: existingRole.organizationId,
            })),
          });
        }
      }

      // Log audit event
      await tx.auditLog.create({
        data: {
          userId: updatedById,
          organizationId: existingRole.organizationId,
          action: 'UPDATE',
          resource: 'role',
          resourceId: roleId,
          details: {
            action: 'role_updated',
            changes: updateRoleDto,
          },
        },
      });

      return role;
    });

    // Publish APIX event
    await this.apixGateway.publishToOrganization(existingRole.organizationId, {
      type: 'role.updated',
      payload: {
        roleId,
        changes: updateRoleDto,
        updatedBy: updatedById,
      },
    });

    return result;
  }

  async deleteRole(roleId: string, deletedById: string) {
    const role = await this.prisma.role.findUnique({
      where: { id: roleId },
      include: {
        organization: true,
        userRoles: true,
      },
    });

    if (!role) {
      throw new NotFoundException('Role not found');
    }

    if (role.isSystem) {
      throw new BadRequestException('Cannot delete system role');
    }

    if (role.userRoles.length > 0) {
      throw new BadRequestException('Cannot delete role that is assigned to users');
    }

    await this.prisma.$transaction(async (tx) => {
      // Delete role permissions
      await tx.rolePermission.deleteMany({
        where: { roleId },
      });

      // Delete role
      await tx.role.delete({
        where: { id: roleId },
      });

      // Log audit event
      await tx.auditLog.create({
        data: {
          userId: deletedById,
          organizationId: role.organizationId,
          action: 'DELETE',
          resource: 'role',
          resourceId: roleId,
          details: {
            action: 'role_deleted',
            name: role.name,
            description: role.description,
          },
        },
      });
    });

    // Publish APIX event
    await this.apixGateway.publishToOrganization(role.organizationId, {
      type: 'role.deleted',
      payload: {
        roleId,
        name: role.name,
        deletedBy: deletedById,
      },
    });

    return { message: 'Role deleted successfully' };
  }

  async getRoles(filters: RoleFilters) {
    const {
      search,
      organizationId,
      isActive,
      page = 1,
      limit = 20,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = filters;

    const skip = (page - 1) * limit;

    const where: any = {};

    if (organizationId) {
      where.organizationId = organizationId;
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (isActive !== undefined) {
      where.isActive = isActive;
    }

    const [roles, total] = await Promise.all([
      this.prisma.role.findMany({
        where,
        include: {
          organization: true,
          rolePermissions: {
            include: {
              permission: true,
            },
          },
          userRoles: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
        },
        orderBy: { [sortBy]: sortOrder },
        skip,
        take: limit,
      }),
      this.prisma.role.count({ where }),
    ]);

    return {
      roles,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }

  async getRoleById(roleId: string) {
    const role = await this.prisma.role.findUnique({
      where: { id: roleId },
      include: {
        organization: true,
        rolePermissions: {
          include: {
            permission: true,
          },
        },
        userRoles: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                systemRole: true,
                isActive: true,
              },
            },
          },
        },
      },
    });

    if (!role) {
      throw new NotFoundException('Role not found');
    }

    return role;
  }

  async assignRoleToUser(roleId: string, userId: string, assignedById: string) {
    const role = await this.prisma.role.findUnique({
      where: { id: roleId },
      include: { organization: true },
    });

    if (!role) {
      throw new NotFoundException('Role not found');
    }

    const user = await this.prisma.user.findUnique({
      where: { id: userId, organizationId: role.organizationId },
    });

    if (!user) {
      throw new NotFoundException('User not found in organization');
    }

    // Check if assignment already exists
    const existingAssignment = await this.prisma.userRole.findUnique({
      where: {
        userId_roleId: {
          userId,
          roleId,
        },
      },
    });

    if (existingAssignment) {
      throw new ConflictException('User already has this role');
    }

    await this.prisma.$transaction(async (tx) => {
      await tx.userRole.create({
        data: {
          userId,
          roleId,
          organizationId: role.organizationId,
        },
      });

      // Log audit event
      await tx.auditLog.create({
        data: {
          userId: assignedById,
          organizationId: role.organizationId,
          action: 'CREATE',
          resource: 'user_role',
          resourceId: `${userId}-${roleId}`,
          details: {
            action: 'role_assigned',
            userId,
            roleId,
            roleName: role.name,
            userEmail: user.email,
          },
        },
      });
    });

    // Publish APIX event
    await this.apixGateway.publishToOrganization(role.organizationId, {
      type: 'role.assigned',
      payload: {
        userId,
        roleId,
        roleName: role.name,
        userEmail: user.email,
        assignedBy: assignedById,
      },
    });

    return { message: 'Role assigned successfully' };
  }

  async removeRoleFromUser(roleId: string, userId: string, removedById: string) {
    const assignment = await this.prisma.userRole.findUnique({
      where: {
        userId_roleId: {
          userId,
          roleId,
        },
      },
      include: {
        role: true,
        user: true,
      },
    });

    if (!assignment) {
      throw new NotFoundException('Role assignment not found');
    }

    await this.prisma.$transaction(async (tx) => {
      await tx.userRole.delete({
        where: {
          userId_roleId: {
            userId,
            roleId,
          },
        },
      });

      // Log audit event
      await tx.auditLog.create({
        data: {
          userId: removedById,
          organizationId: assignment.organizationId,
          action: 'DELETE',
          resource: 'user_role',
          resourceId: `${userId}-${roleId}`,
          details: {
            action: 'role_removed',
            userId,
            roleId,
            roleName: assignment.role.name,
            userEmail: assignment.user.email,
          },
        },
      });
    });

    // Publish APIX event
    await this.apixGateway.publishToOrganization(assignment.organizationId, {
      type: 'role.removed',
      payload: {
        userId,
        roleId,
        roleName: assignment.role.name,
        userEmail: assignment.user.email,
        removedBy: removedById,
      },
    });

    return { message: 'Role removed successfully' };
  }
}