import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { ApixGateway } from '../websocket/apix.gateway';
import * as bcrypt from 'bcryptjs';
import { SystemUserRole, OrganizationStatus } from '@prisma/client';

export interface CreateUserDto {
  email: string;
  name: string;
  password?: string;
  systemRole?: SystemUserRole;
  organizationId: string;
  roleIds?: string[];
  permissionIds?: string[];
}

export interface UpdateUserDto {
  name?: string;
  email?: string;
  systemRole?: SystemUserRole;
  isActive?: boolean;
  roleIds?: string[];
  permissionIds?: string[];
}

export interface UserFilters {
  search?: string;
  role?: SystemUserRole;
  organizationId?: string;
  isActive?: boolean;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

@Injectable()
export class UserService {
  constructor(
    private prisma: PrismaService,
    private apixGateway: ApixGateway,
  ) {}

  async createUser(createUserDto: CreateUserDto, createdById: string) {
    const { email, name, password, systemRole = SystemUserRole.VIEWER, organizationId, roleIds = [], permissionIds = [] } = createUserDto;

    // Check if user already exists
    const existingUser = await this.prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      throw new ConflictException('User with this email already exists');
    }

    // Verify organization exists and is active
    const organization = await this.prisma.organization.findUnique({
      where: { id: organizationId, status: OrganizationStatus.ACTIVE },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found or inactive');
    }

    // Hash password if provided
    let passwordHash: string | undefined;
    if (password) {
      passwordHash = await bcrypt.hash(password, 12);
    }

    // Create user with roles and permissions in transaction
    const result = await this.prisma.$transaction(async (tx) => {
      // Create user
      const user = await tx.user.create({
        data: {
          email,
          name,
          passwordHash,
          systemRole,
          organizationId,
        },
        include: {
          organization: true,
          userRoles: {
            include: {
              role: true,
            },
          },
          userPermissions: {
            include: {
              permission: true,
            },
          },
        },
      });

      // Assign roles
      if (roleIds.length > 0) {
        await tx.userRole.createMany({
          data: roleIds.map(roleId => ({
            userId: user.id,
            roleId,
            organizationId,
          })),
        });
      }

      // Assign permissions
      if (permissionIds.length > 0) {
        await tx.userPermission.createMany({
          data: permissionIds.map(permissionId => ({
            userId: user.id,
            permissionId,
            organizationId,
          })),
        });
      }

      // Log audit event
      await tx.auditLog.create({
        data: {
          userId: createdById,
          organizationId,
          action: 'CREATE',
          resource: 'user',
          resourceId: user.id,
          details: {
            action: 'user_created',
            email: user.email,
            name: user.name,
            systemRole: user.systemRole,
            roleIds,
            permissionIds,
          },
        },
      });

      return user;
    });

    // Publish APIX event
    await this.apixGateway.publishToOrganization(organizationId, {
      type: 'user.created',
      payload: {
        userId: result.id,
        email: result.email,
        name: result.name,
        systemRole: result.systemRole,
        createdBy: createdById,
      },
    });

    return this.sanitizeUser(result);
  }

  async updateUser(userId: string, updateUserDto: UpdateUserDto, updatedById: string) {
    const { name, email, systemRole, isActive, roleIds, permissionIds } = updateUserDto;

    const existingUser = await this.prisma.user.findUnique({
      where: { id: userId },
      include: {
        organization: true,
        userRoles: true,
        userPermissions: true,
      },
    });

    if (!existingUser) {
      throw new NotFoundException('User not found');
    }

    // Check email uniqueness if email is being updated
    if (email && email !== existingUser.email) {
      const emailExists = await this.prisma.user.findUnique({
        where: { email },
      });

      if (emailExists) {
        throw new ConflictException('Email already in use');
      }
    }

    const result = await this.prisma.$transaction(async (tx) => {
      // Update user
      const user = await tx.user.update({
        where: { id: userId },
        data: {
          ...(name && { name }),
          ...(email && { email }),
          ...(systemRole && { systemRole }),
          ...(isActive !== undefined && { isActive }),
        },
        include: {
          organization: true,
          userRoles: {
            include: {
              role: true,
            },
          },
          userPermissions: {
            include: {
              permission: true,
            },
          },
        },
      });

      // Update roles if provided
      if (roleIds !== undefined) {
        // Remove existing roles
        await tx.userRole.deleteMany({
          where: { userId },
        });

        // Add new roles
        if (roleIds.length > 0) {
          await tx.userRole.createMany({
            data: roleIds.map(roleId => ({
              userId,
              roleId,
              organizationId: existingUser.organizationId,
            })),
          });
        }
      }

      // Update permissions if provided
      if (permissionIds !== undefined) {
        // Remove existing permissions
        await tx.userPermission.deleteMany({
          where: { userId },
        });

        // Add new permissions
        if (permissionIds.length > 0) {
          await tx.userPermission.createMany({
            data: permissionIds.map(permissionId => ({
              userId,
              permissionId,
              organizationId: existingUser.organizationId,
            })),
          });
        }
      }

      // Log audit event
      await tx.auditLog.create({
        data: {
          userId: updatedById,
          organizationId: existingUser.organizationId,
          action: 'UPDATE',
          resource: 'user',
          resourceId: userId,
          details: {
            action: 'user_updated',
            changes: updateUserDto,
          },
        },
      });

      return user;
    });

    // Publish APIX event
    await this.apixGateway.publishToOrganization(existingUser.organizationId, {
      type: 'user.updated',
      payload: {
        userId,
        changes: updateUserDto,
        updatedBy: updatedById,
      },
    });

    return this.sanitizeUser(result);
  }

  async deleteUser(userId: string, deletedById: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      include: { organization: true },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    await this.prisma.$transaction(async (tx) => {
      // Soft delete by deactivating
      await tx.user.update({
        where: { id: userId },
        data: { isActive: false },
      });

      // Log audit event
      await tx.auditLog.create({
        data: {
          userId: deletedById,
          organizationId: user.organizationId,
          action: 'DELETE',
          resource: 'user',
          resourceId: userId,
          details: {
            action: 'user_deleted',
            email: user.email,
            name: user.name,
          },
        },
      });
    });

    // Publish APIX event
    await this.apixGateway.publishToOrganization(user.organizationId, {
      type: 'user.deleted',
      payload: {
        userId,
        email: user.email,
        name: user.name,
        deletedBy: deletedById,
      },
    });

    return { message: 'User deleted successfully' };
  }

  async getUsers(filters: UserFilters) {
    const {
      search,
      role,
      organizationId,
      isActive,
      page = 1,
      limit = 20,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = filters;

    const skip = (page - 1) * limit;

    const where: any = {};

    if (organizationId) {
      where.organizationId = organizationId;
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (role) {
      where.systemRole = role;
    }

    if (isActive !== undefined) {
      where.isActive = isActive;
    }

    const [users, total] = await Promise.all([
      this.prisma.user.findMany({
        where,
        include: {
          organization: true,
          userRoles: {
            include: {
              role: true,
            },
          },
          userPermissions: {
            include: {
              permission: true,
            },
          },
        },
        orderBy: { [sortBy]: sortOrder },
        skip,
        take: limit,
      }),
      this.prisma.user.count({ where }),
    ]);

    return {
      users: users.map(user => this.sanitizeUser(user)),
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }

  async getUserById(userId: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      include: {
        organization: true,
        userRoles: {
          include: {
            role: true,
          },
        },
        userPermissions: {
          include: {
            permission: true,
          },
        },
        socialLogins: true,
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return this.sanitizeUser(user);
  }

  async bulkUpdateUsers(userIds: string[], updateData: Partial<UpdateUserDto>, updatedById: string) {
    const users = await this.prisma.user.findMany({
      where: { id: { in: userIds } },
      include: { organization: true },
    });

    if (users.length !== userIds.length) {
      throw new BadRequestException('Some users not found');
    }

    const organizationId = users[0].organizationId;

    // Ensure all users belong to the same organization
    if (!users.every(user => user.organizationId === organizationId)) {
      throw new BadRequestException('All users must belong to the same organization');
    }

    await this.prisma.$transaction(async (tx) => {
      // Update users
      await tx.user.updateMany({
        where: { id: { in: userIds } },
        data: updateData,
      });

      // Log audit event
      await tx.auditLog.create({
        data: {
          userId: updatedById,
          organizationId,
          action: 'UPDATE',
          resource: 'user',
          resourceId: userIds.join(','),
          details: {
            action: 'bulk_user_update',
            userIds,
            changes: updateData,
          },
        },
      });
    });

    // Publish APIX event
    await this.apixGateway.publishToOrganization(organizationId, {
      type: 'user.bulk_updated',
      payload: {
        userIds,
        changes: updateData,
        updatedBy: updatedById,
      },
    });

    return { message: `${userIds.length} users updated successfully` };
  }

  async resetPassword(userId: string, newPassword: string, resetById: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      include: { organization: true },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const passwordHash = await bcrypt.hash(newPassword, 12);

    await this.prisma.$transaction(async (tx) => {
      await tx.user.update({
        where: { id: userId },
        data: {
          passwordHash,
          resetToken: null,
          resetTokenExpiry: null,
        },
      });

      // Log audit event
      await tx.auditLog.create({
        data: {
          userId: resetById,
          organizationId: user.organizationId,
          action: 'UPDATE',
          resource: 'user',
          resourceId: userId,
          details: {
            action: 'password_reset',
            email: user.email,
          },
        },
      });
    });

    // Publish APIX event
    await this.apixGateway.publishToOrganization(user.organizationId, {
      type: 'user.password_reset',
      payload: {
        userId,
        email: user.email,
        resetBy: resetById,
      },
    });

    return { message: 'Password reset successfully' };
  }

  private sanitizeUser(user: any) {
    const { passwordHash, resetToken, resetTokenExpiry, ...sanitized } = user;
    return sanitized;
  }
}