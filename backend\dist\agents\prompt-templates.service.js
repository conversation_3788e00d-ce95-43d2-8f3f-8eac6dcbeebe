"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PromptTemplatesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const apix_gateway_1 = require("../websocket/apix.gateway");
let PromptTemplatesService = class PromptTemplatesService {
    constructor(prisma, apixGateway) {
        this.prisma = prisma;
        this.apixGateway = apixGateway;
    }
    async create(userId, organizationId, dto) {
        try {
            const template = await this.prisma.promptTemplate.create({
                data: Object.assign(Object.assign({}, dto), { organizationId, createdById: userId }),
                include: {
                    createdBy: {
                        select: { id: true, name: true, email: true }
                    },
                    agentTemplate: {
                        select: { id: true, name: true }
                    },
                    reviews: {
                        include: {
                            user: {
                                select: { id: true, name: true }
                            }
                        }
                    },
                    analytics: {
                        orderBy: { date: 'desc' },
                        take: 30
                    }
                }
            });
            this.apixGateway.emitToOrganization(organizationId, 'prompt_template_created', {
                template,
                userId,
                timestamp: new Date().toISOString()
            });
            return {
                success: true,
                data: template,
                message: 'Prompt template created successfully'
            };
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to create prompt template: ${error.message}`);
        }
    }
    async findAll(organizationId, options) {
        const { category, isPublic, search, agentTemplateId, page = 1, limit = 20, sortBy = 'createdAt', sortOrder = 'desc' } = options || {};
        const where = {
            OR: [
                { organizationId },
                { isPublic: true }
            ]
        };
        if (category) {
            where.category = category;
        }
        if (isPublic !== undefined) {
            where.isPublic = isPublic;
        }
        if (agentTemplateId) {
            where.agentTemplateId = agentTemplateId;
        }
        if (search) {
            where.OR = [
                { name: { contains: search, mode: 'insensitive' } },
                { description: { contains: search, mode: 'insensitive' } },
                { content: { contains: search, mode: 'insensitive' } },
                { tags: { hasSome: [search] } }
            ];
        }
        const [templates, total] = await Promise.all([
            this.prisma.promptTemplate.findMany({
                where,
                include: {
                    createdBy: {
                        select: { id: true, name: true, email: true }
                    },
                    agentTemplate: {
                        select: { id: true, name: true }
                    },
                    reviews: {
                        include: {
                            user: {
                                select: { id: true, name: true }
                            }
                        }
                    },
                    analytics: {
                        orderBy: { date: 'desc' },
                        take: 7
                    }
                },
                orderBy: { [sortBy]: sortOrder },
                skip: (page - 1) * limit,
                take: limit,
            }),
            this.prisma.promptTemplate.count({ where })
        ]);
        return {
            success: true,
            data: {
                templates,
                pagination: {
                    page,
                    limit,
                    total,
                    pages: Math.ceil(total / limit)
                }
            }
        };
    }
    async findOne(id, organizationId) {
        const template = await this.prisma.promptTemplate.findFirst({
            where: {
                id,
                OR: [
                    { organizationId },
                    { isPublic: true }
                ]
            },
            include: {
                createdBy: {
                    select: { id: true, name: true, email: true }
                },
                agentTemplate: {
                    select: { id: true, name: true }
                },
                reviews: {
                    include: {
                        user: {
                            select: { id: true, name: true }
                        }
                    },
                    orderBy: { createdAt: 'desc' }
                },
                analytics: {
                    orderBy: { date: 'desc' },
                    take: 30
                },
                optimizations: {
                    orderBy: { createdAt: 'desc' },
                    take: 10
                },
                parent: {
                    select: { id: true, name: true, version: true }
                },
                children: {
                    select: { id: true, name: true, version: true }
                }
            }
        });
        if (!template) {
            throw new common_1.NotFoundException('Prompt template not found');
        }
        return {
            success: true,
            data: template
        };
    }
    async update(id, userId, organizationId, dto) {
        const template = await this.prisma.promptTemplate.findFirst({
            where: { id, organizationId }
        });
        if (!template) {
            throw new common_1.NotFoundException('Prompt template not found');
        }
        if (template.createdById !== userId) {
            throw new common_1.ForbiddenException('You can only update your own templates');
        }
        try {
            const updatedTemplate = await this.prisma.promptTemplate.update({
                where: { id },
                data: Object.assign(Object.assign({}, dto), { version: template.version + 1 }),
                include: {
                    createdBy: {
                        select: { id: true, name: true, email: true }
                    },
                    agentTemplate: {
                        select: { id: true, name: true }
                    },
                    reviews: {
                        include: {
                            user: {
                                select: { id: true, name: true }
                            }
                        }
                    },
                    analytics: {
                        orderBy: { date: 'desc' },
                        take: 30
                    }
                }
            });
            this.apixGateway.emitToOrganization(organizationId, 'prompt_template_updated', {
                template: updatedTemplate,
                userId,
                timestamp: new Date().toISOString()
            });
            return {
                success: true,
                data: updatedTemplate,
                message: 'Prompt template updated successfully'
            };
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to update prompt template: ${error.message}`);
        }
    }
    async delete(id, userId, organizationId) {
        const template = await this.prisma.promptTemplate.findFirst({
            where: { id, organizationId }
        });
        if (!template) {
            throw new common_1.NotFoundException('Prompt template not found');
        }
        if (template.createdById !== userId) {
            throw new common_1.ForbiddenException('You can only delete your own templates');
        }
        try {
            await this.prisma.promptTemplate.delete({
                where: { id }
            });
            this.apixGateway.emitToOrganization(organizationId, 'prompt_template_deleted', {
                templateId: id,
                userId,
                timestamp: new Date().toISOString()
            });
            return {
                success: true,
                message: 'Prompt template deleted successfully'
            };
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to delete prompt template: ${error.message}`);
        }
    }
    async duplicate(id, userId, organizationId, name) {
        const originalTemplate = await this.prisma.promptTemplate.findFirst({
            where: {
                id,
                OR: [
                    { organizationId },
                    { isPublic: true }
                ]
            }
        });
        if (!originalTemplate) {
            throw new common_1.NotFoundException('Prompt template not found');
        }
        try {
            const duplicatedTemplate = await this.prisma.promptTemplate.create({
                data: {
                    name: name || `${originalTemplate.name} (Copy)`,
                    description: originalTemplate.description,
                    content: originalTemplate.content,
                    variables: originalTemplate.variables,
                    category: originalTemplate.category,
                    tags: originalTemplate.tags,
                    isPublic: false,
                    parentId: originalTemplate.id,
                    organizationId,
                    createdById: userId,
                },
                include: {
                    createdBy: {
                        select: { id: true, name: true, email: true }
                    },
                    agentTemplate: {
                        select: { id: true, name: true }
                    },
                    reviews: {
                        include: {
                            user: {
                                select: { id: true, name: true }
                            }
                        }
                    }
                }
            });
            this.apixGateway.emitToOrganization(organizationId, 'prompt_template_duplicated', {
                originalId: id,
                template: duplicatedTemplate,
                userId,
                timestamp: new Date().toISOString()
            });
            return {
                success: true,
                data: duplicatedTemplate,
                message: 'Prompt template duplicated successfully'
            };
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to duplicate prompt template: ${error.message}`);
        }
    }
    async addReview(templateId, userId, organizationId, dto) {
        const template = await this.prisma.promptTemplate.findFirst({
            where: {
                id: templateId,
                OR: [
                    { organizationId },
                    { isPublic: true }
                ]
            }
        });
        if (!template) {
            throw new common_1.NotFoundException('Prompt template not found');
        }
        try {
            const review = await this.prisma.promptReview.upsert({
                where: {
                    userId_promptTemplateId: {
                        userId,
                        promptTemplateId: templateId
                    }
                },
                update: {
                    rating: dto.rating,
                    comment: dto.comment,
                },
                create: {
                    rating: dto.rating,
                    comment: dto.comment,
                    userId,
                    promptTemplateId: templateId,
                    organizationId,
                },
                include: {
                    user: {
                        select: { id: true, name: true }
                    }
                }
            });
            const reviews = await this.prisma.promptReview.findMany({
                where: { promptTemplateId: templateId }
            });
            const avgRating = reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length;
            await this.prisma.promptTemplate.update({
                where: { id: templateId },
                data: { rating: avgRating }
            });
            this.apixGateway.emitToOrganization(organizationId, 'prompt_template_reviewed', {
                templateId,
                review,
                avgRating,
                userId,
                timestamp: new Date().toISOString()
            });
            return {
                success: true,
                data: review,
                message: 'Review added successfully'
            };
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to add review: ${error.message}`);
        }
    }
    async optimizePrompt(id, userId, organizationId) {
        const template = await this.prisma.promptTemplate.findFirst({
            where: { id, organizationId }
        });
        if (!template) {
            throw new common_1.NotFoundException('Prompt template not found');
        }
        try {
            const optimization = await this.prisma.promptOptimization.create({
                data: {
                    originalContent: template.content,
                    optimizedContent: await this.generateOptimizedPrompt(template.content),
                    improvement: {
                        clarity: 0.15,
                        effectiveness: 0.12,
                        tokenReduction: 0.08
                    },
                    status: 'PENDING',
                    promptTemplateId: id,
                    organizationId,
                }
            });
            this.apixGateway.emitToOrganization(organizationId, 'prompt_optimized', {
                templateId: id,
                optimization,
                userId,
                timestamp: new Date().toISOString()
            });
            return {
                success: true,
                data: optimization,
                message: 'Prompt optimization generated successfully'
            };
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to optimize prompt: ${error.message}`);
        }
    }
    async getCategories(organizationId) {
        const categories = await this.prisma.promptTemplate.findMany({
            where: {
                OR: [
                    { organizationId },
                    { isPublic: true }
                ]
            },
            select: { category: true },
            distinct: ['category']
        });
        return {
            success: true,
            data: categories.map(c => c.category).sort()
        };
    }
    async validateVariables(content, variables) {
        const variablePattern = /\{\{(\w+)\}\}/g;
        const usedVariables = [];
        let match;
        while ((match = variablePattern.exec(content)) !== null) {
            usedVariables.push(match[1]);
        }
        const definedVariables = variables.map(v => v.name);
        const missingVariables = usedVariables.filter(v => !definedVariables.includes(v));
        const unusedVariables = definedVariables.filter(v => !usedVariables.includes(v));
        return {
            success: true,
            data: {
                isValid: missingVariables.length === 0,
                usedVariables,
                definedVariables,
                missingVariables,
                unusedVariables
            }
        };
    }
    async incrementUsage(id) {
        try {
            await this.prisma.promptTemplate.update({
                where: { id },
                data: {
                    usage: {
                        increment: 1
                    }
                }
            });
        }
        catch (error) {
            console.error('Failed to increment prompt template usage:', error);
        }
    }
    async generateOptimizedPrompt(content) {
        return `${content}\n\n[Optimized for clarity and effectiveness]`;
    }
};
exports.PromptTemplatesService = PromptTemplatesService;
exports.PromptTemplatesService = PromptTemplatesService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        apix_gateway_1.ApixGateway])
], PromptTemplatesService);
//# sourceMappingURL=prompt-templates.service.js.map