import { Workflow } from '../workflows/workflow.entity';
import { Agent } from '../agents/agent.entity';
import { Tool } from '../tools/tool.entity';
export declare enum UserRole {
    ADMIN = "admin",
    USER = "user",
    VIEWER = "viewer"
}
export declare enum AuthProvider {
    LOCAL = "local",
    GOOGLE = "google",
    GITHUB = "github"
}
export declare class User {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    password: string;
    avatar: string;
    role: UserRole;
    authProvider: AuthProvider;
    googleId: string;
    githubId: string;
    isActive: boolean;
    lastLoginAt: Date;
    preferences: Record<string, any>;
    workflows: Workflow[];
    agents: Agent[];
    tools: Tool[];
    createdAt: Date;
    updatedAt: Date;
}
