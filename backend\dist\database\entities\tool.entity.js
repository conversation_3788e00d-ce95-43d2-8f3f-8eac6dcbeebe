"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Tool = exports.ToolType = exports.ToolStatus = void 0;
const typeorm_1 = require("typeorm");
const organization_entity_1 = require("./organization.entity");
const user_entity_1 = require("./user.entity");
const tool_execution_entity_1 = require("./tool-execution.entity");
var ToolStatus;
(function (ToolStatus) {
    ToolStatus["DRAFT"] = "DRAFT";
    ToolStatus["ACTIVE"] = "ACTIVE";
    ToolStatus["PAUSED"] = "PAUSED";
    ToolStatus["ARCHIVED"] = "ARCHIVED";
})(ToolStatus || (exports.ToolStatus = ToolStatus = {}));
var ToolType;
(function (ToolType) {
    ToolType["API"] = "API";
    ToolType["DATABASE"] = "DATABASE";
    ToolType["EMAIL"] = "EMAIL";
    ToolType["FILE"] = "FILE";
    ToolType["WEBHOOK"] = "WEBHOOK";
    ToolType["CUSTOM"] = "CUSTOM";
})(ToolType || (exports.ToolType = ToolType = {}));
let Tool = class Tool {
};
exports.Tool = Tool;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Tool.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Tool.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Tool.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'enum', enum: ToolType }),
    __metadata("design:type", String)
], Tool.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb' }),
    __metadata("design:type", Object)
], Tool.prototype, "config", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'enum', enum: ToolStatus, default: ToolStatus.DRAFT }),
    __metadata("design:type", String)
], Tool.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', default: {} }),
    __metadata("design:type", Object)
], Tool.prototype, "metrics", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 1 }),
    __metadata("design:type", Number)
], Tool.prototype, "version", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], Tool.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], Tool.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => organization_entity_1.Organization, organization => organization.tools),
    __metadata("design:type", organization_entity_1.Organization)
], Tool.prototype, "organization", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Tool.prototype, "organizationId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    __metadata("design:type", user_entity_1.User)
], Tool.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Tool.prototype, "createdById", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => tool_execution_entity_1.ToolExecution, execution => execution.tool),
    __metadata("design:type", Array)
], Tool.prototype, "executions", void 0);
exports.Tool = Tool = __decorate([
    (0, typeorm_1.Entity)('tools')
], Tool);
//# sourceMappingURL=tool.entity.js.map