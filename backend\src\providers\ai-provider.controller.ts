import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards, Req } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { AIProviderManagerService } from './ai-provider-manager.service';
import { AIProviderSelectorService } from './ai-provider-selector.service';
import { AIProviderIntegrationService } from './ai-provider-integration.service';
import { 
  CreateAIProviderDto, 
  UpdateAIProviderDto, 
  AIRequestDto, 
  ProviderSelectionDto,
  ProviderTestDto 
} from './dto/ai-provider.dto';

@ApiTags('AI Providers')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('api/v1/providers')
export class AIProviderController {
  constructor(
    private readonly providerManager: AIProviderManagerService,
    private readonly providerSelector: AIProviderSelectorService,
    private readonly providerIntegration: AIProviderIntegrationService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a new AI provider' })
  @ApiResponse({ status: 201, description: 'Provider created successfully' })
  async createProvider(
    @Body() createProviderDto: CreateAIProviderDto,
    @Req() req: any,
  ) {
    try {
      const provider = await this.providerManager.createProvider(
        createProviderDto,
        req.user.organizationId,
        req.user.id,
      );
      return {
        success: true,
        data: provider,
        message: 'AI provider created successfully',
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Failed to create AI provider',
      };
    }
  }

  @Get()
  @ApiOperation({ summary: 'Get all AI providers for organization' })
  @ApiResponse({ status: 200, description: 'Providers retrieved successfully' })
  async getProviders(@Req() req: any) {
    try {
      const providers = await this.providerManager.getProvidersByOrganization(
        req.user.organizationId,
      );
      return {
        success: true,
        data: providers,
        message: 'AI providers retrieved successfully',
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Failed to retrieve AI providers',
      };
    }
  }

  @Get('active')
  @ApiOperation({ summary: 'Get active AI providers' })
  @ApiResponse({ status: 200, description: 'Active providers retrieved successfully' })
  async getActiveProviders(@Req() req: any) {
    try {
      const providers = await this.providerManager.getActiveProviders(
        req.user.organizationId,
      );
      return {
        success: true,
        data: providers,
        message: 'Active AI providers retrieved successfully',
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Failed to retrieve active AI providers',
      };
    }
  }

  @Get('rankings')
  @ApiOperation({ summary: 'Get provider performance rankings' })
  @ApiResponse({ status: 200, description: 'Provider rankings retrieved successfully' })
  async getProviderRankings(@Req() req: any) {
    try {
      const rankings = await this.providerSelector.getProviderRankings(
        req.user.organizationId,
      );
      return {
        success: true,
        data: rankings,
        message: 'Provider rankings retrieved successfully',
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Failed to retrieve provider rankings',
      };
    }
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get AI provider by ID' })
  @ApiResponse({ status: 200, description: 'Provider retrieved successfully' })
  async getProvider(@Param('id') id: string, @Req() req: any) {
    try {
      const provider = await this.providerManager.getProviderById(
        id,
        req.user.organizationId,
      );
      return {
        success: true,
        data: provider,
        message: 'AI provider retrieved successfully',
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Failed to retrieve AI provider',
      };
    }
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update AI provider' })
  @ApiResponse({ status: 200, description: 'Provider updated successfully' })
  async updateProvider(
    @Param('id') id: string,
    @Body() updateProviderDto: UpdateAIProviderDto,
    @Req() req: any,
  ) {
    try {
      const provider = await this.providerManager.updateProvider(
        id,
        updateProviderDto,
        req.user.organizationId,
      );
      return {
        success: true,
        data: provider,
        message: 'AI provider updated successfully',
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Failed to update AI provider',
      };
    }
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete AI provider' })
  @ApiResponse({ status: 200, description: 'Provider deleted successfully' })
  async deleteProvider(@Param('id') id: string, @Req() req: any) {
    try {
      await this.providerManager.deleteProvider(id, req.user.organizationId);
      return {
        success: true,
        message: 'AI provider deleted successfully',
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Failed to delete AI provider',
      };
    }
  }

  @Post('select')
  @ApiOperation({ summary: 'Select optimal provider for request' })
  @ApiResponse({ status: 200, description: 'Provider selected successfully' })
  async selectProvider(
    @Body() selectionDto: ProviderSelectionDto,
    @Req() req: any,
  ) {
    try {
      const selection = await this.providerSelector.selectOptimalProvider({
        ...selectionDto,
        organizationId: req.user.organizationId,
      });
      return {
        success: true,
        data: selection,
        message: 'Optimal provider selected successfully',
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Failed to select optimal provider',
      };
    }
  }

  @Post('request')
  @ApiOperation({ summary: 'Process AI request' })
  @ApiResponse({ status: 200, description: 'Request processed successfully' })
  async processRequest(
    @Body() requestDto: AIRequestDto,
    @Req() req: any,
  ) {
    try {
      const response = await this.providerIntegration.processRequest({
        ...requestDto,
        organizationId: req.user.organizationId,
      });
      return {
        success: true,
        data: response,
        message: 'AI request processed successfully',
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Failed to process AI request',
      };
    }
  }

  @Post(':id/test')
  @ApiOperation({ summary: 'Test AI provider connection' })
  @ApiResponse({ status: 200, description: 'Provider tested successfully' })
  async testProvider(
    @Param('id') id: string,
    @Body() testDto: ProviderTestDto,
    @Req() req: any,
  ) {
    try {
      const result = await this.providerIntegration.testProvider(
        id,
        req.user.organizationId,
      );
      return {
        success: true,
        data: result,
        message: 'AI provider tested successfully',
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Failed to test AI provider',
      };
    }
  }

  @Get('usage/stats')
  @ApiOperation({ summary: 'Get usage statistics' })
  @ApiResponse({ status: 200, description: 'Usage stats retrieved successfully' })
  async getUsageStats(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Req() req?: any,
  ) {
    try {
      const stats = await this.providerManager.getUsageStats(
        req.user.organizationId,
        startDate ? new Date(startDate) : undefined,
        endDate ? new Date(endDate) : undefined,
      );
      return {
        success: true,
        data: stats,
        message: 'Usage statistics retrieved successfully',
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Failed to retrieve usage statistics',
      };
    }
  }
}