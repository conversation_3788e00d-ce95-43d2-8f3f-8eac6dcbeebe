import { Tool } from './tool.entity';
import { Session } from './session.entity';
export declare enum ExecutionStatus {
    PENDING = "PENDING",
    RUNNING = "RUNNING",
    COMPLETED = "COMPLETED",
    FAILED = "FAILED",
    TIMEOUT = "TIMEOUT"
}
export declare class ToolExecution {
    id: string;
    status: ExecutionStatus;
    input: Record<string, any>;
    output: Record<string, any>;
    metadata: {
        duration: number;
        retries: number;
        cost: number;
    };
    error: string;
    startedAt: Date;
    completedAt: Date;
    createdAt: Date;
    tool: Tool;
    toolId: string;
    session: Session;
    sessionId: string;
}
