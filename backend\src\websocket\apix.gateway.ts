import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  ConnectedSocket,
  MessageBody,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Injectable, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { PrismaService } from '../prisma/prisma.service';
import Redis from 'ioredis';

export interface ApixEvent {
  type: string;
  payload: any;
  organizationId: string;
  userId?: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

@Injectable()
@WebSocketGateway({
  cors: {
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true,
  },
  namespace: '/apix',
})
export class ApixGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(ApixGateway.name);
  private redis: Redis;
  private pubClient: Redis;
  private subClient: Redis;

  constructor(
    private jwtService: JwtService,
    private prisma: PrismaService,
  ) {
    this.redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT) || 6379,
      password: process.env.REDIS_PASSWORD,
    });

    this.pubClient = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT) || 6379,
      password: process.env.REDIS_PASSWORD,
    });

    this.subClient = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT) || 6379,
      password: process.env.REDIS_PASSWORD,
    });

    this.setupRedisSubscriptions();
  }

  async handleConnection(client: Socket) {
    try {
      const token = client.handshake.auth.token || client.handshake.headers.authorization?.replace('Bearer ', '');
      
      if (!token) {
        client.disconnect();
        return;
      }

      const payload = this.jwtService.verify(token);
      const { userId, organizationId } = payload;

      // Verify user exists and is active
      const user = await this.prisma.user.findUnique({
        where: { 
          id: userId, 
          isActive: true,
          organizationId, // Tenant isolation
        },
      });

      if (!user) {
        client.disconnect();
        return;
      }

      client.data.userId = userId;
      client.data.organizationId = organizationId;

      // Join organization room (tenant-scoped)
      await client.join(`org:${organizationId}`);
      await client.join(`user:${userId}`);

      // Store connection in Redis with tenant isolation
      await this.redis.hset(
        `connections:${organizationId}`,
        client.id,
        JSON.stringify({
          userId,
          organizationId,
          connectedAt: new Date().toISOString(),
        })
      );

      this.logger.log(`Client connected: ${client.id} (User: ${userId}, Org: ${organizationId})`);

      // Send connection confirmation
      client.emit('apix:connected', {
        type: 'connection_established',
        payload: { userId, organizationId },
        timestamp: new Date(),
      });

      // Log audit event
      await this.prisma.auditLog.create({
        data: {
          userId,
          organizationId,
          action: 'LOGIN',
          resource: 'websocket',
          resourceId: client.id,
          details: { action: 'websocket_connected' },
        },
      });

    } catch (error) {
      this.logger.error('Connection authentication failed:', error);
      client.disconnect();
    }
  }

  async handleDisconnect(client: Socket) {
    const { userId, organizationId } = client.data;
    
    if (organizationId) {
      await this.redis.hdel(`connections:${organizationId}`, client.id);
    }

    if (userId && organizationId) {
      // Log audit event
      await this.prisma.auditLog.create({
        data: {
          userId,
          organizationId,
          action: 'LOGOUT',
          resource: 'websocket',
          resourceId: client.id,
          details: { action: 'websocket_disconnected' },
        },
      });
    }

    this.logger.log(`Client disconnected: ${client.id}`);
  }

  @SubscribeMessage('apix:subscribe')
  async handleSubscribe(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { channels: string[] }
  ) {
    const { organizationId } = client.data;
    
    for (const channel of data.channels) {
      // Validate channel access based on organization (tenant isolation)
      if (this.validateChannelAccess(channel, organizationId)) {
        await client.join(channel);
        this.logger.log(`Client ${client.id} subscribed to ${channel}`);
      }
    }
  }

  @SubscribeMessage('apix:unsubscribe')
  async handleUnsubscribe(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { channels: string[] }
  ) {
    for (const channel of data.channels) {
      await client.leave(channel);
      this.logger.log(`Client ${client.id} unsubscribed from ${channel}`);
    }
  }

  // Core event publishing methods with tenant isolation
  async publishEvent(event: ApixEvent) {
    // Store event in Redis for replay (tenant-scoped)
    await this.redis.lpush(
      `events:${event.organizationId}`,
      JSON.stringify(event)
    );
    await this.redis.ltrim(`events:${event.organizationId}`, 0, 1000); // Keep last 1000 events

    // Publish to Redis pub/sub
    await this.pubClient.publish('apix:events', JSON.stringify(event));
  }

  async publishToOrganization(organizationId: string, event: Omit<ApixEvent, 'organizationId'>) {
    const fullEvent: ApixEvent = {
      ...event,
      organizationId,
      timestamp: new Date(),
    };

    await this.publishEvent(fullEvent);
  }

  async publishToUser(userId: string, organizationId: string, event: Omit<ApixEvent, 'organizationId' | 'userId'>) {
    const fullEvent: ApixEvent = {
      ...event,
      organizationId,
      userId,
      timestamp: new Date(),
    };

    await this.publishEvent(fullEvent);
  }

  // Specific event types
  async publishAgentEvent(type: string, payload: any, organizationId: string, userId?: string) {
    await this.publishEvent({
      type: `agent:${type}`,
      payload,
      organizationId,
      userId,
      timestamp: new Date(),
    });
  }

  async publishToolEvent(type: string, payload: any, organizationId: string, userId?: string) {
    await this.publishEvent({
      type: `tool:${type}`,
      payload,
      organizationId,
      userId,
      timestamp: new Date(),
    });
  }

  async publishSessionEvent(type: string, payload: any, organizationId: string, userId?: string) {
    await this.publishEvent({
      type: `session:${type}`,
      payload,
      organizationId,
      userId,
      timestamp: new Date(),
    });
  }

  async publishSystemEvent(type: string, payload: any, organizationId: string) {
    await this.publishEvent({
      type: `system:${type}`,
      payload,
      organizationId,
      timestamp: new Date(),
    });
  }

  private setupRedisSubscriptions() {
    this.subClient.subscribe('apix:events');
    
    this.subClient.on('message', (channel, message) => {
      if (channel === 'apix:events') {
        try {
          const event: ApixEvent = JSON.parse(message);
          this.broadcastEvent(event);
        } catch (error) {
          this.logger.error('Failed to parse Redis event:', error);
        }
      }
    });
  }

  private broadcastEvent(event: ApixEvent) {
    // Broadcast to organization room (tenant-scoped)
    this.server.to(`org:${event.organizationId}`).emit('apix:event', event);

    // If event is user-specific, also send to user room
    if (event.userId) {
      this.server.to(`user:${event.userId}`).emit('apix:event', event);
    }

    // Broadcast to specific channels based on event type (tenant-scoped)
    const eventChannel = `${event.type}:${event.organizationId}`;
    this.server.to(eventChannel).emit('apix:event', event);
  }

  private validateChannelAccess(channel: string, organizationId: string): boolean {
    // Ensure user can only access their organization's channels (tenant isolation)
    return channel.includes(organizationId) || channel.startsWith('public:');
  }

  // Event replay functionality (tenant-scoped)
  async getEventHistory(organizationId: string, limit: number = 100): Promise<ApixEvent[]> {
    const events = await this.redis.lrange(`events:${organizationId}`, 0, limit - 1);
    return events.map(event => JSON.parse(event));
  }

  // Get active connections for organization
  async getActiveConnections(organizationId: string) {
    const connections = await this.redis.hgetall(`connections:${organizationId}`);
    return Object.entries(connections).map(([socketId, data]) => ({
      socketId,
      ...JSON.parse(data),
    }));
  }
}