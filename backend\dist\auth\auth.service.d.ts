import { JwtService } from '@nestjs/jwt';
import { PrismaService } from '../prisma/prisma.service';
import { UserRole } from '@prisma/client';
export interface JwtPayload {
    userId: string;
    organizationId: string;
    role: UserRole;
    permissions: string[];
}
export interface LoginDto {
    email: string;
    password: string;
}
export interface RegisterDto {
    email: string;
    password: string;
    name: string;
    organizationName: string;
}
export declare class AuthService {
    private prisma;
    private jwtService;
    private redis;
    constructor(prisma: PrismaService, jwtService: JwtService);
    register(registerDto: RegisterDto): Promise<{
        accessToken: string;
        refreshToken: string;
        expiresIn: number;
        user: any;
        organization: any;
    }>;
    login(loginDto: LoginDto): Promise<{
        accessToken: string;
        refreshToken: string;
        expiresIn: number;
        user: any;
        organization: any;
    }>;
    refreshToken(refreshToken: string): Promise<{
        accessToken: string;
        refreshToken: string;
        expiresIn: number;
        user: any;
        organization: any;
    }>;
    logout(userId: string): Promise<{
        message: string;
    }>;
    validateUser(payload: JwtPayload): Promise<any>;
    hasPermission(user: any, permission: string): boolean;
    hasAnyPermission(user: any, permissions: string[]): boolean;
    hasAllPermissions(user: any, permissions: string[]): boolean;
    enforceOrganizationAccess(userId: string, organizationId: string): Promise<any>;
    createApiKey(userId: string, name: string, permissions?: string[]): Promise<any>;
    validateApiKey(key: string): Promise<any>;
    private generateTokens;
    private sanitizeUser;
    private getDefaultPermissions;
    private logAuditEvent;
}
