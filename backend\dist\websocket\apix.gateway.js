"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ApixGateway_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApixGateway = void 0;
const websockets_1 = require("@nestjs/websockets");
const socket_io_1 = require("socket.io");
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const prisma_service_1 = require("../prisma/prisma.service");
const ioredis_1 = require("ioredis");
let ApixGateway = ApixGateway_1 = class ApixGateway {
    constructor(jwtService, prisma) {
        this.jwtService = jwtService;
        this.prisma = prisma;
        this.logger = new common_1.Logger(ApixGateway_1.name);
        this.redis = new ioredis_1.default({
            host: process.env.REDIS_HOST || 'localhost',
            port: parseInt(process.env.REDIS_PORT) || 6379,
            password: process.env.REDIS_PASSWORD,
        });
        this.pubClient = new ioredis_1.default({
            host: process.env.REDIS_HOST || 'localhost',
            port: parseInt(process.env.REDIS_PORT) || 6379,
            password: process.env.REDIS_PASSWORD,
        });
        this.subClient = new ioredis_1.default({
            host: process.env.REDIS_HOST || 'localhost',
            port: parseInt(process.env.REDIS_PORT) || 6379,
            password: process.env.REDIS_PASSWORD,
        });
        this.setupRedisSubscriptions();
    }
    async handleConnection(client) {
        var _a;
        try {
            const token = client.handshake.auth.token || ((_a = client.handshake.headers.authorization) === null || _a === void 0 ? void 0 : _a.replace('Bearer ', ''));
            if (!token) {
                client.disconnect();
                return;
            }
            const payload = this.jwtService.verify(token);
            const { userId, organizationId } = payload;
            const user = await this.prisma.user.findUnique({
                where: {
                    id: userId,
                    isActive: true,
                    organizationId,
                },
            });
            if (!user) {
                client.disconnect();
                return;
            }
            client.data.userId = userId;
            client.data.organizationId = organizationId;
            await client.join(`org:${organizationId}`);
            await client.join(`user:${userId}`);
            await this.redis.hset(`connections:${organizationId}`, client.id, JSON.stringify({
                userId,
                organizationId,
                connectedAt: new Date().toISOString(),
            }));
            this.logger.log(`Client connected: ${client.id} (User: ${userId}, Org: ${organizationId})`);
            client.emit('apix:connected', {
                type: 'connection_established',
                payload: { userId, organizationId },
                timestamp: new Date(),
            });
            await this.prisma.auditLog.create({
                data: {
                    userId,
                    organizationId,
                    action: 'LOGIN',
                    resource: 'websocket',
                    resourceId: client.id,
                    details: { action: 'websocket_connected' },
                },
            });
        }
        catch (error) {
            this.logger.error('Connection authentication failed:', error);
            client.disconnect();
        }
    }
    async handleDisconnect(client) {
        const { userId, organizationId } = client.data;
        if (organizationId) {
            await this.redis.hdel(`connections:${organizationId}`, client.id);
        }
        if (userId && organizationId) {
            await this.prisma.auditLog.create({
                data: {
                    userId,
                    organizationId,
                    action: 'LOGOUT',
                    resource: 'websocket',
                    resourceId: client.id,
                    details: { action: 'websocket_disconnected' },
                },
            });
        }
        this.logger.log(`Client disconnected: ${client.id}`);
    }
    async handleSubscribe(client, data) {
        const { organizationId } = client.data;
        for (const channel of data.channels) {
            if (this.validateChannelAccess(channel, organizationId)) {
                await client.join(channel);
                this.logger.log(`Client ${client.id} subscribed to ${channel}`);
            }
        }
    }
    async handleUnsubscribe(client, data) {
        for (const channel of data.channels) {
            await client.leave(channel);
            this.logger.log(`Client ${client.id} unsubscribed from ${channel}`);
        }
    }
    async publishEvent(event) {
        await this.redis.lpush(`events:${event.organizationId}`, JSON.stringify(event));
        await this.redis.ltrim(`events:${event.organizationId}`, 0, 1000);
        await this.pubClient.publish('apix:events', JSON.stringify(event));
    }
    async publishToOrganization(organizationId, event) {
        const fullEvent = Object.assign(Object.assign({}, event), { organizationId, timestamp: new Date() });
        await this.publishEvent(fullEvent);
    }
    async publishToUser(userId, organizationId, event) {
        const fullEvent = Object.assign(Object.assign({}, event), { organizationId,
            userId, timestamp: new Date() });
        await this.publishEvent(fullEvent);
    }
    async publishAgentEvent(type, payload, organizationId, userId) {
        await this.publishEvent({
            type: `agent:${type}`,
            payload,
            organizationId,
            userId,
            timestamp: new Date(),
        });
    }
    async publishToolEvent(type, payload, organizationId, userId) {
        await this.publishEvent({
            type: `tool:${type}`,
            payload,
            organizationId,
            userId,
            timestamp: new Date(),
        });
    }
    async publishSessionEvent(type, payload, organizationId, userId) {
        await this.publishEvent({
            type: `session:${type}`,
            payload,
            organizationId,
            userId,
            timestamp: new Date(),
        });
    }
    async publishSystemEvent(type, payload, organizationId) {
        await this.publishEvent({
            type: `system:${type}`,
            payload,
            organizationId,
            timestamp: new Date(),
        });
    }
    setupRedisSubscriptions() {
        this.subClient.subscribe('apix:events');
        this.subClient.on('message', (channel, message) => {
            if (channel === 'apix:events') {
                try {
                    const event = JSON.parse(message);
                    this.broadcastEvent(event);
                }
                catch (error) {
                    this.logger.error('Failed to parse Redis event:', error);
                }
            }
        });
    }
    broadcastEvent(event) {
        this.server.to(`org:${event.organizationId}`).emit('apix:event', event);
        if (event.userId) {
            this.server.to(`user:${event.userId}`).emit('apix:event', event);
        }
        const eventChannel = `${event.type}:${event.organizationId}`;
        this.server.to(eventChannel).emit('apix:event', event);
    }
    validateChannelAccess(channel, organizationId) {
        return channel.includes(organizationId) || channel.startsWith('public:');
    }
    async getEventHistory(organizationId, limit = 100) {
        const events = await this.redis.lrange(`events:${organizationId}`, 0, limit - 1);
        return events.map(event => JSON.parse(event));
    }
    async getActiveConnections(organizationId) {
        const connections = await this.redis.hgetall(`connections:${organizationId}`);
        return Object.entries(connections).map(([socketId, data]) => (Object.assign({ socketId }, JSON.parse(data))));
    }
};
exports.ApixGateway = ApixGateway;
__decorate([
    (0, websockets_1.WebSocketServer)(),
    __metadata("design:type", socket_io_1.Server)
], ApixGateway.prototype, "server", void 0);
__decorate([
    (0, websockets_1.SubscribeMessage)('apix:subscribe'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [socket_io_1.Socket, Object]),
    __metadata("design:returntype", Promise)
], ApixGateway.prototype, "handleSubscribe", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('apix:unsubscribe'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [socket_io_1.Socket, Object]),
    __metadata("design:returntype", Promise)
], ApixGateway.prototype, "handleUnsubscribe", null);
exports.ApixGateway = ApixGateway = ApixGateway_1 = __decorate([
    (0, common_1.Injectable)(),
    (0, websockets_1.WebSocketGateway)({
        cors: {
            origin: process.env.FRONTEND_URL || 'http://localhost:3000',
            credentials: true,
        },
        namespace: '/apix',
    }),
    __metadata("design:paramtypes", [jwt_1.JwtService,
        prisma_service_1.PrismaService])
], ApixGateway);
//# sourceMappingURL=apix.gateway.js.map