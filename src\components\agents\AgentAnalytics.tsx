'use client';

import React, { useState, useEffect } from 'react';
import { Bar<PERSON>hart3, TrendingUp, Clock, DollarSign, Activity, Zap, AlertCircle } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';
import { AgentInstance, AgentAnalytics } from '@/lib/types/agent.types';
import { agentApi } from '@/lib/api/agent-api';

interface AgentAnalyticsProps {
  agent: AgentInstance;
  onClose: () => void;
}

export function AgentAnalytics({ agent, onClose }: AgentAnalyticsProps) {
  const [analytics, setAnalytics] = useState<AgentAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    loadAnalytics();
  }, [agent.id]);

  const loadAnalytics = async () => {
    try {
      setLoading(true);
      const response = await agentApi.getAgentAnalytics(agent.id!);
      
      if (response.success) {
        setAnalytics(response.data || null);
      } else {
        toast({
          title: 'Error',
          description: response.message,
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load analytics',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 4,
    }).format(amount);
  };

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  };

  const getSuccessRateColor = (rate: number) => {
    if (rate >= 0.9) return 'text-green-600';
    if (rate >= 0.7) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getProviderColor = (provider: string) => {
    const colors = {
      OPENAI: 'bg-green-500',
      CLAUDE: 'bg-purple-500',
      GEMINI: 'bg-blue-500',
      MISTRAL: 'bg-orange-500',
      GROQ: 'bg-red-500',
    };
    return colors[provider as keyof typeof colors] || 'bg-gray-500';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-600 mb-2">
          No Analytics Available
        </h3>
        <p className="text-gray-500">
          Analytics will appear after the agent has been executed
        </p>
      </div>
    );
  }

  return (
    <div className="bg-white space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Executions</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.totalExecutions}</div>
            <p className="text-xs text-muted-foreground">
              Since {new Date(analytics.lastExecuted).toLocaleDateString()}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getSuccessRateColor(analytics.successRate)}`}>
              {(analytics.successRate * 100).toFixed(1)}%
            </div>
            <Progress value={analytics.successRate * 100} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Response Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatDuration(analytics.averageResponseTime)}
            </div>
            <p className="text-xs text-muted-foreground">
              Error rate: {(analytics.errorRate * 100).toFixed(1)}%
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Cost</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(analytics.totalCost)}
            </div>
            <p className="text-xs text-muted-foreground">
              Avg per execution: {formatCurrency(analytics.totalCost / analytics.totalExecutions)}
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="performance" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="providers">Providers</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
        </TabsList>

        <TabsContent value="performance" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Performance Metrics</CardTitle>
                <CardDescription>Key performance indicators</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Success Rate</span>
                  <div className="flex items-center space-x-2">
                    <Progress value={analytics.successRate * 100} className="w-20" />
                    <span className={`text-sm font-semibold ${getSuccessRateColor(analytics.successRate)}`}>
                      {(analytics.successRate * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Error Rate</span>
                  <div className="flex items-center space-x-2">
                    <Progress value={analytics.errorRate * 100} className="w-20" />
                    <span className="text-sm font-semibold text-red-600">
                      {(analytics.errorRate * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Avg Response Time</span>
                  <span className="text-sm font-semibold">
                    {formatDuration(analytics.averageResponseTime)}
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Cost per Execution</span>
                  <span className="text-sm font-semibold">
                    {formatCurrency(analytics.totalCost / analytics.totalExecutions)}
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Agent Information</CardTitle>
                <CardDescription>Configuration and status</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Type</span>
                  <Badge variant="secondary">{agent.type}</Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Status</span>
                  <Badge variant={agent.status === 'ACTIVE' ? 'default' : 'secondary'}>
                    {agent.status}
                  </Badge>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Primary Provider</span>
                  <Badge className={getProviderColor(agent.primaryProvider)}>
                    {agent.primaryProvider}
                  </Badge>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Memory Config</span>
                  <span className="text-sm">
                    {agent.memoryConfig?.maxTokens || 4000} tokens
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Last Executed</span>
                  <span className="text-sm">
                    {new Date(analytics.lastExecuted).toLocaleString()}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="providers" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Provider Distribution</CardTitle>
              <CardDescription>Usage across different AI providers</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(analytics.providerDistribution).map(([provider, count]) => {
                  const percentage = (count / analytics.totalExecutions) * 100;
                  return (
                    <div key={provider} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <div className={`w-3 h-3 rounded-full ${getProviderColor(provider)}`} />
                          <span className="text-sm font-medium">{provider}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm text-gray-600">{count} executions</span>
                          <span className="text-sm font-semibold">{percentage.toFixed(1)}%</span>
                        </div>
                      </div>
                      <Progress value={percentage} className="h-2" />
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Performance Trends</CardTitle>
              <CardDescription>Daily performance over the last 7 days</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analytics.performanceTrend.map((trend, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium">
                        {new Date(trend.period).toLocaleDateString()}
                      </span>
                      <Badge variant="outline">
                        {trend.executions} executions
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Success Rate</span>
                        <div className={`font-semibold ${getSuccessRateColor(trend.successRate)}`}>
                          {(trend.successRate * 100).toFixed(1)}%
                        </div>
                      </div>
                      <div>
                        <span className="text-gray-600">Avg Time</span>
                        <div className="font-semibold">
                          {formatDuration(trend.averageTime)}
                        </div>
                      </div>
                      <div>
                        <span className="text-gray-600">Cost</span>
                        <div className="font-semibold">
                          {formatCurrency(trend.cost)}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}