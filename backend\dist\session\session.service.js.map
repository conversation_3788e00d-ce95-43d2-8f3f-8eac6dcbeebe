{"version": 3, "file": "session.service.js", "sourceRoot": "", "sources": ["../../src/session/session.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA+D;AAC/D,6DAAyD;AACzD,qCAA4B;AAC5B,2CAA+C;AAC/C,4DAAwD;AAqBjD,IAAM,cAAc,GAApB,MAAM,cAAc;IAGzB,YACU,MAAqB,EACrB,WAAwB;QADxB,WAAM,GAAN,MAAM,CAAe;QACrB,gBAAW,GAAX,WAAW,CAAa;QAEhC,IAAI,CAAC,KAAK,GAAG,IAAI,iBAAK,CAAC;YACrB,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,WAAW;YAC3C,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,IAAI;YAC9C,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc;SACrC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,gBAAkC;QACpD,MAAM,EAAE,MAAM,EAAE,cAAc,EAAE,QAAQ,GAAG,EAAE,EAAE,GAAG,gBAAgB,CAAC;QAGnE,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC/C,IAAI,EAAE;gBACJ,MAAM;gBACN,cAAc;gBACd,MAAM,EAAE,sBAAa,CAAC,MAAM;gBAC5B,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,EAAE;gBACX,QAAQ;gBACR,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;aACtD;SACF,CAAC,CAAC;QAGH,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CACpB,WAAW,OAAO,CAAC,EAAE,EAAE,EACvB,EAAE,GAAG,EAAE,GAAG,EAAE,EACZ,IAAI,CAAC,SAAS,CAAC;YACb,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,cAAc,EAAE,OAAO,CAAC,cAAc;YACtC,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,QAAQ,EAAE,OAAO,CAAC,QAAQ;SAC3B,CAAC,CACH,CAAC;QAGF,MAAM,IAAI,CAAC,WAAW,CAAC,mBAAmB,CACxC,SAAS,EACT,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE,EAAE,EACzB,cAAc,EACd,MAAM,CACP,CAAC;QAEF,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,SAAiB,EAAE,cAAsB;QAExD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,SAAS,EAAE,CAAC,CAAC;QACnE,IAAI,aAAa,EAAE,CAAC;YAClB,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAE9C,IAAI,WAAW,CAAC,cAAc,KAAK,cAAc,EAAE,CAAC;gBAClD,MAAM,IAAI,0BAAiB,CAAC,mBAAmB,CAAC,CAAC;YACnD,CAAC;YACD,OAAO,WAAW,CAAC;QACrB,CAAC;QAGD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YACnD,KAAK,EAAE;gBACL,EAAE,EAAE,SAAS;gBACb,cAAc;aACf;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,0BAAiB,CAAC,mBAAmB,CAAC,CAAC;QACnD,CAAC;QAGD,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CACpB,WAAW,SAAS,EAAE,EACtB,EAAE,GAAG,EAAE,GAAG,EAAE,EACZ,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CACxB,CAAC;QAEF,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,aAA4B,EAAE,cAAsB;QACnE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,GAAG,EAAE,EAAE,GAAG,aAAa,CAAC;QAElE,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;QAEjE,IAAI,OAAO,CAAC,MAAM,KAAK,sBAAa,CAAC,MAAM,EAAE,CAAC;YAC5C,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;QAC5D,CAAC;QAED,MAAM,OAAO,GAAG;YACd,EAAE,EAAE,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAClE,IAAI;YACJ,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,QAAQ;SACT,CAAC;QAEF,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;QACzE,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAGvB,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;QAGjF,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC/B,KAAK,EAAE;gBACL,EAAE,EAAE,SAAS;gBACb,cAAc;aACf;YACD,IAAI,EAAE;gBACJ,QAAQ,EAAE,eAAe;gBACzB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB;SACF,CAAC,CAAC;QAGH,MAAM,cAAc,mCAAQ,OAAO,KAAE,QAAQ,EAAE,eAAe,GAAE,CAAC;QACjE,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CACpB,WAAW,SAAS,EAAE,EACtB,EAAE,GAAG,EAAE,GAAG,EAAE,EACZ,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAC/B,CAAC;QAGF,MAAM,IAAI,CAAC,WAAW,CAAC,mBAAmB,CACxC,eAAe,EACf,EAAE,SAAS,EAAE,OAAO,EAAE,EACtB,cAAc,EACd,OAAO,CAAC,MAAM,CACf,CAAC;QAEF,OAAO,cAAc,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,gBAAkC,EAAE,cAAsB;QAC5E,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,gBAAgB,CAAC;QAEhD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;QAGjE,MAAM,aAAa,mCAAQ,OAAO,CAAC,OAAO,GAAK,OAAO,CAAE,CAAC;QAGzD,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC/B,KAAK,EAAE;gBACL,EAAE,EAAE,SAAS;gBACb,cAAc;aACf;YACD,IAAI,EAAE;gBACJ,OAAO,EAAE,aAAa;gBACtB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB;SACF,CAAC,CAAC;QAGH,MAAM,cAAc,mCAAQ,OAAO,KAAE,OAAO,EAAE,aAAa,GAAE,CAAC;QAC9D,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CACpB,WAAW,SAAS,EAAE,EACtB,EAAE,GAAG,EAAE,GAAG,EAAE,EACZ,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAC/B,CAAC;QAGF,MAAM,IAAI,CAAC,WAAW,CAAC,mBAAmB,CACxC,iBAAiB,EACjB,EAAE,SAAS,EAAE,OAAO,EAAE,EACtB,cAAc,EACd,OAAO,CAAC,MAAM,CACf,CAAC;QAEF,OAAO,cAAc,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,MAAc,EAAE,cAAsB,EAAE,QAAgB,EAAE;QAC9E,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YAClC,KAAK,EAAE;gBACL,MAAM;gBACN,cAAc;aACf;YACD,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;YAC9B,IAAI,EAAE,KAAK;SACZ,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,SAAiB,EAAE,cAAsB;QAC7D,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;QAGjE,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC/B,KAAK,EAAE;gBACL,EAAE,EAAE,SAAS;gBACb,cAAc;aACf;YACD,IAAI,EAAE;gBACJ,MAAM,EAAE,sBAAa,CAAC,SAAS;gBAC/B,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB;SACF,CAAC,CAAC;QAGH,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,SAAS,EAAE,CAAC,CAAC;QAG7C,MAAM,IAAI,CAAC,WAAW,CAAC,mBAAmB,CACxC,WAAW,EACX,EAAE,SAAS,EAAE,EACb,cAAc,EACd,OAAO,CAAC,MAAM,CACf,CAAC;QAEF,uCAAY,OAAO,KAAE,MAAM,EAAE,sBAAa,CAAC,SAAS,IAAG;IACzD,CAAC;IAGD,KAAK,CAAC,mBAAmB,CAAC,SAAiB,EAAE,cAAsB,EAAE,YAAoB;QACvF,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;QAEjE,MAAM,aAAa,GAAG;YACpB,SAAS,EAAE,OAAO,CAAC,EAAE;YACrB,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,cAAc,EAAE,OAAO,CAAC,cAAc;YACtC,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,cAAc,EAAE,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC;gBAC7C,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gBAC7B,CAAC,CAAC,EAAE;YACN,QAAQ,EAAE,OAAO,CAAC,QAAQ;SAC3B,CAAC;QAGF,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CACpB,kBAAkB,YAAY,IAAI,SAAS,EAAE,EAC7C,EAAE,GAAG,EAAE,EACP,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAC9B,CAAC;QAEF,OAAO,aAAa,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,SAAiB,EAAE,MAAc,EAAE,cAAsB;QAC9E,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,kBAAkB,MAAM,IAAI,SAAS,EAAE,CAAC,CAAC;QACpF,IAAI,CAAC,aAAa;YAAE,OAAO,IAAI,CAAC;QAEhC,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;QAG1C,IAAI,OAAO,CAAC,cAAc,KAAK,cAAc,EAAE,CAAC;YAC9C,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAGO,KAAK,CAAC,iBAAiB,CAAC,QAAe,EAAE,WAAgB,EAAE;QACjE,MAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,IAAI,GAAG,CAAC;QAEhD,IAAI,QAAQ,CAAC,MAAM,GAAG,WAAW,EAAE,CAAC;YAElC,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;YACrE,MAAM,cAAc,GAAG,QAAQ;iBAC5B,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,QAAQ,CAAC;iBACpC,KAAK,CAAC,CAAC,CAAC,WAAW,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;YAEjD,OAAO,CAAC,GAAG,cAAc,EAAE,GAAG,cAAc,CAAC,CAAC;QAChD,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAGD,KAAK,CAAC,sBAAsB;QAC1B,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YACzD,KAAK,EAAE;gBACL,SAAS,EAAE;oBACT,EAAE,EAAE,IAAI,IAAI,EAAE;iBACf;gBACD,MAAM,EAAE,sBAAa,CAAC,MAAM;aAC7B;SACF,CAAC,CAAC;QAEH,KAAK,MAAM,OAAO,IAAI,eAAe,EAAE,CAAC;YACtC,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;gBAC/B,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE;gBACzB,IAAI,EAAE,EAAE,MAAM,EAAE,sBAAa,CAAC,OAAO,EAAE;aACxC,CAAC,CAAC;YAEH,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;YAE9C,MAAM,IAAI,CAAC,WAAW,CAAC,mBAAmB,CACxC,SAAS,EACT,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE,EAAE,EACzB,OAAO,CAAC,cAAc,EACtB,OAAO,CAAC,MAAM,CACf,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AAlTY,wCAAc;yBAAd,cAAc;IAD1B,IAAA,mBAAU,GAAE;qCAKO,8BAAa;QACR,0BAAW;GALvB,cAAc,CAkT1B"}