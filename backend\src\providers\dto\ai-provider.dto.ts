import { IsString, IsEnum, IsObject, IsArray, IsBoolean, IsOptional, IsNumber, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { ProviderType } from '../database/entities/ai-provider.entity';

export class AIModelDto {
  @ApiProperty()
  @IsString()
  id: string;

  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty()
  @IsString()
  version: string;

  @ApiProperty()
  @IsObject()
  capabilities: {
    chat: boolean;
    completion: boolean;
    embedding: boolean;
    vision: boolean;
    functionCalling: boolean;
    codeGeneration: boolean;
    analysis: boolean;
    multimodal: boolean;
    streaming: boolean;
  };

  @ApiProperty({ default: false })
  @IsOptional()
  @IsBoolean()
  fineTuned?: boolean;

  @ApiProperty({ default: 4096 })
  @IsOptional()
  @IsNumber()
  contextLength?: number;

  @ApiProperty()
  @IsObject()
  costPer1KTokens: {
    input: number;
    output: number;
  };
}

export class CreateAIProviderDto {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty({ enum: ProviderType })
  @IsEnum(ProviderType)
  type: ProviderType;

  @ApiProperty()
  @IsObject()
  config: {
    apiKey?: string;
    baseUrl?: string;
    organizationId?: string;
    projectId?: string;
    region?: string;
    customHeaders?: Record<string, string>;
    timeout?: number;
    maxRetries?: number;
  };

  @ApiProperty({ type: [AIModelDto], required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AIModelDto)
  models?: AIModelDto[];

  @ApiProperty({ default: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsObject()
  quotaLimits?: {
    dailyRequests?: number;
    monthlyRequests?: number;
    dailyCostCents?: number;
    monthlyCostCents?: number;
  };
}

export class UpdateAIProviderDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsObject()
  config?: {
    apiKey?: string;
    baseUrl?: string;
    organizationId?: string;
    projectId?: string;
    region?: string;
    customHeaders?: Record<string, string>;
    timeout?: number;
    maxRetries?: number;
  };

  @ApiProperty({ type: [AIModelDto], required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AIModelDto)
  models?: AIModelDto[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsObject()
  quotaLimits?: {
    dailyRequests?: number;
    monthlyRequests?: number;
    dailyCostCents?: number;
    monthlyCostCents?: number;
  };
}

export class AIRequestDto {
  @ApiProperty()
  @IsString()
  requestId: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  providerId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  modelId?: string;

  @ApiProperty()
  @IsArray()
  messages: Array<{
    role: 'system' | 'user' | 'assistant';
    content: string;
  }>;

  @ApiProperty({ required: false, default: 0.7 })
  @IsOptional()
  @IsNumber()
  temperature?: number;

  @ApiProperty({ required: false, default: 1000 })
  @IsOptional()
  @IsNumber()
  maxTokens?: number;

  @ApiProperty({ required: false, default: false })
  @IsOptional()
  @IsBoolean()
  stream?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  capabilities?: string[];
}

export class ProviderSelectionDto {
  @ApiProperty()
  @IsArray()
  capabilities: string[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  maxCost?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  maxLatency?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  preferredProviders?: string[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  excludeProviders?: string[];
}

export class ProviderTestDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  testMessage?: string;

  @ApiProperty({ required: false, default: 50 })
  @IsOptional()
  @IsNumber()
  maxTokens?: number;
}