import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
} from '@nestjs/common';
import { RoleService, CreateRoleDto, UpdateRoleDto, RoleFilters } from './roles.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { PermissionGuard } from '../auth/permission.guard';
import { RequirePermissions } from '../auth/permissions.decorator';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

@ApiTags('roles')
@ApiBearerAuth()
@Controller('api/roles')
@UseGuards(JwtAuthGuard)
export class RoleController {
  constructor(private readonly roleService: RoleService) {}

  @Post()
  @UseGuards(PermissionGuard)
  @RequirePermissions('roles.write')
  @ApiOperation({ summary: 'Create a new role' })
  @ApiResponse({ status: 201, description: 'Role created successfully' })
  async createRole(@Body() createRoleDto: CreateRoleDto, @Request() req) {
    createRoleDto.organizationId = req.user.organizationId;
    return this.roleService.createRole(createRoleDto, req.user.userId);
  }

  @Get()
  @UseGuards(PermissionGuard)
  @RequirePermissions('roles.read')
  @ApiOperation({ summary: 'Get roles with filtering and pagination' })
  async getRoles(@Query() filters: RoleFilters, @Request() req) {
    filters.organizationId = req.user.organizationId;
    return this.roleService.getRoles(filters);
  }

  @Get(':id')
  @UseGuards(PermissionGuard)
  @RequirePermissions('roles.read')
  @ApiOperation({ summary: 'Get role by ID' })
  async getRoleById(@Param('id') id: string) {
    return this.roleService.getRoleById(id);
  }

  @Put(':id')
  @UseGuards(PermissionGuard)
  @RequirePermissions('roles.write')
  @ApiOperation({ summary: 'Update role' })
  async updateRole(
    @Param('id') id: string,
    @Body() updateRoleDto: UpdateRoleDto,
    @Request() req,
  ) {
    return this.roleService.updateRole(id, updateRoleDto, req.user.userId);
  }

  @Delete(':id')
  @UseGuards(PermissionGuard)
  @RequirePermissions('roles.write')
  @ApiOperation({ summary: 'Delete role' })
  async deleteRole(@Param('id') id: string, @Request() req) {
    return this.roleService.deleteRole(id, req.user.userId);
  }

  @Post(':roleId/users/:userId')
  @UseGuards(PermissionGuard)
  @RequirePermissions('roles.write')
  @ApiOperation({ summary: 'Assign role to user' })
  async assignRoleToUser(
    @Param('roleId') roleId: string,
    @Param('userId') userId: string,
    @Request() req,
  ) {
    return this.roleService.assignRoleToUser(roleId, userId, req.user.userId);
  }

  @Delete(':roleId/users/:userId')
  @UseGuards(PermissionGuard)
  @RequirePermissions('roles.write')
  @ApiOperation({ summary: 'Remove role from user' })
  async removeRoleFromUser(
    @Param('roleId') roleId: string,
    @Param('userId') userId: string,
    @Request() req,
  ) {
    return this.roleService.removeRoleFromUser(roleId, userId, req.user.userId);
  }
}