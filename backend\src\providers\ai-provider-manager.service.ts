import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { InjectRedis } from '@nestjs-modules/ioredis';
import Redis from 'ioredis';
import { AIProvider, AIModel, ProviderUsage } from '../database/entities/ai-provider.entity';
import { ApixGateway } from '../websocket/apix.gateway';
import { CreateAIProviderDto, UpdateAIProviderDto } from './dto/ai-provider.dto';

@Injectable()
export class AIProviderManagerService {
  private readonly logger = new Logger(AIProviderManagerService.name);

  constructor(
    @InjectRepository(AIProvider)
    private providerRepository: Repository<AIProvider>,
    @InjectRepository(AIModel)
    private modelRepository: Repository<AIModel>,
    @InjectRepository(ProviderUsage)
    private usageRepository: Repository<ProviderUsage>,
    @InjectRedis() private readonly redis: Redis,
    private apixGateway: ApixGateway,
  ) {}

  async createProvider(
    createProviderDto: CreateAIProviderDto,
    organizationId: string,
    userId: string,
  ): Promise<AIProvider> {
    try {
      // Validate provider configuration
      await this.validateProviderConfig(createProviderDto.type, createProviderDto.config);

      const provider = this.providerRepository.create({
        ...createProviderDto,
        organizationId,
        createdBy: userId,
      });

      const savedProvider = await this.providerRepository.save(provider);

      // Create models for this provider
      if (createProviderDto.models && createProviderDto.models.length > 0) {
        const models = createProviderDto.models.map(modelData => 
          this.modelRepository.create({
            ...modelData,
            providerId: savedProvider.id,
          })
        );
        await this.modelRepository.save(models);
      }

      // Cache provider configuration
      await this.cacheProviderConfig(savedProvider);

      // Emit APIX event
      this.apixGateway.emitToOrganization(organizationId, 'provider.created', {
        providerId: savedProvider.id,
        organizationId,
        name: savedProvider.name,
        type: savedProvider.type,
        timestamp: new Date(),
      });

      this.logger.log(`AI Provider created: ${savedProvider.id} for organization: ${organizationId}`);
      return savedProvider;
    } catch (error) {
      this.logger.error(`Failed to create AI provider: ${error.message}`, error.stack);
      throw error;
    }
  }

  async updateProvider(
    providerId: string,
    updateProviderDto: UpdateAIProviderDto,
    organizationId: string,
  ): Promise<AIProvider> {
    try {
      const provider = await this.providerRepository.findOne({
        where: { id: providerId, organizationId },
      });

      if (!provider) {
        throw new Error('Provider not found');
      }

      // Validate new configuration if provided
      if (updateProviderDto.config) {
        await this.validateProviderConfig(provider.type, updateProviderDto.config);
      }

      Object.assign(provider, updateProviderDto);
      const updatedProvider = await this.providerRepository.save(provider);

      // Update cache
      await this.cacheProviderConfig(updatedProvider);

      // Emit APIX event
      this.apixGateway.emitToOrganization(organizationId, 'provider.updated', {
        providerId: updatedProvider.id,
        changes: Object.keys(updateProviderDto),
        timestamp: new Date(),
      });

      return updatedProvider;
    } catch (error) {
      this.logger.error(`Failed to update AI provider ${providerId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async deleteProvider(providerId: string, organizationId: string): Promise<void> {
    try {
      const result = await this.providerRepository.delete({
        id: providerId,
        organizationId,
      });

      if (result.affected === 0) {
        throw new Error('Provider not found');
      }

      // Remove from cache
      await this.redis.del(`provider:${providerId}`);

      // Emit APIX event
      this.apixGateway.emitToOrganization(organizationId, 'provider.deleted', {
        providerId,
        timestamp: new Date(),
      });

      this.logger.log(`AI Provider deleted: ${providerId}`);
    } catch (error) {
      this.logger.error(`Failed to delete AI provider ${providerId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getProvidersByOrganization(organizationId: string): Promise<AIProvider[]> {
    return this.providerRepository.find({
      where: { organizationId },
      relations: ['models'],
      order: { createdAt: 'DESC' },
    });
  }

  async getProviderById(providerId: string, organizationId: string): Promise<AIProvider> {
    const provider = await this.providerRepository.findOne({
      where: { id: providerId, organizationId },
      relations: ['models'],
    });

    if (!provider) {
      throw new Error('Provider not found');
    }

    return provider;
  }

  async getActiveProviders(organizationId: string): Promise<AIProvider[]> {
    return this.providerRepository.find({
      where: { organizationId, isActive: true },
      relations: ['models'],
      order: { createdAt: 'DESC' },
    });
  }

  async trackUsage(
    providerId: string,
    usage: {
      requests: number;
      tokensUsed: number;
      costInCents: number;
    },
  ): Promise<void> {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      let usageRecord = await this.usageRepository.findOne({
        where: { providerId, date: today },
      });

      if (usageRecord) {
        usageRecord.requests += usage.requests;
        usageRecord.tokensUsed += usage.tokensUsed;
        usageRecord.costInCents += usage.costInCents;
      } else {
        usageRecord = this.usageRepository.create({
          providerId,
          date: today,
          ...usage,
        });
      }

      await this.usageRepository.save(usageRecord);

      // Emit usage event
      const provider = await this.providerRepository.findOne({ where: { id: providerId } });
      if (provider) {
        this.apixGateway.emitToOrganization(provider.organizationId, 'provider.usage.report', {
          providerId,
          date: today,
          requests: usage.requests,
          tokensUsed: usage.tokensUsed,
          costInCents: usage.costInCents,
          timestamp: new Date(),
        });
      }
    } catch (error) {
      this.logger.error(`Failed to track usage for provider ${providerId}: ${error.message}`, error.stack);
    }
  }

  async getUsageStats(
    organizationId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<{
    totalRequests: number;
    totalTokens: number;
    totalCostInCents: number;
    providerBreakdown: Array<{
      providerId: string;
      providerName: string;
      requests: number;
      tokens: number;
      costInCents: number;
    }>;
  }> {
    try {
      const providers = await this.getProvidersByOrganization(organizationId);
      const providerIds = providers.map(p => p.id);

      const query = this.usageRepository.createQueryBuilder('usage')
        .where('usage.providerId IN (:...providerIds)', { providerIds });

      if (startDate) {
        query.andWhere('usage.date >= :startDate', { startDate });
      }
      if (endDate) {
        query.andWhere('usage.date <= :endDate', { endDate });
      }

      const usageRecords = await query.getMany();

      let totalRequests = 0;
      let totalTokens = 0;
      let totalCostInCents = 0;

      const providerBreakdown = providers.map(provider => {
        const providerUsage = usageRecords.filter(u => u.providerId === provider.id);
        const requests = providerUsage.reduce((sum, u) => sum + u.requests, 0);
        const tokens = providerUsage.reduce((sum, u) => sum + u.tokensUsed, 0);
        const costInCents = providerUsage.reduce((sum, u) => sum + u.costInCents, 0);

        totalRequests += requests;
        totalTokens += tokens;
        totalCostInCents += costInCents;

        return {
          providerId: provider.id,
          providerName: provider.name,
          requests,
          tokens,
          costInCents,
        };
      });

      return {
        totalRequests,
        totalTokens,
        totalCostInCents,
        providerBreakdown,
      };
    } catch (error) {
      this.logger.error(`Failed to get usage stats for organization ${organizationId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  private async validateProviderConfig(type: string, config: any): Promise<void> {
    switch (type) {
      case 'OPENAI':
        if (!config.apiKey) {
          throw new Error('OpenAI API key is required');
        }
        break;
      case 'CLAUDE':
        if (!config.apiKey) {
          throw new Error('Claude API key is required');
        }
        break;
      case 'GEMINI':
        if (!config.apiKey) {
          throw new Error('Gemini API key is required');
        }
        break;
      case 'MISTRAL':
        if (!config.apiKey) {
          throw new Error('Mistral API key is required');
        }
        break;
      case 'GROQ':
        if (!config.apiKey) {
          throw new Error('Groq API key is required');
        }
        break;
      default:
        throw new Error(`Unsupported provider type: ${type}`);
    }
  }

  private async cacheProviderConfig(provider: AIProvider): Promise<void> {
    const cacheKey = `provider:${provider.id}`;
    const cacheData = {
      id: provider.id,
      name: provider.name,
      type: provider.type,
      config: provider.config,
      isActive: provider.isActive,
      organizationId: provider.organizationId,
    };

    await this.redis.setex(cacheKey, 3600, JSON.stringify(cacheData)); // Cache for 1 hour
  }

  async getCachedProvider(providerId: string): Promise<any> {
    const cacheKey = `provider:${providerId}`;
    const cachedData = await this.redis.get(cacheKey);
    
    if (cachedData) {
      return JSON.parse(cachedData);
    }

    return null;
  }
}