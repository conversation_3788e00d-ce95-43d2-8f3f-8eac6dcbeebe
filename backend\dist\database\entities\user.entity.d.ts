import { Organization } from './organization.entity';
import { Session } from './session.entity';
export declare enum UserRole {
    SUPER_ADMIN = "SUPER_ADMIN",
    ORG_ADMIN = "ORG_ADMIN",
    DEVELOPER = "DEVELOPER",
    VIEWER = "VIEWER"
}
export declare class User {
    id: string;
    email: string;
    name: string;
    avatar: string;
    passwordHash: string;
    role: UserRole;
    permissions: string[];
    preferences: Record<string, any>;
    isActive: boolean;
    lastLoginAt: Date;
    createdAt: Date;
    updatedAt: Date;
    organization: Organization;
    organizationId: string;
    sessions: Session[];
}
