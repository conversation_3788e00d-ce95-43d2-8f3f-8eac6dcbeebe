import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, ManyToOne, Join<PERSON><PERSON>um<PERSON> } from 'typeorm';
import { Agent } from './agent.entity';
import { Session } from './session.entity';

export enum ExecutionStatus {
  PENDING = 'PENDING',
  RUNNING = 'RUNNING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED'
}

@Entity('agent_executions')
export class AgentExecution {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column('uuid')
  agentId!: string;

  @ManyToOne(() => Agent, agent => agent.executions)
  @JoinColumn({ name: 'agentId' })
  agent!: Agent;

  @Column('uuid', { nullable: true })
  sessionId!: string;

  @ManyToOne(() => Session, { nullable: true })
  @JoinColumn({ name: 'sessionId' })
  session!: Session;

  @Column('text') 
  input!: string;

  @Column('text', { nullable: true })
  output!: string;

  @Column({
    type: 'enum',
    enum: ExecutionStatus,
    default: ExecutionStatus.PENDING
  })
  status!: ExecutionStatus;

  @Column('jsonb', { nullable: true })
  context!: Record<string, any>;

  @Column('jsonb', { nullable: true })
  metadata!: {
    provider: string;
    model: string;
    tokens: {
      input: number;
      output: number;
      total: number;
      promptTokens: number;
      completionTokens: number;
      totalTokens: number;
    };
    cost: number;
    duration: number;
    retryCount: number;
  };

  @Column('text', { nullable: true })
  errorMessage!: string;

  @Column('jsonb', { nullable: true })
  errorDetails!: Record<string, any>;

  @Column('uuid')
  organizationId!: string;

  @CreateDateColumn()
  createdAt!: Date;

  @CreateDateColumn()
  completedAt!: Date;
}