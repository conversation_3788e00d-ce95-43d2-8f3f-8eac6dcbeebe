import { Organization } from './organization.entity';
import { User } from './user.entity';
import { ToolExecution } from './tool-execution.entity';
export declare enum ToolStatus {
    DRAFT = "DRAFT",
    ACTIVE = "ACTIVE",
    PAUSED = "PAUSED",
    ARCHIVED = "ARCHIVED"
}
export declare enum ToolType {
    API = "API",
    DATABASE = "DATABASE",
    EMAIL = "EMAIL",
    FILE = "FILE",
    WEBHOOK = "WEBHOOK",
    CUSTOM = "CUSTOM"
}
export declare class Tool {
    id: string;
    name: string;
    description: string;
    type: ToolType;
    config: {
        endpoint?: string;
        method?: string;
        headers?: Record<string, string>;
        authentication?: {
            type: 'none' | 'bearer' | 'basic' | 'api_key';
            credentials: Record<string, string>;
        };
        inputSchema: Record<string, any>;
        outputSchema: Record<string, any>;
        timeout: number;
        retries: number;
    };
    status: ToolStatus;
    metrics: {
        totalExecutions: number;
        successRate: number;
        avgResponseTime: number;
        totalCost: number;
    };
    version: number;
    createdAt: Date;
    updatedAt: Date;
    organization: Organization;
    organizationId: string;
    createdBy: User;
    createdById: string;
    executions: ToolExecution[];
}
