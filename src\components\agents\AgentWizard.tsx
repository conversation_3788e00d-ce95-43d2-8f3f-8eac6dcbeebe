'use client';

import React, { useState } from 'react';
import { ChevronLeft, ChevronRight, Check, <PERSON>rkles, <PERSON><PERSON>, Brain, Zap } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Slider } from '@/components/ui/slider';
import { AgentTemplate, AgentType, ProviderType, CreateAgentInstanceDto } from '@/lib/types/agent.types';

interface AgentWizardProps {
  templates: AgentTemplate[];
  onComplete: (agentData: CreateAgentInstanceDto) => void;
  onCancel: () => void;
}

const WIZARD_STEPS = [
  { id: 'type', title: 'Agent Type', description: 'Choose the type of agent you want to create' },
  { id: 'template', title: 'Template', description: 'Select a template or start from scratch' },
  { id: 'basic', title: 'Basic Info', description: 'Configure basic agent settings' },
  { id: 'provider', title: 'AI Provider', description: 'Choose your AI provider and model' },
  { id: 'memory', title: 'Memory & Skills', description: 'Configure memory and capabilities' },
  { id: 'review', title: 'Review', description: 'Review and create your agent' },
];

const AGENT_TYPE_INFO = {
  [AgentType.BASIC]: {
    icon: <Sparkles className="w-8 h-8" />,
    title: 'Basic Agent',
    description: 'Simple conversational agent for basic interactions',
    features: ['Text conversations', 'Basic memory', 'Single provider'],
  },
  [AgentType.TOOL_DRIVEN]: {
    icon: <Settings className="w-8 h-8" />,
    title: 'Tool-Driven Agent',
    description: 'Agent that can use external tools and APIs',
    features: ['Tool integration', 'API calls', 'Function execution'],
  },
  [AgentType.HYBRID]: {
    icon: <Brain className="w-8 h-8" />,
    title: 'Hybrid Agent',
    description: 'Combines chat, tools, and API capabilities',
    features: ['Chat + Tools', 'API integration', 'Complex workflows'],
  },
  [AgentType.MULTI_TASK]: {
    icon: <Zap className="w-8 h-8" />,
    title: 'Multi-Task Agent',
    description: 'Handles multiple concurrent tasks',
    features: ['Parallel processing', 'Task queuing', 'Priority handling'],
  },
  [AgentType.MULTI_PROVIDER]: {
    icon: <Brain className="w-8 h-8" />,
    title: 'Multi-Provider Agent',
    description: 'Uses multiple AI providers with fallbacks',
    features: ['Provider switching', 'Fallback chains', 'Cost optimization'],
  },
  [AgentType.COLLABORATIVE]: {
    icon: <Zap className="w-8 h-8" />,
    title: 'Collaborative Agent',
    description: 'Works with other agents in teams',
    features: ['Agent coordination', 'Shared context', 'Team workflows'],
  },
};

export function AgentWizard({ templates, onComplete, onCancel }: AgentWizardProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState<Partial<CreateAgentInstanceDto>>({
    type: AgentType.BASIC,
    primaryProvider: ProviderType.OPENAI,
    fallbackProviders: [],
    memoryConfig: {
      maxTokens: 4000,
      retentionDays: 30,
      enableLongTerm: false,
    },
    skills: [],
    config: {
      personality: 'helpful',
      responseStyle: 'professional',
      maxResponseLength: 500,
    },
  });

  const handleNext = () => {
    if (currentStep < WIZARD_STEPS.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = () => {
    if (formData.name && formData.templateId && formData.type && formData.primaryProvider) {
      onComplete(formData as CreateAgentInstanceDto);
    }
  };

  const updateFormData = (updates: Partial<CreateAgentInstanceDto>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  };

  const updateConfig = (configUpdates: Record<string, any>) => {
    setFormData(prev => ({
      ...prev,
      config: { ...prev.config, ...configUpdates },
    }));
  };

  const updateMemoryConfig = (memoryUpdates: Partial<typeof formData.memoryConfig>) => {
    setFormData(prev => ({
      ...prev,
      memoryConfig: { ...prev.memoryConfig, ...memoryUpdates },
    }));
  };

  const renderStepContent = () => {
    switch (WIZARD_STEPS[currentStep].id) {
      case 'type':
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold mb-2">Choose Your Agent Type</h2>
              <p className="text-gray-600">Select the type that best fits your use case</p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(AGENT_TYPE_INFO).map(([type, info]) => (
                <Card
                  key={type}
                  className={`cursor-pointer transition-all duration-200 ${
                    formData.type === type
                      ? 'ring-2 ring-blue-500 bg-blue-50'
                      : 'hover:shadow-md'
                  }`}
                  onClick={() => updateFormData({ type: type as AgentType })}
                >
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded-lg ${
                        formData.type === type ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'
                      }`}>
                        {info.icon}
                      </div>
                      <div>
                        <CardTitle className="text-lg">{info.title}</CardTitle>
                        <CardDescription>{info.description}</CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-1">
                      {info.features.map((feature, index) => (
                        <li key={index} className="flex items-center text-sm text-gray-600">
                          <Check className="w-4 h-4 mr-2 text-green-500" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        );

      case 'template':
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold mb-2">Choose a Template</h2>
              <p className="text-gray-600">Start with a pre-built template or create from scratch</p>
            </div>
            
            <Card
              className={`cursor-pointer transition-all duration-200 ${
                formData.templateId === 'custom'
                  ? 'ring-2 ring-blue-500 bg-blue-50'
                  : 'hover:shadow-md'
              }`}
              onClick={() => updateFormData({ templateId: 'custom' })}
            >
              <CardHeader>
                <CardTitle>Custom Agent</CardTitle>
                <CardDescription>Start from scratch with full customization</CardDescription>
              </CardHeader>
            </Card>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {templates
                .filter(template => template.type === formData.type || template.type === AgentType.BASIC)
                .map(template => (
                <Card
                  key={template.id}
                  className={`cursor-pointer transition-all duration-200 ${
                    formData.templateId === template.id
                      ? 'ring-2 ring-blue-500 bg-blue-50'
                      : 'hover:shadow-md'
                  }`}
                  onClick={() => updateFormData({ templateId: template.id })}
                >
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">{template.name}</CardTitle>
                      <Badge variant="secondary">{template.category}</Badge>
                    </div>
                    <CardDescription>{template.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-1">
                      {template.skills.slice(0, 3).map(skill => (
                        <Badge key={skill} variant="outline" className="text-xs">
                          {skill}
                        </Badge>
                      ))}
                      {template.skills.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{template.skills.length - 3} more
                        </Badge>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        );

      case 'basic':
        return (
          <div className="space-y-6 max-w-2xl mx-auto">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold mb-2">Basic Configuration</h2>
              <p className="text-gray-600">Set up your agent's basic information</p>
            </div>

            <div className="space-y-4">
              <div>
                <Label htmlFor="name">Agent Name *</Label>
                <Input
                  id="name"
                  placeholder="Enter agent name"
                  value={formData.name || ''}
                  onChange={(e) => updateFormData({ name: e.target.value })}
                />
              </div>

              <div>
                <Label htmlFor="personality">Personality</Label>
                <Select
                  value={formData.config?.personality || 'helpful'}
                  onValueChange={(value) => updateConfig({ personality: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="helpful">Helpful</SelectItem>
                    <SelectItem value="friendly">Friendly</SelectItem>
                    <SelectItem value="professional">Professional</SelectItem>
                    <SelectItem value="creative">Creative</SelectItem>
                    <SelectItem value="analytical">Analytical</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="responseStyle">Response Style</Label>
                <Select
                  value={formData.config?.responseStyle || 'professional'}
                  onValueChange={(value) => updateConfig({ responseStyle: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="professional">Professional</SelectItem>
                    <SelectItem value="casual">Casual</SelectItem>
                    <SelectItem value="detailed">Detailed</SelectItem>
                    <SelectItem value="concise">Concise</SelectItem>
                    <SelectItem value="engaging">Engaging</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="maxResponseLength">Max Response Length</Label>
                <div className="mt-2">
                  <Slider
                    value={[formData.config?.maxResponseLength || 500]}
                    onValueChange={(value) => updateConfig({ maxResponseLength: value[0] })}
                    max={2000}
                    min={100}
                    step={50}
                    className="w-full"
                  />
                  <div className="flex justify-between text-sm text-gray-500 mt-1">
                    <span>100</span>
                    <span>{formData.config?.maxResponseLength || 500} characters</span>
                    <span>2000</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'provider':
        return (
          <div className="space-y-6 max-w-2xl mx-auto">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold mb-2">AI Provider Configuration</h2>
              <p className="text-gray-600">Choose your primary AI provider and fallbacks</p>
            </div>

            <div className="space-y-4">
              <div>
                <Label htmlFor="primaryProvider">Primary Provider *</Label>
                <Select
                  value={formData.primaryProvider}
                  onValueChange={(value) => updateFormData({ primaryProvider: value as ProviderType })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(ProviderType).map(provider => (
                      <SelectItem key={provider} value={provider}>{provider}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Fallback Providers (Optional)</Label>
                <div className="grid grid-cols-2 gap-2 mt-2">
                  {Object.values(ProviderType)
                    .filter(provider => provider !== formData.primaryProvider)
                    .map(provider => (
                    <div key={provider} className="flex items-center space-x-2">
                      <Checkbox
                        id={provider}
                        checked={formData.fallbackProviders?.includes(provider) || false}
                        onCheckedChange={(checked) => {
                          const current = formData.fallbackProviders || [];
                          if (checked) {
                            updateFormData({ fallbackProviders: [...current, provider] });
                          } else {
                            updateFormData({ 
                              fallbackProviders: current.filter(p => p !== provider) 
                            });
                          }
                        }}
                      />
                      <Label htmlFor={provider}>{provider}</Label>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        );

      case 'memory':
        return (
          <div className="space-y-6 max-w-2xl mx-auto">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold mb-2">Memory & Skills</h2>
              <p className="text-gray-600">Configure memory settings and capabilities</p>
            </div>

            <div className="space-y-6">
              <div>
                <Label>Memory Configuration</Label>
                <div className="space-y-4 mt-2">
                  <div>
                    <Label htmlFor="maxTokens">Max Tokens</Label>
                    <Slider
                      value={[formData.memoryConfig?.maxTokens || 4000]}
                      onValueChange={(value) => updateMemoryConfig({ maxTokens: value[0] })}
                      max={16000}
                      min={1000}
                      step={500}
                      className="w-full"
                    />
                    <div className="flex justify-between text-sm text-gray-500 mt-1">
                      <span>1K</span>
                      <span>{formData.memoryConfig?.maxTokens || 4000} tokens</span>
                      <span>16K</span>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="retentionDays">Retention Days</Label>
                    <Slider
                      value={[formData.memoryConfig?.retentionDays || 30]}
                      onValueChange={(value) => updateMemoryConfig({ retentionDays: value[0] })}
                      max={365}
                      min={1}
                      step={1}
                      className="w-full"
                    />
                    <div className="flex justify-between text-sm text-gray-500 mt-1">
                      <span>1 day</span>
                      <span>{formData.memoryConfig?.retentionDays || 30} days</span>
                      <span>365 days</span>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="enableLongTerm"
                      checked={formData.memoryConfig?.enableLongTerm || false}
                      onCheckedChange={(checked) => updateMemoryConfig({ enableLongTerm: !!checked })}
                    />
                    <Label htmlFor="enableLongTerm">Enable Long-term Memory</Label>
                  </div>
                </div>
              </div>

              <div>
                <Label htmlFor="skills">Skills (Optional)</Label>
                <Textarea
                  id="skills"
                  placeholder="Enter skills separated by commas (e.g., customer_support, product_knowledge)"
                  value={formData.skills?.join(', ') || ''}
                  onChange={(e) => {
                    const skills = e.target.value.split(',').map(s => s.trim()).filter(s => s);
                    updateFormData({ skills });
                  }}
                />
              </div>
            </div>
          </div>
        );

      case 'review':
        return (
          <div className="space-y-6 max-w-2xl mx-auto">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold mb-2">Review Your Agent</h2>
              <p className="text-gray-600">Review your configuration before creating the agent</p>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Agent Configuration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Name</Label>
                    <p className="font-semibold">{formData.name}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Type</Label>
                    <p className="font-semibold">{formData.type}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Primary Provider</Label>
                    <p className="font-semibold">{formData.primaryProvider}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Fallback Providers</Label>
                    <p className="font-semibold">
                      {formData.fallbackProviders?.length ? formData.fallbackProviders.join(', ') : 'None'}
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Max Tokens</Label>
                    <p className="font-semibold">{formData.memoryConfig?.maxTokens}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Retention</Label>
                    <p className="font-semibold">{formData.memoryConfig?.retentionDays} days</p>
                  </div>
                </div>
                
                {formData.skills && formData.skills.length > 0 && (
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Skills</Label>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {formData.skills.map(skill => (
                        <Badge key={skill} variant="secondary">{skill}</Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="bg-white">
      {/* Progress Steps */}
      <div className="flex items-center justify-between mb-8">
        {WIZARD_STEPS.map((step, index) => (
          <div key={step.id} className="flex items-center">
            <div className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
              index <= currentStep
                ? 'bg-blue-600 text-white'
                : 'bg-gray-200 text-gray-600'
            }`}>
              {index < currentStep ? <Check className="w-4 h-4" /> : index + 1}
            </div>
            {index < WIZARD_STEPS.length - 1 && (
              <div className={`w-12 h-0.5 mx-2 ${
                index < currentStep ? 'bg-blue-600' : 'bg-gray-200'
              }`} />
            )}
          </div>
        ))}
      </div>

      {/* Step Content */}
      <div className="min-h-[500px]">
        {renderStepContent()}
      </div>

      {/* Navigation */}
      <div className="flex items-center justify-between mt-8 pt-6 border-t">
        <Button
          variant="outline"
          onClick={currentStep === 0 ? onCancel : handlePrevious}
        >
          <ChevronLeft className="w-4 h-4 mr-2" />
          {currentStep === 0 ? 'Cancel' : 'Previous'}
        </Button>

        <div className="text-sm text-gray-500">
          Step {currentStep + 1} of {WIZARD_STEPS.length}
        </div>

        <Button
          onClick={currentStep === WIZARD_STEPS.length - 1 ? handleComplete : handleNext}
          disabled={
            (currentStep === 2 && !formData.name) ||
            (currentStep === 1 && !formData.templateId)
          }
        >
          {currentStep === WIZARD_STEPS.length - 1 ? 'Create Agent' : 'Next'}
          <ChevronRight className="w-4 h-4 ml-2" />
        </Button>
      </div>
    </div>
  );
}