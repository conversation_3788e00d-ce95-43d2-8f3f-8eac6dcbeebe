import { apiClient } from './api-client';

export interface User {
  id: string;
  email: string;
  name: string;
  systemRole: 'SUPER_ADMIN' | 'ORG_ADMIN' | 'DEVELOPER' | 'VIEWER';
  isActive: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
  organization: {
    id: string;
    name: string;
    slug: string;
  };
  userRoles: Array<{
    role: {
      id: string;
      name: string;
      description?: string;
    };
  }>;
  userPermissions: Array<{
    permission: {
      id: string;
      name: string;
      description?: string;
    };
  }>;
}

export interface Role {
  id: string;
  name: string;
  description?: string;
  isSystem: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  rolePermissions: Array<{
    permission: {
      id: string;
      name: string;
      description?: string;
      resource: string;
      action: string;
    };
  }>;
  userRoles: Array<{
    user: {
      id: string;
      name: string;
      email: string;
    };
  }>;
}

export interface Permission {
  id: string;
  name: string;
  description?: string;
  resource: string;
  action: string;
  isSystem: boolean;
  createdAt: string;
  updatedAt: string;
  rolePermissions: Array<{
    role: {
      id: string;
      name: string;
    };
  }>;
  userPermissions: Array<{
    user: {
      id: string;
      name: string;
      email: string;
    };
  }>;
}

export interface Organization {
  id: string;
  name: string;
  slug: string;
  domain?: string;
  status: 'ACTIVE' | 'SUSPENDED' | 'DELETED';
  isActive: boolean;
  settings: any;
  quotas: any;
  billing: any;
  branding: any;
  createdAt: string;
  updatedAt: string;
  users: Array<{
    id: string;
    name: string;
    email: string;
    systemRole: string;
    isActive: boolean;
  }>;
  _count: {
    users: number;
    agents: number;
    tools: number;
    workflows: number;
  };
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

class AdminApiClient {
  // User Management
  async getUsers(params?: {
    search?: string;
    role?: string;
    isActive?: boolean;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const response = await apiClient.get<{ users: User[]; pagination: any }>(`/api/users?${queryParams}`);
    return response;
  }

  async getUserById(id: string) {
    return apiClient.get<User>(`/api/users/${id}`);
  }

  async createUser(data: {
    email: string;
    name: string;
    password?: string;
    systemRole?: string;
    roleIds?: string[];
    permissionIds?: string[];
  }) {
    return apiClient.post<User>('/api/users', data);
  }

  async updateUser(id: string, data: {
    name?: string;
    email?: string;
    systemRole?: string;
    isActive?: boolean;
    roleIds?: string[];
    permissionIds?: string[];
  }) {
    return apiClient.put<User>(`/api/users/${id}`, data);
  }

  async deleteUser(id: string) {
    return apiClient.delete(`/api/users/${id}`);
  }

  async bulkUpdateUsers(userIds: string[], updateData: any) {
    return apiClient.put('/api/users/bulk-update', { userIds, updateData });
  }

  async resetUserPassword(id: string, newPassword: string) {
    return apiClient.put(`/api/users/${id}/reset-password`, { newPassword });
  }

  // Role Management
  async getRoles(params?: {
    search?: string;
    isActive?: boolean;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    return apiClient.get<{ roles: Role[]; pagination: any }>(`/api/roles?${queryParams}`);
  }

  async getRoleById(id: string) {
    return apiClient.get<Role>(`/api/roles/${id}`);
  }

  async createRole(data: {
    name: string;
    description?: string;
    permissionIds?: string[];
  }) {
    return apiClient.post<Role>('/api/roles', data);
  }

  async updateRole(id: string, data: {
    name?: string;
    description?: string;
    isActive?: boolean;
    permissionIds?: string[];
  }) {
    return apiClient.put<Role>(`/api/roles/${id}`, data);
  }

  async deleteRole(id: string) {
    return apiClient.delete(`/api/roles/${id}`);
  }

  async assignRoleToUser(roleId: string, userId: string) {
    return apiClient.post(`/api/roles/${roleId}/users/${userId}`, {});
  }

  async removeRoleFromUser(roleId: string, userId: string) {
    return apiClient.delete(`/api/roles/${roleId}/users/${userId}`);
  }

  // Permission Management
  async getPermissions(params?: {
    search?: string;
    resource?: string;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    return apiClient.get<{ permissions: Permission[]; pagination: any }>(`/api/permissions?${queryParams}`);
  }

  async getPermissionById(id: string) {
    return apiClient.get<Permission>(`/api/permissions/${id}`);
  }

  async createPermission(data: {
    name: string;
    description?: string;
    resource: string;
    action: string;
  }) {
    return apiClient.post<Permission>('/api/permissions', data);
  }

  async updatePermission(id: string, data: {
    name?: string;
    description?: string;
    resource?: string;
    action?: string;
  }) {
    return apiClient.put<Permission>(`/api/permissions/${id}`, data);
  }

  async deletePermission(id: string) {
    return apiClient.delete(`/api/permissions/${id}`);
  }

  async getResources() {
    return apiClient.get<string[]>('/api/permissions/resources');
  }

  async getActions(resource?: string) {
    const params = resource ? `?resource=${resource}` : '';
    return apiClient.get<string[]>(`/api/permissions/actions${params}`);
  }

  async assignPermissionToUser(permissionId: string, userId: string) {
    return apiClient.post(`/api/permissions/${permissionId}/users/${userId}`, {});
  }

  async removePermissionFromUser(permissionId: string, userId: string) {
    return apiClient.delete(`/api/permissions/${permissionId}/users/${userId}`);
  }

  // Organization Management
  async getOrganizations(params?: {
    search?: string;
    status?: string;
    isActive?: boolean;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    return apiClient.get<{ organizations: Organization[]; pagination: any }>(`/api/organizations?${queryParams}`);
  }

  async getCurrentOrganization() {
    return apiClient.get<Organization>('/api/organizations/current');
  }

  async getCurrentOrganizationStats() {
    return apiClient.get('/api/organizations/current/stats');
  }

  async getOrganizationById(id: string) {
    return apiClient.get<Organization>(`/api/organizations/${id}`);
  }

  async createOrganization(data: {
    name: string;
    slug: string;
    domain?: string;
    settings?: any;
    quotas?: any;
    billing?: any;
    branding?: any;
  }) {
    return apiClient.post<Organization>('/api/organizations', data);
  }

  async updateOrganization(id: string, data: {
    name?: string;
    slug?: string;
    domain?: string;
    settings?: any;
    quotas?: any;
    billing?: any;
    branding?: any;
    status?: string;
    isActive?: boolean;
  }) {
    return apiClient.put<Organization>(`/api/organizations/${id}`, data);
  }

  async deleteOrganization(id: string) {
    return apiClient.delete(`/api/organizations/${id}`);
  }

  async suspendOrganization(id: string, reason?: string) {
    return apiClient.put(`/api/organizations/${id}/suspend`, { reason });
  }

  async activateOrganization(id: string) {
    return apiClient.put(`/api/organizations/${id}/activate`, {});
  }
}

export const adminApiClient = new AdminApiClient();