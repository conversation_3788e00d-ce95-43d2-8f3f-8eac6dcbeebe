import { UserService, CreateUserDto, UpdateUserDto, UserFilters } from './users.service';
export declare class UserController {
    private readonly userService;
    constructor(userService: UserService);
    createUser(createUserDto: CreateUserDto, req: any): Promise<any>;
    getUsers(filters: UserFilters, req: any): Promise<{
        users: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            pages: number;
        };
    }>;
    getUserById(id: string): Promise<any>;
    updateUser(id: string, updateUserDto: UpdateUserDto, req: any): Promise<any>;
    deleteUser(id: string, req: any): Promise<{
        message: string;
    }>;
    bulkUpdateUsers(body: {
        userIds: string[];
        updateData: Partial<UpdateUserDto>;
    }, req: any): Promise<{
        message: string;
    }>;
    resetPassword(id: string, body: {
        newPassword: string;
    }, req: any): Promise<{
        message: string;
    }>;
}
