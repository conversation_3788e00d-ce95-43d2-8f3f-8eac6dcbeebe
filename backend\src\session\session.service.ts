import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import Redis from 'ioredis';
import { SessionStatus } from '@prisma/client';
import { ApixGateway } from '../websocket/apix.gateway';

export interface CreateSessionDto {
  userId: string;
  organizationId: string;
  metadata?: Record<string, any>;
}

export interface AddMessageDto {
  sessionId: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  metadata?: Record<string, any>;
}

export interface UpdateContextDto {
  sessionId: string;
  context: Record<string, any>;
}

@Injectable()
export class SessionService {
  private redis: Redis;

  constructor(
    private prisma: PrismaService,
    private apixGateway: ApixGateway,
  ) {
    this.redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT) || 6379,
      password: process.env.REDIS_PASSWORD,
    });
  }

  async createSession(createSessionDto: CreateSessionDto) {
    const { userId, organizationId, metadata = {} } = createSessionDto;

    // Create session in database
    const session = await this.prisma.session.create({
      data: {
        userId,
        organizationId,
        status: SessionStatus.ACTIVE,
        messages: [],
        context: {},
        metadata,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
      },
    });

    // Store session in Redis for fast access
    await this.redis.setex(
      `session:${session.id}`,
      24 * 60 * 60, // 24 hours
      JSON.stringify({
        id: session.id,
        userId: session.userId,
        organizationId: session.organizationId,
        status: session.status,
        messages: session.messages,
        context: session.context,
        metadata: session.metadata,
      })
    );

    // Publish session created event
    await this.apixGateway.publishSessionEvent(
      'created',
      { sessionId: session.id },
      organizationId,
      userId
    );

    return session;
  }

  async getSession(sessionId: string, organizationId: string) {
    // Try Redis first
    const cachedSession = await this.redis.get(`session:${sessionId}`);
    if (cachedSession) {
      const sessionData = JSON.parse(cachedSession);
      // Verify organization access
      if (sessionData.organizationId !== organizationId) {
        throw new NotFoundException('Session not found');
      }
      return sessionData;
    }

    // Fallback to database with organization filtering
    const session = await this.prisma.session.findUnique({
      where: { 
        id: sessionId,
        organizationId, // Tenant isolation
      },
    });

    if (!session) {
      throw new NotFoundException('Session not found');
    }

    // Cache in Redis
    await this.redis.setex(
      `session:${sessionId}`,
      24 * 60 * 60,
      JSON.stringify(session)
    );

    return session;
  }

  async addMessage(addMessageDto: AddMessageDto, organizationId: string) {
    const { sessionId, role, content, metadata = {} } = addMessageDto;

    const session = await this.getSession(sessionId, organizationId);

    if (session.status !== SessionStatus.ACTIVE) {
      throw new Error('Cannot add message to inactive session');
    }

    const message = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      role,
      content,
      timestamp: new Date(),
      metadata,
    };

    const messages = Array.isArray(session.messages) ? session.messages : [];
    messages.push(message);

    // Apply memory limits
    const limitedMessages = await this.applyMemoryLimits(messages, session.metadata);

    // Update in database
    await this.prisma.session.update({
      where: { 
        id: sessionId,
        organizationId, // Tenant isolation
      },
      data: {
        messages: limitedMessages,
        updatedAt: new Date(),
      },
    });

    // Update in Redis
    const updatedSession = { ...session, messages: limitedMessages };
    await this.redis.setex(
      `session:${sessionId}`,
      24 * 60 * 60,
      JSON.stringify(updatedSession)
    );

    // Publish message added event
    await this.apixGateway.publishSessionEvent(
      'message_added',
      { sessionId, message },
      organizationId,
      session.userId
    );

    return updatedSession;
  }

  async updateContext(updateContextDto: UpdateContextDto, organizationId: string) {
    const { sessionId, context } = updateContextDto;

    const session = await this.getSession(sessionId, organizationId);

    // Merge context
    const mergedContext = { ...session.context, ...context };

    // Update in database
    await this.prisma.session.update({
      where: { 
        id: sessionId,
        organizationId, // Tenant isolation
      },
      data: {
        context: mergedContext,
        updatedAt: new Date(),
      },
    });

    // Update in Redis
    const updatedSession = { ...session, context: mergedContext };
    await this.redis.setex(
      `session:${sessionId}`,
      24 * 60 * 60,
      JSON.stringify(updatedSession)
    );

    // Publish context updated event
    await this.apixGateway.publishSessionEvent(
      'context_updated',
      { sessionId, context },
      organizationId,
      session.userId
    );

    return updatedSession;
  }

  async getUserSessions(userId: string, organizationId: string, limit: number = 20) {
    return this.prisma.session.findMany({
      where: { 
        userId, 
        organizationId, // Tenant isolation
      },
      orderBy: { updatedAt: 'desc' },
      take: limit,
    });
  }

  async completeSession(sessionId: string, organizationId: string) {
    const session = await this.getSession(sessionId, organizationId);

    // Update in database
    await this.prisma.session.update({
      where: { 
        id: sessionId,
        organizationId, // Tenant isolation
      },
      data: {
        status: SessionStatus.COMPLETED,
        updatedAt: new Date(),
      },
    });

    // Remove from Redis
    await this.redis.del(`session:${sessionId}`);

    // Publish session completed event
    await this.apixGateway.publishSessionEvent(
      'completed',
      { sessionId },
      organizationId,
      session.userId
    );

    return { ...session, status: SessionStatus.COMPLETED };
  }

  // Cross-module session sharing
  async shareSessionContext(sessionId: string, organizationId: string, targetModule: string) {
    const session = await this.getSession(sessionId, organizationId);
    
    const sharedContext = {
      sessionId: session.id,
      userId: session.userId,
      organizationId: session.organizationId,
      context: session.context,
      recentMessages: Array.isArray(session.messages) 
        ? session.messages.slice(-10) 
        : [], // Last 10 messages
      metadata: session.metadata,
    };

    // Store shared context in Redis with module-specific key
    await this.redis.setex(
      `shared_context:${targetModule}:${sessionId}`,
      60 * 60, // 1 hour
      JSON.stringify(sharedContext)
    );

    return sharedContext;
  }

  async getSharedContext(sessionId: string, module: string, organizationId: string) {
    const sharedContext = await this.redis.get(`shared_context:${module}:${sessionId}`);
    if (!sharedContext) return null;

    const context = JSON.parse(sharedContext);
    
    // Verify organization access
    if (context.organizationId !== organizationId) {
      return null;
    }

    return context;
  }

  // Memory management
  private async applyMemoryLimits(messages: any[], metadata: any = {}) {
    const maxMessages = metadata.maxMessages || 100;
    
    if (messages.length > maxMessages) {
      // Keep system messages and recent messages
      const systemMessages = messages.filter(msg => msg.role === 'system');
      const recentMessages = messages
        .filter(msg => msg.role !== 'system')
        .slice(-(maxMessages - systemMessages.length));
      
      return [...systemMessages, ...recentMessages];
    }

    return messages;
  }

  // Cleanup expired sessions
  async cleanupExpiredSessions() {
    const expiredSessions = await this.prisma.session.findMany({
      where: {
        expiresAt: {
          lt: new Date(),
        },
        status: SessionStatus.ACTIVE,
      },
    });

    for (const session of expiredSessions) {
      await this.prisma.session.update({
        where: { id: session.id },
        data: { status: SessionStatus.TIMEOUT },
      });

      await this.redis.del(`session:${session.id}`);

      await this.apixGateway.publishSessionEvent(
        'expired',
        { sessionId: session.id },
        session.organizationId,
        session.userId
      );
    }
  }
}