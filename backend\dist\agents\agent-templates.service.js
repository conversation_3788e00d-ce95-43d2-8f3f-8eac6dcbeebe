"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AgentTemplatesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentTemplatesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const agent_entity_1 = require("../database/entities/agent.entity");
let AgentTemplatesService = AgentTemplatesService_1 = class AgentTemplatesService {
    constructor(templateRepository) {
        this.templateRepository = templateRepository;
        this.logger = new common_1.Logger(AgentTemplatesService_1.name);
    }
    async createTemplate(createTemplateDto, organizationId, userId) {
        try {
            const template = this.templateRepository.create(Object.assign(Object.assign({}, createTemplateDto), { organizationId, createdBy: userId }));
            const savedTemplate = await this.templateRepository.save(template);
            this.logger.log(`Agent template created: ${savedTemplate.id}`);
            return savedTemplate;
        }
        catch (error) {
            this.logger.error(`Failed to create agent template: ${error.message}`);
            throw error;
        }
    }
    async getTemplatesByOrganization(organizationId) {
        return this.templateRepository.find({
            where: [
                { organizationId },
                { isPublic: true },
            ],
            order: { createdAt: 'DESC' },
        });
    }
    async getTemplateById(templateId, organizationId) {
        const template = await this.templateRepository.findOne({
            where: [
                { id: templateId, organizationId },
                { id: templateId, isPublic: true },
            ],
        });
        if (!template) {
            throw new Error('Template not found');
        }
        return template;
    }
    async updateTemplate(templateId, updateData, organizationId) {
        const template = await this.templateRepository.findOne({
            where: { id: templateId, organizationId },
        });
        if (!template) {
            throw new Error('Template not found');
        }
        Object.assign(template, updateData);
        return this.templateRepository.save(template);
    }
    async deleteTemplate(templateId, organizationId) {
        const result = await this.templateRepository.delete({
            id: templateId,
            organizationId,
        });
        if (result.affected === 0) {
            throw new Error('Template not found');
        }
    }
    async getPublicTemplates() {
        return this.templateRepository.find({
            where: { isPublic: true },
            order: { createdAt: 'DESC' },
        });
    }
    async getTemplatesByCategory(category, organizationId) {
        return this.templateRepository.find({
            where: [
                { category, organizationId },
                { category, isPublic: true },
            ],
            order: { createdAt: 'DESC' },
        });
    }
    async seedDefaultTemplates() {
        const defaultTemplates = [
            {
                name: 'Customer Support Agent',
                category: 'Customer Service',
                description: 'Handles customer inquiries with empathy and efficiency',
                config: {
                    personality: 'helpful',
                    responseStyle: 'professional',
                    maxResponseLength: 500,
                },
                skills: ['customer_support', 'product_knowledge', 'escalation'],
                isPublic: true,
                promptTemplate: `You are a helpful customer support agent. Always be polite, empathetic, and solution-focused. 
        If you cannot resolve an issue, escalate it appropriately.`,
                type: 'BASIC',
                supportedProviders: ['OPENAI', 'CLAUDE'],
                createdBy: 'system',
                organizationId: 'system',
            },
            {
                name: 'Sales Assistant',
                category: 'Sales',
                description: 'Helps qualify leads and provide product information',
                config: {
                    personality: 'persuasive',
                    responseStyle: 'engaging',
                    maxResponseLength: 300,
                },
                skills: ['lead_qualification', 'product_demo', 'objection_handling'],
                isPublic: true,
                promptTemplate: `You are a knowledgeable sales assistant. Help qualify leads, provide product information, 
        and guide prospects through the sales process.`,
                type: 'TOOL_DRIVEN',
                supportedProviders: ['OPENAI', 'CLAUDE', 'GEMINI'],
                createdBy: 'system',
                organizationId: 'system',
            },
            {
                name: 'Content Creator',
                category: 'Marketing',
                description: 'Creates engaging content for various platforms',
                config: {
                    personality: 'creative',
                    responseStyle: 'engaging',
                    maxResponseLength: 1000,
                },
                skills: ['content_writing', 'seo_optimization', 'social_media'],
                isPublic: true,
                promptTemplate: `You are a creative content creator. Generate engaging, original content 
        optimized for the target platform and audience.`,
                type: 'HYBRID',
                supportedProviders: ['OPENAI', 'CLAUDE', 'GEMINI'],
                createdBy: 'system',
                organizationId: 'system',
            },
            {
                name: 'Data Analyst',
                category: 'Analytics',
                description: 'Analyzes data and provides insights',
                config: {
                    personality: 'analytical',
                    responseStyle: 'detailed',
                    maxResponseLength: 800,
                },
                skills: ['data_analysis', 'visualization', 'reporting'],
                isPublic: true,
                promptTemplate: `You are a data analyst. Analyze data thoroughly, identify patterns and trends, 
        and provide actionable insights with clear explanations.`,
                type: 'TOOL_DRIVEN',
                supportedProviders: ['OPENAI', 'CLAUDE'],
                createdBy: 'system',
                organizationId: 'system',
            },
            {
                name: 'Project Manager',
                category: 'Management',
                description: 'Helps manage projects and coordinate teams',
                config: {
                    personality: 'organized',
                    responseStyle: 'structured',
                    maxResponseLength: 600,
                },
                skills: ['project_planning', 'team_coordination', 'risk_management'],
                isPublic: true,
                promptTemplate: `You are an experienced project manager. Help plan projects, coordinate teams, 
        manage timelines, and identify potential risks.`,
                type: 'MULTI_TASK',
                supportedProviders: ['OPENAI', 'CLAUDE', 'GEMINI'],
                createdBy: 'system',
                organizationId: 'system',
            },
        ];
        for (const template of defaultTemplates) {
            const exists = await this.templateRepository.findOne({
                where: { name: template.name, organizationId: 'system' },
            });
            if (!exists) {
                await this.templateRepository.save(template);
                this.logger.log(`Seeded template: ${template.name}`);
            }
        }
    }
};
exports.AgentTemplatesService = AgentTemplatesService;
exports.AgentTemplatesService = AgentTemplatesService = AgentTemplatesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(agent_entity_1.AgentTemplate)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], AgentTemplatesService);
//# sourceMappingURL=agent-templates.service.js.map