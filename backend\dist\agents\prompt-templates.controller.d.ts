import { HttpStatus } from '@nestjs/common';
import { PromptTemplatesService } from './prompt-templates.service';
import { CreatePromptTemplateDto, UpdatePromptTemplateDto, CreatePromptReviewDto } from './dto/agent.dto';
export declare class PromptTemplatesController {
    private readonly promptTemplatesService;
    constructor(promptTemplatesService: PromptTemplatesService);
    create(req: any, createDto: CreatePromptTemplateDto): Promise<{
        success: boolean;
        data: any;
        message: string;
        statusCode: HttpStatus;
    } | {
        statusCode: HttpStatus;
        success: boolean;
        message: any;
    }>;
    findAll(req: any, category?: string, isPublic?: string, search?: string, agentTemplateId?: string, page?: string, limit?: string, sortBy?: 'name' | 'usage' | 'rating' | 'createdAt', sortOrder?: 'asc' | 'desc'): Promise<{
        success: boolean;
        data: {
            templates: any;
            pagination: {
                page: number;
                limit: number;
                total: any;
                pages: number;
            };
        };
        statusCode: HttpStatus;
        message?: undefined;
    } | {
        statusCode: HttpStatus;
        success: boolean;
        message: any;
    }>;
    getCategories(req: any): Promise<{
        success: boolean;
        data: any;
        statusCode: HttpStatus;
        message?: undefined;
    } | {
        statusCode: HttpStatus;
        success: boolean;
        message: any;
    }>;
    findOne(req: any, id: string): Promise<{
        success: boolean;
        data: any;
        statusCode: HttpStatus;
        message?: undefined;
    } | {
        statusCode: HttpStatus;
        success: boolean;
        message: any;
    }>;
    update(req: any, id: string, updateDto: UpdatePromptTemplateDto): Promise<{
        success: boolean;
        data: any;
        message: string;
        statusCode: HttpStatus;
    } | {
        statusCode: HttpStatus;
        success: boolean;
        message: any;
    }>;
    delete(req: any, id: string): Promise<{
        statusCode: HttpStatus;
        success: boolean;
        message: any;
    }>;
    duplicate(req: any, id: string, name?: string): Promise<{
        success: boolean;
        data: any;
        message: string;
        statusCode: HttpStatus;
    } | {
        statusCode: HttpStatus;
        success: boolean;
        message: any;
    }>;
    addReview(req: any, id: string, reviewDto: CreatePromptReviewDto): Promise<{
        success: boolean;
        data: any;
        message: string;
        statusCode: HttpStatus;
    } | {
        statusCode: HttpStatus;
        success: boolean;
        message: any;
    }>;
    optimizePrompt(req: any, id: string): Promise<{
        success: boolean;
        data: any;
        message: string;
        statusCode: HttpStatus;
    } | {
        statusCode: HttpStatus;
        success: boolean;
        message: any;
    }>;
    validateVariables(req: any, id: string, body: {
        content: string;
        variables: any[];
    }): Promise<{
        success: boolean;
        data: {
            isValid: boolean;
            usedVariables: string[];
            definedVariables: any[];
            missingVariables: string[];
            unusedVariables: any[];
        };
        statusCode: HttpStatus;
        message?: undefined;
    } | {
        statusCode: HttpStatus;
        success: boolean;
        message: any;
    }>;
}
