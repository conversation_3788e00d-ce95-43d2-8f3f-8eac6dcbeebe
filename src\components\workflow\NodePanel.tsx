"use client";

import React, { useState } from "react";
import { Search, ChevronDown, ChevronRight } from "lucide-react";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Card } from "@/components/ui/card";

interface NodeType {
  id: string;
  type: string;
  category: string;
  label: string;
  description: string;
  icon: React.ReactNode;
}

interface NodePanelProps {
  onDragStart?: (event: React.DragEvent, nodeType: NodeType) => void;
}

const NodePanel = ({ onDragStart = () => {} }: NodePanelProps) => {
  const [searchQuery, setSearchQuery] = useState("");

  // Default node types organized by category
  const nodeTypes: NodeType[] = [
    // AI Providers
    {
      id: "openai",
      type: "aiProvider",
      category: "AI Providers",
      label: "OpenAI",
      description: "Connect to OpenAI models like GPT-4 and GPT-3.5",
      icon: (
        <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center text-white font-bold">
          AI
        </div>
      ),
    },
    {
      id: "claude",
      type: "aiProvider",
      category: "AI Providers",
      label: "Claude",
      description: "Connect to Anthropic's Claude models",
      icon: (
        <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center text-white font-bold">
          C
        </div>
      ),
    },
    {
      id: "gemini",
      type: "aiProvider",
      category: "AI Providers",
      label: "Gemini",
      description: "Connect to Google's Gemini models",
      icon: (
        <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center text-white font-bold">
          G
        </div>
      ),
    },

    // Tools
    {
      id: "text-summarizer",
      type: "tool",
      category: "Tools",
      label: "Text Summarizer",
      description: "Summarize long text content",
      icon: (
        <div className="w-8 h-8 bg-amber-500 rounded-md flex items-center justify-center text-white font-bold">
          T
        </div>
      ),
    },
    {
      id: "data-extractor",
      type: "tool",
      category: "Tools",
      label: "Data Extractor",
      description: "Extract structured data from text",
      icon: (
        <div className="w-8 h-8 bg-amber-500 rounded-md flex items-center justify-center text-white font-bold">
          D
        </div>
      ),
    },
    {
      id: "api-connector",
      type: "tool",
      category: "Tools",
      label: "API Connector",
      description: "Connect to external APIs",
      icon: (
        <div className="w-8 h-8 bg-amber-500 rounded-md flex items-center justify-center text-white font-bold">
          A
        </div>
      ),
    },

    // Agents
    {
      id: "customer-support",
      type: "agent",
      category: "Agents",
      label: "Customer Support",
      description: "Agent specialized in customer support interactions",
      icon: (
        <div className="w-8 h-8 bg-indigo-500 rounded-md flex items-center justify-center text-white font-bold">
          CS
        </div>
      ),
    },
    {
      id: "data-analyst",
      type: "agent",
      category: "Agents",
      label: "Data Analyst",
      description: "Agent specialized in data analysis",
      icon: (
        <div className="w-8 h-8 bg-indigo-500 rounded-md flex items-center justify-center text-white font-bold">
          DA
        </div>
      ),
    },

    // Logic
    {
      id: "conditional",
      type: "logic",
      category: "Logic",
      label: "Conditional",
      description: "Branch workflow based on conditions",
      icon: (
        <div className="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center text-white font-bold">
          IF
        </div>
      ),
    },
    {
      id: "loop",
      type: "logic",
      category: "Logic",
      label: "Loop",
      description: "Repeat actions for a collection of items",
      icon: (
        <div className="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center text-white font-bold">
          ⟳
        </div>
      ),
    },
  ];

  // Filter nodes based on search query
  const filteredNodes = searchQuery
    ? nodeTypes.filter(
        (node) =>
          node.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
          node.description.toLowerCase().includes(searchQuery.toLowerCase()),
      )
    : nodeTypes;

  // Group nodes by category
  const nodesByCategory = filteredNodes.reduce<Record<string, NodeType[]>>(
    (acc, node) => {
      if (!acc[node.category]) {
        acc[node.category] = [];
      }
      acc[node.category].push(node);
      return acc;
    },
    {},
  );

  return (
    <div className="w-full h-full bg-background border-r flex flex-col">
      <div className="p-4 border-b">
        <h3 className="text-lg font-semibold mb-2">Nodes</h3>
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search nodes..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-8"
          />
        </div>
      </div>

      <ScrollArea className="flex-1">
        <div className="p-4">
          <Accordion
            type="multiple"
            defaultValue={Object.keys(nodesByCategory)}
          >
            {Object.entries(nodesByCategory).map(([category, nodes]) => (
              <AccordionItem key={category} value={category}>
                <AccordionTrigger className="py-2">
                  <span className="text-sm font-medium">{category}</span>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="grid gap-2">
                    {nodes.map((node) => (
                      <Card
                        key={node.id}
                        className="p-3 cursor-grab hover:bg-accent transition-colors"
                        draggable
                        onDragStart={(e) => onDragStart(e, node)}
                      >
                        <div className="flex items-center gap-3">
                          {node.icon}
                          <div>
                            <h4 className="text-sm font-medium">
                              {node.label}
                            </h4>
                            <p className="text-xs text-muted-foreground">
                              {node.description}
                            </p>
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </ScrollArea>
    </div>
  );
};

export default NodePanel;
