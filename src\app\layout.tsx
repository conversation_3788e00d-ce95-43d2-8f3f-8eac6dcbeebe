import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/contexts/AuthContext";
import { ThemeProvider } from "@/contexts/ThemeContext";
import { Toaster } from "sonner";
import Script from "next/script";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "SynapseAI - Universal AI Orchestration Platform",
  description: "Production-grade multi-tenant AI orchestration system with real-time collaboration, RBAC, and enterprise security. Build intelligent agents, tools, and workflows without code.",
  keywords: "AI, automation, orchestration, agents, tools, workflows, enterprise, SaaS, no-code, artificial intelligence",
  authors: [{ name: "SynapseAI Team" }],
  viewport: "width=device-width, initial-scale=1",
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "#ffffff" },
    { media: "(prefers-color-scheme: dark)", color: "#0f172a" },
  ],
  openGraph: {
    title: "SynapseAI - Universal AI Orchestration Platform",
    description: "Build intelligent AI workflows without code. Enterprise-grade security, real-time collaboration, and multi-tenant architecture.",
    type: "website",
    url: "https://synapseai.com",
    images: [
      {
        url: "https://synapseai.com/og-image.png",
        width: 1200,
        height: 630,
        alt: "SynapseAI Platform",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "SynapseAI - Universal AI Orchestration Platform",
    description: "Build intelligent AI workflows without code. Enterprise-grade security and real-time collaboration.",
    images: ["https://synapseai.com/og-image.png"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta name="theme-color" content="#ffffff" />
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
      </head>
      <body className={inter.className}>
        <Script 
          src="https://api.tempo.build/proxy-asset?url=https://storage.googleapis.com/tempo-public-assets/error-handling.js" 
          strategy="afterInteractive"
        />
        
        <ThemeProvider>
          <AuthProvider>
            {children}
            <Toaster 
              position="top-right"
              expand={true}
              richColors
              closeButton
              toastOptions={{
                style: {
                  background: 'rgba(255, 255, 255, 0.95)',
                  backdropFilter: 'blur(12px)',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
                },
                className: 'dark:!bg-slate-800/95 dark:!border-slate-700/50 dark:!text-white',
                duration: 4000,
              }}
            />
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}