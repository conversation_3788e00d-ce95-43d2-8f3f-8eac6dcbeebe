"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIProviderModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const ai_provider_entity_1 = require("../database/entities/ai-provider.entity");
const ai_provider_controller_1 = require("./ai-provider.controller");
const ai_provider_manager_service_1 = require("./ai-provider-manager.service");
const ai_provider_selector_service_1 = require("./ai-provider-selector.service");
const ai_provider_integration_service_1 = require("./ai-provider-integration.service");
const apix_module_1 = require("../websocket/apix.module");
let AIProviderModule = class AIProviderModule {
};
exports.AIProviderModule = AIProviderModule;
exports.AIProviderModule = AIProviderModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                ai_provider_entity_1.AIProvider,
                ai_provider_entity_1.AIModel,
                ai_provider_entity_1.ProviderUsage,
                ai_provider_entity_1.ProviderHealthCheck,
            ]),
            apix_module_1.ApixModule,
        ],
        controllers: [ai_provider_controller_1.AIProviderController],
        providers: [
            ai_provider_manager_service_1.AIProviderManagerService,
            ai_provider_selector_service_1.AIProviderSelectorService,
            ai_provider_integration_service_1.AIProviderIntegrationService,
        ],
        exports: [
            ai_provider_manager_service_1.AIProviderManagerService,
            ai_provider_selector_service_1.AIProviderSelectorService,
            ai_provider_integration_service_1.AIProviderIntegrationService,
        ],
    })
], AIProviderModule);
//# sourceMappingURL=ai-provider.module.js.map