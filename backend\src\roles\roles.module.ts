import { Module } from '@nestjs/common';
import { RoleService } from './roles.service';
import { RoleController } from './roles.controller';
import { PrismaModule } from '../prisma/prisma.module';
import { ApixModule } from '../websocket/apix.module';

@Module({
  imports: [PrismaModule, ApixModule],
  providers: [RoleService],
  controllers: [RoleController],
  exports: [RoleService],
})
export class RoleModule {}