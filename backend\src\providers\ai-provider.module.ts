import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AIProvider, AIModel, ProviderUsage, ProviderHealthCheck } from '../database/entities/ai-provider.entity';
import { AIProviderController } from './ai-provider.controller';
import { AIProviderManagerService } from './ai-provider-manager.service';
import { AIProviderSelectorService } from './ai-provider-selector.service';
import { AIProviderIntegrationService } from './ai-provider-integration.service';
import { ApixModule } from '../websocket/apix.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AIProvider,
      AIModel,
      ProviderUsage,
      ProviderHealthCheck,
    ]),
    ApixModule,
  ],
  controllers: [AIProviderController],
  providers: [
    AIProviderManagerService,
    AIProviderSelectorService,
    AIProviderIntegrationService,
  ],
  exports: [
    AIProviderManagerService,
    AIProviderSelectorService,
    AIProviderIntegrationService,
  ],
})
export class AIProviderModule {}