'use client';

import { useState, useEffect } from 'react';
import { io, Socket } from 'socket.io-client';
import { useAuth } from '@/contexts/AuthContext';

interface ApixEvent {
  type: string;
  payload: any;
  timestamp: string;
  organizationId?: string;
  userId?: string;
}

interface ApixContextType {
  socket: Socket | null;
  connected: boolean;
  on: (event: string, callback: (data: ApixEvent) => void) => void;
  off: (event: string) => void;
  emit: (event: string, data: any) => void;
  publishToOrganization: (organizationId: string, event: ApixEvent) => void;
}

let socket: Socket | null = null;
const eventCallbacks = new Map<string, ((data: ApixEvent) => void)[]>();

export function useApix(): ApixContextType {
  const { user, organization } = useAuth();
  const [connected, setConnected] = useState(false);

  useEffect(() => {
    if (user && organization && !socket) {
      initializeSocket();
    }

    return () => {
      if (socket) {
        socket.disconnect();
        socket = null;
        setConnected(false);
      }
    };
  }, [user, organization]);

  const initializeSocket = () => {
    const token = localStorage.getItem('accessToken');
    if (!token) return;

    socket = io(process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3001', {
      auth: {
        token,
        organizationId: organization?.id,
      },
      transports: ['websocket'],
    });

    socket.on('connect', () => {
      setConnected(true);
      console.log('APIX connected');
    });

    socket.on('disconnect', () => {
      setConnected(false);
      console.log('APIX disconnected');
    });

    socket.on('error', (error) => {
      console.error('APIX error:', error);
    });

    // Handle incoming events
    socket.onAny((eventType: string, data: ApixEvent) => {
      const callbacks = eventCallbacks.get(eventType);
      if (callbacks) {
        callbacks.forEach(callback => callback(data));
      }
    });
  };

  const on = (event: string, callback: (data: ApixEvent) => void) => {
    if (!eventCallbacks.has(event)) {
      eventCallbacks.set(event, []);
    }
    eventCallbacks.get(event)!.push(callback);
  };

  const off = (event: string) => {
    eventCallbacks.delete(event);
  };

  const emit = (event: string, data: any) => {
    if (socket && connected) {
      socket.emit(event, data);
    }
  };

  const publishToOrganization = (organizationId: string, event: ApixEvent) => {
    if (socket && connected) {
      socket.emit('organization_event', {
        organizationId,
        event,
      });
    }
  };

  return {
    socket,
    connected,
    on,
    off,
    emit,
    publishToOrganization,
  };
}