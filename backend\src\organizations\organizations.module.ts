import { Module } from '@nestjs/common';
import { OrganizationService } from './organizations.service';
import { OrganizationController } from './organizations.controller';
import { PrismaModule } from '../prisma/prisma.module';
import { ApixModule } from '../websocket/apix.module';

@Module({
  imports: [PrismaModule, ApixModule],
  providers: [OrganizationService],
  controllers: [OrganizationController],
  exports: [OrganizationService],
})
export class OrganizationModule {}