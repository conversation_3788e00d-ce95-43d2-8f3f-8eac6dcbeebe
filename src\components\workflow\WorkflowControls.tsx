"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Save,
  Play,
  Undo2,
  Redo2,
  ZoomIn,
  ZoomOut,
  Grid,
  CheckCircle2,
  AlertCircle,
  Download,
  Upload,
  Share2,
} from "lucide-react";

interface WorkflowControlsProps {
  workflowName?: string;
  onWorkflowNameChange?: (name: string) => void;
  onSave?: () => void;
  onRun?: () => void;
  onUndo?: () => void;
  onRedo?: () => void;
  onZoomIn?: () => void;
  onZoomOut?: () => void;
  onToggleGrid?: () => void;
  isGridEnabled?: boolean;
  isValid?: boolean;
  isRunning?: boolean;
  canUndo?: boolean;
  canRedo?: boolean;
}

const WorkflowControls = ({
  workflowName = "Untitled Workflow",
  onWorkflowNameChange = () => {},
  onSave = () => {},
  onRun = () => {},
  onUndo = () => {},
  onRedo = () => {},
  onZoomIn = () => {},
  onZoomOut = () => {},
  onToggleGrid = () => {},
  isGridEnabled = true,
  isValid = true,
  isRunning = false,
  canUndo = false,
  canRedo = false,
}: WorkflowControlsProps) => {
  const [localWorkflowName, setLocalWorkflowName] = useState(workflowName);

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLocalWorkflowName(e.target.value);
    onWorkflowNameChange(e.target.value);
  };

  return (
    <div className="flex items-center justify-between w-full h-16 px-4 py-2 bg-background border-b">
      <div className="flex items-center space-x-4">
        <Input
          className="w-64 h-9"
          value={localWorkflowName}
          onChange={handleNameChange}
          placeholder="Workflow name"
        />

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="outline"
                size="icon"
                onClick={onSave}
                className="h-9 w-9"
              >
                <Save className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Save workflow</TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant={isRunning ? "secondary" : "default"}
                size="sm"
                onClick={onRun}
                disabled={!isValid}
                className="h-9 px-3 flex items-center space-x-1"
              >
                <Play className="h-4 w-4 mr-1" />
                {isRunning ? "Running..." : "Run"}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              {isValid
                ? "Run workflow"
                : "Fix validation errors before running"}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <Separator orientation="vertical" className="h-8" />

        <div className="flex items-center space-x-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={onUndo}
                  disabled={!canUndo}
                  className="h-8 w-8"
                >
                  <Undo2 className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Undo</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={onRedo}
                  disabled={!canRedo}
                  className="h-8 w-8"
                >
                  <Redo2 className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Redo</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      <div className="flex items-center space-x-4">
        {isValid ? (
          <Badge
            variant="outline"
            className="flex items-center gap-1 bg-green-50 text-green-700 border-green-200"
          >
            <CheckCircle2 className="h-3 w-3" />
            Valid
          </Badge>
        ) : (
          <Badge
            variant="outline"
            className="flex items-center gap-1 bg-red-50 text-red-700 border-red-200"
          >
            <AlertCircle className="h-3 w-3" />
            Invalid
          </Badge>
        )}

        <Separator orientation="vertical" className="h-8" />

        <div className="flex items-center space-x-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={onZoomOut}
                  className="h-8 w-8"
                >
                  <ZoomOut className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Zoom out</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={onZoomIn}
                  className="h-8 w-8"
                >
                  <ZoomIn className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Zoom in</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        <div className="flex items-center space-x-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="grid-toggle"
                    checked={isGridEnabled}
                    onCheckedChange={onToggleGrid}
                  />
                  <Label
                    htmlFor="grid-toggle"
                    className="cursor-pointer flex items-center"
                  >
                    <Grid className="h-4 w-4 mr-1" />
                    Grid
                  </Label>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                {isGridEnabled ? "Disable grid" : "Enable grid"}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        <Separator orientation="vertical" className="h-8" />

        <div className="flex items-center space-x-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="icon" className="h-8 w-8">
                  <Download className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Export workflow</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="icon" className="h-8 w-8">
                  <Upload className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Import workflow</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="icon" className="h-8 w-8">
                  <Share2 className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Share workflow</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>
    </div>
  );
};

export default WorkflowControls;
