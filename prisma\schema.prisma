generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Organization {
  id        String   @id @default(cuid())
  name      String
  slug      String   @unique
  domain    String?
  settings  J<PERSON>     @default("{}")
  quotas    <PERSON><PERSON>     @default("{}")
  billing   <PERSON><PERSON>     @default("{}")
  branding  J<PERSON>     @default("{}")
  status    OrganizationStatus @default(ACTIVE)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  users            User[]
  agents           Agent[]
  tools            Tool[]
  sessions         Session[]
  apiKeys          ApiKey[]
  workflows        Workflow[]
  knowledgeBase    KnowledgeBase[]
  widgets          Widget[]
  notifications    Notification[]
  billingRecords   BillingRecord[]
  auditLogs        AuditLog[]
  roles            Role[]
  permissions      Permission[]
  userRoles        UserRole[]
  rolePermissions  RolePermission[]
  userPermissions  UserPermission[]
  
  // New Agent Builder relations
  agentTemplates      AgentTemplate[]
  promptTemplates     PromptTemplate[]
  agentCollaborations AgentCollaboration[]
  templateReviews     TemplateReview[]
  promptReviews       PromptReview[]
  templateAnalytics   TemplateAnalytics[]
  promptAnalytics     PromptAnalytics[]
  promptOptimizations PromptOptimization[]

  @@map("organizations")
}

enum OrganizationStatus {
  ACTIVE
  SUSPENDED
  DELETED
}

enum SystemUserRole {
  SUPER_ADMIN
  ORG_ADMIN
  DEVELOPER
  VIEWER
}

model User {
  id           String    @id @default(cuid())
  email        String    @unique
  name         String
  avatar       String?
  passwordHash String?
  systemRole   SystemUserRole  @default(VIEWER)
  permissions  String[]  @default([])
  preferences  Json      @default("{}")
  isActive     Boolean   @default(true)
  lastLoginAt  DateTime?
  emailVerified Boolean  @default(false)
  resetToken   String?
  resetTokenExpiry DateTime?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  // Organization relation
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String

  // Relations
  sessions         Session[]
  createdAgents    Agent[]
  createdTools     Tool[]
  createdWorkflows Workflow[]
  auditLogs        AuditLog[]
  notifications    Notification[]
  userRoles        UserRole[]
  userPermissions  UserPermission[]
  socialLogins     SocialLogin[]
  
  // New Agent Builder relations
  createdAgentTemplates AgentTemplate[]
  createdPromptTemplates PromptTemplate[]
  createdCollaborations AgentCollaboration[]
  templateReviews      TemplateReview[]
  promptReviews        PromptReview[]

  @@map("users")
}

model Role {
  id          String   @id @default(cuid())
  name        String
  description String?
  isSystem    Boolean  @default(false)
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Organization relation
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String

  // Relations
  userRoles       UserRole[]
  rolePermissions RolePermission[]

  @@unique([name, organizationId])
  @@map("roles")
}

model Permission {
  id          String   @id @default(cuid())
  name        String
  description String?
  resource    String
  action      String
  isSystem    Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Organization relation
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String

  // Relations
  rolePermissions RolePermission[]
  userPermissions UserPermission[]

  @@unique([name, organizationId])
  @@map("permissions")
}

model UserRole {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())

  // Relations
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId         String
  role           Role         @relation(fields: [roleId], references: [id], onDelete: Cascade)
  roleId         String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String

  @@unique([userId, roleId])
  @@map("user_roles")
}

model RolePermission {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())

  // Relations
  role           Role         @relation(fields: [roleId], references: [id], onDelete: Cascade)
  roleId         String
  permission     Permission   @relation(fields: [permissionId], references: [id], onDelete: Cascade)
  permissionId   String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String

  @@unique([roleId, permissionId])
  @@map("role_permissions")
}

model UserPermission {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())

  // Relations
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId         String
  permission     Permission   @relation(fields: [permissionId], references: [id], onDelete: Cascade)
  permissionId   String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String

  @@unique([userId, permissionId])
  @@map("user_permissions")
}

model SocialLogin {
  id         String   @id @default(cuid())
  provider   String
  providerId String
  email      String?
  name       String?
  avatar     String?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // Relations
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId String

  @@unique([provider, providerId])
  @@map("social_logins")
}

model ApiKey {
  id          String   @id @default(cuid())
  name        String
  key         String   @unique
  permissions String[] @default([])
  isActive    Boolean  @default(true)
  lastUsedAt  DateTime?
  expiresAt   DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Organization relation
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String

  @@map("api_keys")
}

enum SessionStatus {
  ACTIVE
  COMPLETED
  FAILED
  TIMEOUT
}

model Session {
  id        String        @id @default(cuid())
  status    SessionStatus @default(ACTIVE)
  messages  Json          @default("[]")
  context   Json          @default("{}")
  metadata  Json          @default("{}")
  expiresAt DateTime?
  createdAt DateTime      @default(now())
  updatedAt DateTime      @updatedAt

  // Relations
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId         String

  // Execution relations
  agentExecutions        AgentExecution[]
  toolExecutions         ToolExecution[]
  collaborationExecutions CollaborationExecution[]

  @@map("sessions")
}

enum AgentStatus {
  DRAFT
  ACTIVE
  PAUSED
  ARCHIVED
}

model Agent {
  id          String      @id @default(cuid())
  name        String
  description String?
  config      Json
  status      AgentStatus @default(DRAFT)
  metrics     Json        @default("{}")
  version     Int         @default(1)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String
  createdBy      User         @relation(fields: [createdById], references: [id])
  createdById    String

  executions AgentExecution[]
  templates  AgentTemplate[]
  collaborations AgentCollaboration[]

  @@map("agents")
}

model AgentTemplate {
  id          String   @id @default(cuid())
  name        String
  category    String
  description String
  config      Json
  skills      String[]  @default([])
  isPublic    Boolean   @default(false)
  usage       Int       @default(0)
  rating      Float?
  tags        String[]  @default([])
  version     Int       @default(1)
  parentId    String?   // For template inheritance
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String
  createdBy      User         @relation(fields: [createdById], references: [id])
  createdById    String
  agent          Agent?       @relation(fields: [agentId], references: [id])
  agentId        String?

  // Template relations
  parent         AgentTemplate?  @relation("TemplateInheritance", fields: [parentId], references: [id])
  children       AgentTemplate[] @relation("TemplateInheritance")
  promptTemplates PromptTemplate[]
  reviews        TemplateReview[]
  analytics      TemplateAnalytics[]

  @@map("agent_templates")
}

model PromptTemplate {
  id          String   @id @default(cuid())
  name        String
  description String?
  content     String
  variables   Json     @default("[]")  // Variable definitions with types
  category    String
  tags        String[] @default([])
  isPublic    Boolean  @default(false)
  usage       Int      @default(0)
  rating      Float?
  version     Int      @default(1)
  parentId    String?  // For template inheritance
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String
  createdBy      User         @relation(fields: [createdById], references: [id])
  createdById    String
  agentTemplate  AgentTemplate? @relation(fields: [agentTemplateId], references: [id])
  agentTemplateId String?

  // Template relations
  parent         PromptTemplate?  @relation("PromptInheritance", fields: [parentId], references: [id])
  children       PromptTemplate[] @relation("PromptInheritance")
  reviews        PromptReview[]
  analytics      PromptAnalytics[]
  optimizations  PromptOptimization[]

  @@map("prompt_templates")
}

model AgentCollaboration {
  id            String   @id @default(cuid())
  name          String
  description   String?
  agents        String[] // List of Agent IDs
  coordinatorId String   // Lead agent ID
  workflow      Json     // Collaboration workflow config
  sharedContext Json     @default("{}")
  status        CollaborationStatus @default(ACTIVE)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String
  createdBy      User         @relation(fields: [createdById], references: [id])
  createdById    String
  coordinator    Agent        @relation(fields: [coordinatorId], references: [id])

  executions CollaborationExecution[]

  @@map("agent_collaborations")
}

model CollaborationExecution {
  id              String          @id @default(cuid())
  status          ExecutionStatus @default(PENDING)
  input           Json
  output          Json?
  metadata        Json            @default("{}")
  error           String?
  startedAt       DateTime?
  completedAt     DateTime?
  createdAt       DateTime        @default(now())

  // Relations
  collaboration   AgentCollaboration @relation(fields: [collaborationId], references: [id], onDelete: Cascade)
  collaborationId String
  session         Session @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  sessionId       String

  @@map("collaboration_executions")
}

model TemplateReview {
  id        String   @id @default(cuid())
  rating    Int      // 1-5 stars
  comment   String?
  createdAt DateTime @default(now())

  // Relations
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String
  user           User         @relation(fields: [userId], references: [id])
  userId         String
  agentTemplate  AgentTemplate? @relation(fields: [agentTemplateId], references: [id])
  agentTemplateId String?

  @@unique([userId, agentTemplateId])
  @@map("template_reviews")
}

model PromptReview {
  id        String   @id @default(cuid())
  rating    Int      // 1-5 stars
  comment   String?
  createdAt DateTime @default(now())

  // Relations
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String
  user           User         @relation(fields: [userId], references: [id])
  userId         String
  promptTemplate PromptTemplate @relation(fields: [promptTemplateId], references: [id])
  promptTemplateId String?

  @@unique([userId, promptTemplateId])
  @@map("prompt_reviews")
}

model TemplateAnalytics {
  id              String   @id @default(cuid())
  date            DateTime
  usage           Int      @default(0)
  successRate     Float    @default(0)
  avgResponseTime Float    @default(0)
  tokenUsage      Json     @default("{}")
  errorRate       Float    @default(0)
  createdAt       DateTime @default(now())

  // Relations
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String
  agentTemplate  AgentTemplate @relation(fields: [agentTemplateId], references: [id])
  agentTemplateId String

  @@unique([agentTemplateId, date])
  @@map("template_analytics")
}

model PromptAnalytics {
  id              String   @id @default(cuid())
  date            DateTime
  usage           Int      @default(0)
  successRate     Float    @default(0)
  avgResponseTime Float    @default(0)
  tokenUsage      Json     @default("{}")
  errorRate       Float    @default(0)
  createdAt       DateTime @default(now())

  // Relations
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String
  promptTemplate PromptTemplate @relation(fields: [promptTemplateId], references: [id])
  promptTemplateId String

  @@unique([promptTemplateId, date])
  @@map("prompt_analytics")
}

model PromptOptimization {
  id              String   @id @default(cuid())
  originalContent String
  optimizedContent String
  improvement     Json     // Performance metrics improvement
  status          OptimizationStatus @default(PENDING)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String
  promptTemplate PromptTemplate @relation(fields: [promptTemplateId], references: [id])
  promptTemplateId String

  @@map("prompt_optimizations")
}

enum CollaborationStatus {
  ACTIVE
  PAUSED
  COMPLETED
  FAILED
}

enum OptimizationStatus {
  PENDING
  APPROVED
  REJECTED
  APPLIED
}

enum ToolStatus {
  DRAFT
  ACTIVE
  PAUSED
  ARCHIVED
}

enum ToolType {
  API
  DATABASE
  EMAIL
  FILE
  WEBHOOK
  CUSTOM
}

model Tool {
  id          String     @id @default(cuid())
  name        String
  description String?
  type        ToolType
  config      Json
  status      ToolStatus @default(DRAFT)
  metrics     Json       @default("{}")
  version     Int        @default(1)
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  // Relations
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String
  createdBy      User         @relation(fields: [createdById], references: [id])
  createdById    String

  executions ToolExecution[]

  @@map("tools")
}

model Workflow {
  id          String @id @default(cuid())
  name        String
  description String?
  definition  Json
  isActive    Boolean @default(true)
  metrics     Json    @default("{}")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String
  createdBy      User         @relation(fields: [createdById], references: [id])
  createdById    String

  @@map("workflows")
}

enum ExecutionStatus {
  PENDING
  RUNNING
  COMPLETED
  FAILED
  TIMEOUT
}

model AgentExecution {
  id          String          @id @default(cuid())
  status      ExecutionStatus @default(PENDING)
  input       String
  output      String?
  metadata    Json            @default("{}")
  error       String?
  startedAt   DateTime?
  completedAt DateTime?
  createdAt   DateTime        @default(now())

  // Relations
  agent     Agent   @relation(fields: [agentId], references: [id], onDelete: Cascade)
  agentId   String
  session   Session @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  sessionId String

  @@map("agent_executions")
}

model ToolExecution {
  id          String          @id @default(cuid())
  status      ExecutionStatus @default(PENDING)
  input       Json
  output      Json?
  metadata    Json            @default("{}")
  error       String?
  startedAt   DateTime?
  completedAt DateTime?
  createdAt   DateTime        @default(now())

  // Relations
  tool      Tool    @relation(fields: [toolId], references: [id], onDelete: Cascade)
  toolId    String
  session   Session @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  sessionId String

  @@map("tool_executions")
}

model KnowledgeBase {
  id          String   @id @default(cuid())
  name        String
  description String?
  documents   Json     @default("[]")
  settings    Json     @default("{}")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String

  @@map("knowledge_base")
}

model Widget {
  id          String   @id @default(cuid())
  name        String
  description String?
  config      Json
  embedCode   String
  isActive    Boolean  @default(true)
  metrics     Json     @default("{}")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String

  @@map("widgets")
}

enum NotificationType {
  EMAIL
  SMS
  WEBHOOK
  PUSH
  SLACK
  TEAMS
}

enum NotificationStatus {
  PENDING
  SENT
  DELIVERED
  FAILED
}

model Notification {
  id        String             @id @default(cuid())
  type      NotificationType
  status    NotificationStatus @default(PENDING)
  title     String
  content   String
  metadata  Json               @default("{}")
  sentAt    DateTime?
  createdAt DateTime           @default(now())

  // Relations
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId         String

  @@map("notifications")
}

model BillingRecord {
  id          String   @id @default(cuid())
  type        String
  amount      Decimal  @db.Decimal(10, 4)
  currency    String   @default("USD")
  description String
  metadata    Json     @default("{}")
  billedAt    DateTime @default(now())
  createdAt   DateTime @default(now())

  // Relations
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String

  @@map("billing_records")
}

enum AuditAction {
  CREATE
  UPDATE
  DELETE
  LOGIN
  LOGOUT
  EXECUTE
  APPROVE
  REJECT
}

model AuditLog {
  id          String      @id @default(cuid())
  action      AuditAction
  resource    String
  resourceId  String?
  details     Json        @default("{}")
  ipAddress   String?
  userAgent   String?
  createdAt   DateTime    @default(now())

  // Relations
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId         String

  @@map("audit_logs")
}