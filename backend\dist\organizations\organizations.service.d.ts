import { PrismaService } from '../prisma/prisma.service';
import { ApixGateway } from '../websocket/apix.gateway';
import { OrganizationStatus } from '@prisma/client';
export interface CreateOrganizationDto {
    name: string;
    slug: string;
    domain?: string;
    settings?: any;
    quotas?: any;
    billing?: any;
    branding?: any;
}
export interface UpdateOrganizationDto {
    name?: string;
    slug?: string;
    domain?: string;
    settings?: any;
    quotas?: any;
    billing?: any;
    branding?: any;
    status?: OrganizationStatus;
    isActive?: boolean;
}
export interface OrganizationFilters {
    search?: string;
    status?: OrganizationStatus;
    isActive?: boolean;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}
export declare class OrganizationService {
    private prisma;
    private apixGateway;
    constructor(prisma: PrismaService, apixGateway: ApixGateway);
    createOrganization(createOrganizationDto: CreateOrganizationDto, createdById: string): Promise<any>;
    updateOrganization(organizationId: string, updateOrganizationDto: UpdateOrganizationDto, updatedById: string): Promise<any>;
    deleteOrganization(organizationId: string, deletedById: string): Promise<{
        message: string;
    }>;
    getOrganizations(filters: OrganizationFilters): Promise<{
        organizations: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            pages: number;
        };
    }>;
    getOrganizationById(organizationId: string): Promise<any>;
    getOrganizationStats(organizationId: string): Promise<{
        users: any;
        agents: any;
        tools: any;
        workflows: any;
        sessions: any;
        recentActivity: any;
        quotas: any;
        billing: any;
    }>;
    suspendOrganization(organizationId: string, suspendedById: string, reason?: string): Promise<{
        message: string;
    }>;
    activateOrganization(organizationId: string, activatedById: string): Promise<{
        message: string;
    }>;
}
