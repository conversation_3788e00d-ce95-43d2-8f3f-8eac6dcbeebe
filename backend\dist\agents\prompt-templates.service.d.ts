import { PrismaService } from '../prisma/prisma.service';
import { ApixGateway } from '../websocket/apix.gateway';
import { CreatePromptTemplateDto, UpdatePromptTemplateDto, CreatePromptReviewDto } from './dto/agent.dto';
export declare class PromptTemplatesService {
    private prisma;
    private apixGateway;
    constructor(prisma: PrismaService, apixGateway: ApixGateway);
    create(userId: string, organizationId: string, dto: CreatePromptTemplateDto): Promise<{
        success: boolean;
        data: any;
        message: string;
    }>;
    findAll(organizationId: string, options?: {
        category?: string;
        isPublic?: boolean;
        search?: string;
        agentTemplateId?: string;
        page?: number;
        limit?: number;
        sortBy?: 'name' | 'usage' | 'rating' | 'createdAt';
        sortOrder?: 'asc' | 'desc';
    }): Promise<{
        success: boolean;
        data: {
            templates: any;
            pagination: {
                page: number;
                limit: number;
                total: any;
                pages: number;
            };
        };
    }>;
    findOne(id: string, organizationId: string): Promise<{
        success: boolean;
        data: any;
    }>;
    update(id: string, userId: string, organizationId: string, dto: UpdatePromptTemplateDto): Promise<{
        success: boolean;
        data: any;
        message: string;
    }>;
    delete(id: string, userId: string, organizationId: string): Promise<{
        success: boolean;
        message: string;
    }>;
    duplicate(id: string, userId: string, organizationId: string, name?: string): Promise<{
        success: boolean;
        data: any;
        message: string;
    }>;
    addReview(templateId: string, userId: string, organizationId: string, dto: CreatePromptReviewDto): Promise<{
        success: boolean;
        data: any;
        message: string;
    }>;
    optimizePrompt(id: string, userId: string, organizationId: string): Promise<{
        success: boolean;
        data: any;
        message: string;
    }>;
    getCategories(organizationId: string): Promise<{
        success: boolean;
        data: any;
    }>;
    validateVariables(content: string, variables: any[]): Promise<{
        success: boolean;
        data: {
            isValid: boolean;
            usedVariables: string[];
            definedVariables: any[];
            missingVariables: string[];
            unusedVariables: any[];
        };
    }>;
    incrementUsage(id: string): Promise<void>;
    private generateOptimizedPrompt;
}
