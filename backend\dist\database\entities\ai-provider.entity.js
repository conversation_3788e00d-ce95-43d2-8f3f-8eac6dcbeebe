"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProviderHealthCheck = exports.ProviderUsage = exports.AIModel = exports.AIProvider = exports.ProviderType = void 0;
const typeorm_1 = require("typeorm");
const user_entity_1 = require("../../users/user.entity");
const organization_entity_1 = require("../../organizations/organization.entity");
var ProviderType;
(function (ProviderType) {
    ProviderType["OPENAI"] = "OPENAI";
    ProviderType["CLAUDE"] = "CLAUDE";
    ProviderType["GEMINI"] = "GEMINI";
    ProviderType["MISTRAL"] = "MISTRAL";
    ProviderType["GROQ"] = "GROQ";
    ProviderType["DEEPSEEK"] = "DEEPSEEK";
    ProviderType["HUGGING_FACE"] = "HUGGING_FACE";
    ProviderType["LOCAL_AI"] = "LOCAL_AI";
    ProviderType["CUSTOM"] = "CUSTOM";
})(ProviderType || (exports.ProviderType = ProviderType = {}));
let AIProvider = class AIProvider {
};
exports.AIProvider = AIProvider;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], AIProvider.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], AIProvider.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ProviderType
    }),
    __metadata("design:type", String)
], AIProvider.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)('jsonb'),
    __metadata("design:type", Object)
], AIProvider.prototype, "config", void 0);
__decorate([
    (0, typeorm_1.Column)('jsonb', { nullable: true }),
    __metadata("design:type", Array)
], AIProvider.prototype, "models", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], AIProvider.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)('jsonb', { nullable: true }),
    __metadata("design:type", Object)
], AIProvider.prototype, "quotaLimits", void 0);
__decorate([
    (0, typeorm_1.Column)('jsonb', { nullable: true }),
    __metadata("design:type", Object)
], AIProvider.prototype, "performanceMetrics", void 0);
__decorate([
    (0, typeorm_1.Column)('uuid'),
    __metadata("design:type", String)
], AIProvider.prototype, "organizationId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => organization_entity_1.Organization),
    (0, typeorm_1.JoinColumn)({ name: 'organizationId' }),
    __metadata("design:type", typeof (_a = typeof organization_entity_1.Organization !== "undefined" && organization_entity_1.Organization) === "function" ? _a : Object)
], AIProvider.prototype, "organization", void 0);
__decorate([
    (0, typeorm_1.Column)('uuid'),
    __metadata("design:type", String)
], AIProvider.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'createdBy' }),
    __metadata("design:type", user_entity_1.User)
], AIProvider.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => AIModel, model => model.provider),
    __metadata("design:type", Array)
], AIProvider.prototype, "aiModels", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => ProviderUsage, usage => usage.provider),
    __metadata("design:type", Array)
], AIProvider.prototype, "usageRecords", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], AIProvider.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], AIProvider.prototype, "updatedAt", void 0);
exports.AIProvider = AIProvider = __decorate([
    (0, typeorm_1.Entity)('ai_providers')
], AIProvider);
let AIModel = class AIModel {
};
exports.AIModel = AIModel;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], AIModel.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)('uuid'),
    __metadata("design:type", String)
], AIModel.prototype, "providerId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => AIProvider, provider => provider.aiModels),
    (0, typeorm_1.JoinColumn)({ name: 'providerId' }),
    __metadata("design:type", AIProvider)
], AIModel.prototype, "provider", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], AIModel.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)('text', { nullable: true }),
    __metadata("design:type", String)
], AIModel.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], AIModel.prototype, "version", void 0);
__decorate([
    (0, typeorm_1.Column)('jsonb'),
    __metadata("design:type", Object)
], AIModel.prototype, "capabilities", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], AIModel.prototype, "fineTuned", void 0);
__decorate([
    (0, typeorm_1.Column)('int', { default: 4096 }),
    __metadata("design:type", Number)
], AIModel.prototype, "contextLength", void 0);
__decorate([
    (0, typeorm_1.Column)('jsonb'),
    __metadata("design:type", Object)
], AIModel.prototype, "costPer1KTokens", void 0);
__decorate([
    (0, typeorm_1.Column)('jsonb', { nullable: true }),
    __metadata("design:type", Object)
], AIModel.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], AIModel.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], AIModel.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], AIModel.prototype, "updatedAt", void 0);
exports.AIModel = AIModel = __decorate([
    (0, typeorm_1.Entity)('ai_models')
], AIModel);
let ProviderUsage = class ProviderUsage {
};
exports.ProviderUsage = ProviderUsage;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ProviderUsage.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)('uuid'),
    __metadata("design:type", String)
], ProviderUsage.prototype, "providerId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => AIProvider, provider => provider.usageRecords),
    (0, typeorm_1.JoinColumn)({ name: 'providerId' }),
    __metadata("design:type", AIProvider)
], ProviderUsage.prototype, "provider", void 0);
__decorate([
    (0, typeorm_1.Column)('date'),
    __metadata("design:type", Date)
], ProviderUsage.prototype, "date", void 0);
__decorate([
    (0, typeorm_1.Column)('int', { default: 0 }),
    __metadata("design:type", Number)
], ProviderUsage.prototype, "requests", void 0);
__decorate([
    (0, typeorm_1.Column)('int', { default: 0 }),
    __metadata("design:type", Number)
], ProviderUsage.prototype, "tokensUsed", void 0);
__decorate([
    (0, typeorm_1.Column)('int', { default: 0 }),
    __metadata("design:type", Number)
], ProviderUsage.prototype, "costInCents", void 0);
__decorate([
    (0, typeorm_1.Column)('int', { default: 0 }),
    __metadata("design:type", Number)
], ProviderUsage.prototype, "successfulRequests", void 0);
__decorate([
    (0, typeorm_1.Column)('int', { default: 0 }),
    __metadata("design:type", Number)
], ProviderUsage.prototype, "failedRequests", void 0);
__decorate([
    (0, typeorm_1.Column)('float', { default: 0 }),
    __metadata("design:type", Number)
], ProviderUsage.prototype, "averageLatency", void 0);
__decorate([
    (0, typeorm_1.Column)('jsonb', { nullable: true }),
    __metadata("design:type", Object)
], ProviderUsage.prototype, "breakdown", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ProviderUsage.prototype, "createdAt", void 0);
exports.ProviderUsage = ProviderUsage = __decorate([
    (0, typeorm_1.Entity)('provider_usage')
], ProviderUsage);
let ProviderHealthCheck = class ProviderHealthCheck {
};
exports.ProviderHealthCheck = ProviderHealthCheck;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ProviderHealthCheck.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)('uuid'),
    __metadata("design:type", String)
], ProviderHealthCheck.prototype, "providerId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => AIProvider),
    (0, typeorm_1.JoinColumn)({ name: 'providerId' }),
    __metadata("design:type", AIProvider)
], ProviderHealthCheck.prototype, "provider", void 0);
__decorate([
    (0, typeorm_1.Column)('timestamp'),
    __metadata("design:type", Date)
], ProviderHealthCheck.prototype, "checkedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['healthy', 'degraded', 'unhealthy']
    }),
    __metadata("design:type", String)
], ProviderHealthCheck.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)('int'),
    __metadata("design:type", Number)
], ProviderHealthCheck.prototype, "responseTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], ProviderHealthCheck.prototype, "isAvailable", void 0);
__decorate([
    (0, typeorm_1.Column)('text', { nullable: true }),
    __metadata("design:type", String)
], ProviderHealthCheck.prototype, "errorMessage", void 0);
__decorate([
    (0, typeorm_1.Column)('jsonb', { nullable: true }),
    __metadata("design:type", Object)
], ProviderHealthCheck.prototype, "metrics", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ProviderHealthCheck.prototype, "createdAt", void 0);
exports.ProviderHealthCheck = ProviderHealthCheck = __decorate([
    (0, typeorm_1.Entity)('provider_health_checks')
], ProviderHealthCheck);
//# sourceMappingURL=ai-provider.entity.js.map