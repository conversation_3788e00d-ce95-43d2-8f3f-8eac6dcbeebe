import { Repository } from 'typeorm';
import { Agent, AgentCollaboration } from '../database/entities/agent.entity';
import { AgentExecution } from '../database/entities/agent-execution.entity';
import { SessionMemoryService } from './session-memory.service';
import { AgentAnalyticsService } from './agent-analytics.service';
import { ApixGateway } from '../websocket/apix.gateway';
import { AIProviderIntegrationService } from '../providers/ai-provider-integration.service';
import { AIProviderSelectorService } from '../providers/ai-provider-selector.service';
import { CreateAgentInstanceDto, UpdateAgentInstanceDto, ExecuteAgentDto } from './dto/agent.dto';
export declare class AgentOrchestratorService {
    private agentRepository;
    private executionRepository;
    private collaborationRepository;
    private sessionMemoryService;
    private analyticsService;
    private apixGateway;
    private aiProviderIntegration;
    private aiProviderSelector;
    private readonly logger;
    constructor(agentRepository: Repository<Agent>, executionRepository: Repository<AgentExecution>, collaborationRepository: Repository<AgentCollaboration>, sessionMemoryService: SessionMemoryService, analyticsService: AgentAnalyticsService, apixGateway: ApixGateway, aiProviderIntegration: AIProviderIntegrationService, aiProviderSelector: AIProviderSelectorService);
    createAgent(createAgentDto: CreateAgentInstanceDto, organizationId: string, userId: string): Promise<Agent>;
    updateAgent(agentId: string, updateAgentDto: UpdateAgentInstanceDto, organizationId: string): Promise<Agent>;
    deleteAgent(agentId: string, organizationId: string): Promise<void>;
    executeAgent(agentId: string, executeDto: ExecuteAgentDto, organizationId: string): Promise<AgentExecution>;
    private executeAgentLogic;
    private buildSystemPrompt;
    private getRequiredCapabilities;
    private mapProviderType;
    private updateAgentMetrics;
    createCollaboration(name: string, agentIds: string[], coordinatorId: string, workflow: Record<string, any>, organizationId: string, userId: string): Promise<AgentCollaboration>;
    executeCollaboration(collaborationId: string, input: string, organizationId: string): Promise<{
        collaborationId: string;
        results: Array<{
            agentId: string;
            output: string;
            executionId: string;
        }>;
        finalOutput: string;
    }>;
    private executeCollaborationWorkflow;
    getAgentsByOrganization(organizationId: string): Promise<Agent[]>;
    getAgentById(agentId: string, organizationId: string): Promise<Agent>;
    getAgentExecutions(agentId: string, organizationId: string): Promise<AgentExecution[]>;
    getCollaborationsByOrganization(organizationId: string): Promise<AgentCollaboration[]>;
}
