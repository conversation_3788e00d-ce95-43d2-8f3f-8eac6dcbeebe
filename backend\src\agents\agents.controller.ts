import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards, Req, Sse } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { Observable } from 'rxjs';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { AgentOrchestratorService } from './agent-orchestrator.service';
import { AgentAnalyticsService } from './agent-analytics.service';
import { SessionMemoryService } from './session-memory.service';
import { CreateAgentInstanceDto, UpdateAgentInstanceDto, ExecuteAgentDto } from './dto/agent.dto';

@ApiTags('Agents')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('api/v1/agents')
export class AgentsController {
  constructor(
    private readonly orchestratorService: AgentOrchestratorService,
    private readonly analyticsService: AgentAnalyticsService,
    private readonly sessionMemoryService: SessionMemoryService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a new agent instance' })
  @ApiResponse({ status: 201, description: 'Agent created successfully' })
  async createAgent(
    @Body() createAgentDto: CreateAgentInstanceDto,
    @Req() req: any,
  ) {
    try {
      const agent = await this.orchestratorService.createAgent(
        createAgentDto,
        req.user.organizationId,
        req.user.id,
      );
      return {
        success: true,
        data: agent,
        message: 'Agent created successfully',
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Failed to create agent',
      };
    }
  }

  @Get()
  @ApiOperation({ summary: 'Get all agents for organization' })
  @ApiResponse({ status: 200, description: 'Agents retrieved successfully' })
  async getAgents(@Req() req: any) {
    try {
      const agents = await this.orchestratorService.getAgentsByOrganization(
        req.user.organizationId,
      );
      return {
        success: true,
        data: agents,
        message: 'Agents retrieved successfully',
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Failed to retrieve agents',
      };
    }
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get agent by ID' })
  @ApiResponse({ status: 200, description: 'Agent retrieved successfully' })
  async getAgent(@Param('id') id: string, @Req() req: any) {
    try {
      const agent = await this.orchestratorService.getAgentById(
        id,
        req.user.organizationId,
      );
      return {
        success: true,
        data: agent,
        message: 'Agent retrieved successfully',
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Failed to retrieve agent',
      };
    }
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update agent' })
  @ApiResponse({ status: 200, description: 'Agent updated successfully' })
  async updateAgent(
    @Param('id') id: string,
    @Body() updateAgentDto: UpdateAgentInstanceDto,
    @Req() req: any,
  ) {
    try {
      const agent = await this.orchestratorService.updateAgent(
        id,
        updateAgentDto,
        req.user.organizationId,
      );
      return {
        success: true,
        data: agent,
        message: 'Agent updated successfully',
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Failed to update agent',
      };
    }
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete agent' })
  @ApiResponse({ status: 200, description: 'Agent deleted successfully' })
  async deleteAgent(@Param('id') id: string, @Req() req: any) {
    try {
      await this.orchestratorService.deleteAgent(id, req.user.organizationId);
      return {
        success: true,
        message: 'Agent deleted successfully',
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Failed to delete agent',
      };
    }
  }

  @Post(':id/execute')
  @ApiOperation({ summary: 'Execute agent with message' })
  @ApiResponse({ status: 200, description: 'Agent executed successfully' })
  async executeAgent(
    @Param('id') id: string,
    @Body() executeDto: ExecuteAgentDto,
    @Req() req: any,
  ) {
    try {
      const execution = await this.orchestratorService.executeAgent(
        id,
        executeDto,
        req.user.organizationId,
      );
      return {
        success: true,
        data: execution,
        message: 'Agent executed successfully',
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Failed to execute agent',
      };
    }
  }

  @Get(':id/executions')
  @ApiOperation({ summary: 'Get agent execution history' })
  @ApiResponse({ status: 200, description: 'Executions retrieved successfully' })
  async getAgentExecutions(@Param('id') id: string, @Req() req: any) {
    try {
      const executions = await this.orchestratorService.getAgentExecutions(
        id,
        req.user.organizationId,
      );
      return {
        success: true,
        data: executions,
        message: 'Executions retrieved successfully',
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Failed to retrieve executions',
      };
    }
  }

  @Get(':id/analytics')
  @ApiOperation({ summary: 'Get agent analytics' })
  @ApiResponse({ status: 200, description: 'Analytics retrieved successfully' })
  async getAgentAnalytics(@Param('id') id: string, @Req() req: any) {
    try {
      const analytics = await this.analyticsService.getAgentAnalytics(
        id,
        req.user.organizationId,
      );
      return {
        success: true,
        data: analytics,
        message: 'Analytics retrieved successfully',
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Failed to retrieve analytics',
      };
    }
  }

  @Get(':id/sessions')
  @ApiOperation({ summary: 'Get agent sessions' })
  @ApiResponse({ status: 200, description: 'Sessions retrieved successfully' })
  async getAgentSessions(@Param('id') id: string, @Req() req: any) {
    try {
      const sessions = await this.sessionMemoryService.getSessionsByAgent(
        id,
        req.user.organizationId,
      );
      return {
        success: true,
        data: sessions,
        message: 'Sessions retrieved successfully',
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Failed to retrieve sessions',
      };
    }
  }

  @Get('sessions/:sessionId/history')
  @ApiOperation({ summary: 'Get session conversation history' })
  @ApiResponse({ status: 200, description: 'Session history retrieved successfully' })
  async getSessionHistory(
    @Param('sessionId') sessionId: string,
    @Query('limit') limit?: number,
    @Req() req: any,
  ) {
    try {
      const history = await this.sessionMemoryService.getSessionHistory(
        sessionId,
        req.user.organizationId,
        limit,
      );
      return {
        success: true,
        data: history,
        message: 'Session history retrieved successfully',
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Failed to retrieve session history',
      };
    }
  }

  @Delete('sessions/:sessionId')
  @ApiOperation({ summary: 'Delete session' })
  @ApiResponse({ status: 200, description: 'Session deleted successfully' })
  async deleteSession(@Param('sessionId') sessionId: string, @Req() req: any) {
    try {
      await this.sessionMemoryService.deleteSession(
        sessionId,
        req.user.organizationId,
      );
      return {
        success: true,
        message: 'Session deleted successfully',
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Failed to delete session',
      };
    }
  }

  @Get('analytics/organization')
  @ApiOperation({ summary: 'Get organization-wide agent analytics' })
  @ApiResponse({ status: 200, description: 'Organization analytics retrieved successfully' })
  async getOrganizationAnalytics(@Req() req: any) {
    try {
      const analytics = await this.analyticsService.getOrganizationAnalytics(
        req.user.organizationId,
      );
      return {
        success: true,
        data: analytics,
        message: 'Organization analytics retrieved successfully',
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Failed to retrieve organization analytics',
      };
    }
  }

  @Get('analytics/providers')
  @ApiOperation({ summary: 'Get provider performance analytics' })
  @ApiResponse({ status: 200, description: 'Provider analytics retrieved successfully' })
  async getProviderAnalytics(@Req() req: any) {
    try {
      const analytics = await this.analyticsService.getProviderPerformance(
        req.user.organizationId,
      );
      return {
        success: true,
        data: analytics,
        message: 'Provider analytics retrieved successfully',
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Failed to retrieve provider analytics',
      };
    }
  }
}