import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { ApixGateway } from '../websocket/apix.gateway';

export interface CreatePermissionDto {
  name: string;
  description?: string;
  resource: string;
  action: string;
  organizationId: string;
}

export interface UpdatePermissionDto {
  name?: string;
  description?: string;
  resource?: string;
  action?: string;
}

export interface PermissionFilters {
  search?: string;
  resource?: string;
  organizationId?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

@Injectable()
export class PermissionService {
  constructor(
    private prisma: PrismaService,
    private apixGateway: ApixGateway,
  ) {}

  async createPermission(createPermissionDto: CreatePermissionDto, createdById: string) {
    const { name, description, resource, action, organizationId } = createPermissionDto;

    // Check if permission already exists in organization
    const existingPermission = await this.prisma.permission.findUnique({
      where: {
        name_organizationId: {
          name,
          organizationId,
        },
      },
    });

    if (existingPermission) {
      throw new ConflictException('Permission with this name already exists in organization');
    }

    // Verify organization exists
    const organization = await this.prisma.organization.findUnique({
      where: { id: organizationId },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    const result = await this.prisma.$transaction(async (tx) => {
      // Create permission
      const permission = await tx.permission.create({
        data: {
          name,
          description,
          resource,
          action,
          organizationId,
        },
        include: {
          organization: true,
        },
      });

      // Log audit event
      await tx.auditLog.create({
        data: {
          userId: createdById,
          organizationId,
          action: 'CREATE',
          resource: 'permission',
          resourceId: permission.id,
          details: {
            action: 'permission_created',
            name: permission.name,
            description: permission.description,
            resource: permission.resource,
            permissionAction: permission.action,
          },
        },
      });

      return permission;
    });

    // Publish APIX event
    await this.apixGateway.publishToOrganization(organizationId, {
      type: 'permission.created',
      payload: {
        permissionId: result.id,
        name: result.name,
        description: result.description,
        resource: result.resource,
        action: result.action,
        createdBy: createdById,
      },
    });

    return result;
  }

  async updatePermission(permissionId: string, updatePermissionDto: UpdatePermissionDto, updatedById: string) {
    const { name, description, resource, action } = updatePermissionDto;

    const existingPermission = await this.prisma.permission.findUnique({
      where: { id: permissionId },
      include: { organization: true },
    });

    if (!existingPermission) {
      throw new NotFoundException('Permission not found');
    }

    if (existingPermission.isSystem) {
      throw new BadRequestException('Cannot modify system permission');
    }

    // Check name uniqueness if name is being updated
    if (name && name !== existingPermission.name) {
      const nameExists = await this.prisma.permission.findUnique({
        where: {
          name_organizationId: {
            name,
            organizationId: existingPermission.organizationId,
          },
        },
      });

      if (nameExists) {
        throw new ConflictException('Permission name already exists in organization');
      }
    }

    const result = await this.prisma.$transaction(async (tx) => {
      // Update permission
      const permission = await tx.permission.update({
        where: { id: permissionId },
        data: {
          ...(name && { name }),
          ...(description !== undefined && { description }),
          ...(resource && { resource }),
          ...(action && { action }),
        },
        include: {
          organization: true,
        },
      });

      // Log audit event
      await tx.auditLog.create({
        data: {
          userId: updatedById,
          organizationId: existingPermission.organizationId,
          action: 'UPDATE',
          resource: 'permission',
          resourceId: permissionId,
          details: {
            action: 'permission_updated',
            changes: updatePermissionDto,
          },
        },
      });

      return permission;
    });

    // Publish APIX event
    await this.apixGateway.publishToOrganization(existingPermission.organizationId, {
      type: 'permission.updated',
      payload: {
        permissionId,
        changes: updatePermissionDto,
        updatedBy: updatedById,
      },
    });

    return result;
  }

  async deletePermission(permissionId: string, deletedById: string) {
    const permission = await this.prisma.permission.findUnique({
      where: { id: permissionId },
      include: {
        organization: true,
        rolePermissions: true,
        userPermissions: true,
      },
    });

    if (!permission) {
      throw new NotFoundException('Permission not found');
    }

    if (permission.isSystem) {
      throw new BadRequestException('Cannot delete system permission');
    }

    if (permission.rolePermissions.length > 0 || permission.userPermissions.length > 0) {
      throw new BadRequestException('Cannot delete permission that is assigned to roles or users');
    }

    await this.prisma.$transaction(async (tx) => {
      // Delete permission
      await tx.permission.delete({
        where: { id: permissionId },
      });

      // Log audit event
      await tx.auditLog.create({
        data: {
          userId: deletedById,
          organizationId: permission.organizationId,
          action: 'DELETE',
          resource: 'permission',
          resourceId: permissionId,
          details: {
            action: 'permission_deleted',
            name: permission.name,
            description: permission.description,
            resource: permission.resource,
            permissionAction: permission.action,
          },
        },
      });
    });

    // Publish APIX event
    await this.apixGateway.publishToOrganization(permission.organizationId, {
      type: 'permission.deleted',
      payload: {
        permissionId,
        name: permission.name,
        deletedBy: deletedById,
      },
    });

    return { message: 'Permission deleted successfully' };
  }

  async getPermissions(filters: PermissionFilters) {
    const {
      search,
      resource,
      organizationId,
      page = 1,
      limit = 20,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = filters;

    const skip = (page - 1) * limit;

    const where: any = {};

    if (organizationId) {
      where.organizationId = organizationId;
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { resource: { contains: search, mode: 'insensitive' } },
        { action: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (resource) {
      where.resource = resource;
    }

    const [permissions, total] = await Promise.all([
      this.prisma.permission.findMany({
        where,
        include: {
          organization: true,
          rolePermissions: {
            include: {
              role: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
          userPermissions: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
        },
        orderBy: { [sortBy]: sortOrder },
        skip,
        take: limit,
      }),
      this.prisma.permission.count({ where }),
    ]);

    return {
      permissions,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }

  async getPermissionById(permissionId: string) {
    const permission = await this.prisma.permission.findUnique({
      where: { id: permissionId },
      include: {
        organization: true,
        rolePermissions: {
          include: {
            role: {
              select: {
                id: true,
                name: true,
                description: true,
                isActive: true,
              },
            },
          },
        },
        userPermissions: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                systemRole: true,
                isActive: true,
              },
            },
          },
        },
      },
    });

    if (!permission) {
      throw new NotFoundException('Permission not found');
    }

    return permission;
  }

  async assignPermissionToUser(permissionId: string, userId: string, assignedById: string) {
    const permission = await this.prisma.permission.findUnique({
      where: { id: permissionId },
      include: { organization: true },
    });

    if (!permission) {
      throw new NotFoundException('Permission not found');
    }

    const user = await this.prisma.user.findUnique({
      where: { id: userId, organizationId: permission.organizationId },
    });

    if (!user) {
      throw new NotFoundException('User not found in organization');
    }

    // Check if assignment already exists
    const existingAssignment = await this.prisma.userPermission.findUnique({
      where: {
        userId_permissionId: {
          userId,
          permissionId,
        },
      },
    });

    if (existingAssignment) {
      throw new ConflictException('User already has this permission');
    }

    await this.prisma.$transaction(async (tx) => {
      await tx.userPermission.create({
        data: {
          userId,
          permissionId,
          organizationId: permission.organizationId,
        },
      });

      // Log audit event
      await tx.auditLog.create({
        data: {
          userId: assignedById,
          organizationId: permission.organizationId,
          action: 'CREATE',
          resource: 'user_permission',
          resourceId: `${userId}-${permissionId}`,
          details: {
            action: 'permission_assigned',
            userId,
            permissionId,
            permissionName: permission.name,
            userEmail: user.email,
          },
        },
      });
    });

    // Publish APIX event
    await this.apixGateway.publishToOrganization(permission.organizationId, {
      type: 'permission.assigned',
      payload: {
        userId,
        permissionId,
        permissionName: permission.name,
        userEmail: user.email,
        assignedBy: assignedById,
      },
    });

    return { message: 'Permission assigned successfully' };
  }

  async removePermissionFromUser(permissionId: string, userId: string, removedById: string) {
    const assignment = await this.prisma.userPermission.findUnique({
      where: {
        userId_permissionId: {
          userId,
          permissionId,
        },
      },
      include: {
        permission: true,
        user: true,
      },
    });

    if (!assignment) {
      throw new NotFoundException('Permission assignment not found');
    }

    await this.prisma.$transaction(async (tx) => {
      await tx.userPermission.delete({
        where: {
          userId_permissionId: {
            userId,
            permissionId,
          },
        },
      });

      // Log audit event
      await tx.auditLog.create({
        data: {
          userId: removedById,
          organizationId: assignment.organizationId,
          action: 'DELETE',
          resource: 'user_permission',
          resourceId: `${userId}-${permissionId}`,
          details: {
            action: 'permission_removed',
            userId,
            permissionId,
            permissionName: assignment.permission.name,
            userEmail: assignment.user.email,
          },
        },
      });
    });

    // Publish APIX event
    await this.apixGateway.publishToOrganization(assignment.organizationId, {
      type: 'permission.removed',
      payload: {
        userId,
        permissionId,
        permissionName: assignment.permission.name,
        userEmail: assignment.user.email,
        removedBy: removedById,
      },
    });

    return { message: 'Permission removed successfully' };
  }

  async getResourceList(organizationId: string) {
    const resources = await this.prisma.permission.findMany({
      where: { organizationId },
      select: { resource: true },
      distinct: ['resource'],
      orderBy: { resource: 'asc' },
    });

    return resources.map(r => r.resource);
  }

  async getActionList(organizationId: string, resource?: string) {
    const where: any = { organizationId };
    if (resource) {
      where.resource = resource;
    }

    const actions = await this.prisma.permission.findMany({
      where,
      select: { action: true },
      distinct: ['action'],
      orderBy: { action: 'asc' },
    });

    return actions.map(a => a.action);
  }
}