import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
} from '@nestjs/common';
import { OrganizationService, CreateOrganizationDto, UpdateOrganizationDto, OrganizationFilters } from './organizations.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { PermissionGuard } from '../auth/permission.guard';
import { RequirePermissions } from '../auth/permissions.decorator';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

@ApiTags('organizations')
@ApiBearerAuth()
@Controller('api/organizations')
@UseGuards(JwtAuthGuard)
export class OrganizationController {
  constructor(private readonly organizationService: OrganizationService) {}

  @Post()
  @UseGuards(PermissionGuard)
  @RequirePermissions('system:write')
  @ApiOperation({ summary: 'Create a new organization' })
  async createOrganization(@Body() createOrganizationDto: CreateOrganizationDto, @Request() req) {
    return this.organizationService.createOrganization(createOrganizationDto, req.user.userId);
  }

  @Get()
  @UseGuards(PermissionGuard)
  @RequirePermissions('system:read')
  @ApiOperation({ summary: 'Get organizations with filtering and pagination' })
  async getOrganizations(@Query() filters: OrganizationFilters) {
    return this.organizationService.getOrganizations(filters);
  }

  @Get('current')
  @ApiOperation({ summary: 'Get current user organization' })
  async getCurrentOrganization(@Request() req) {
    return this.organizationService.getOrganizationById(req.user.organizationId);
  }

  @Get(':id')
  @UseGuards(PermissionGuard)
  @RequirePermissions('organization.read')
  @ApiOperation({ summary: 'Get organization by ID' })
  async getOrganizationById(@Param('id') id: string) {
    return this.organizationService.getOrganizationById(id);
  }

  @Get(':id/stats')
  @UseGuards(PermissionGuard)
  @RequirePermissions('organization.read')
  @ApiOperation({ summary: 'Get organization statistics' })
  async getOrganizationStats(@Param('id') id: string) {
    return this.organizationService.getOrganizationStats(id);
  }

  @Put(':id')
  @UseGuards(PermissionGuard)
  @RequirePermissions('organization.write')
  @ApiOperation({ summary: 'Update organization' })
  async updateOrganization(
    @Param('id') id: string,
    @Body() updateOrganizationDto: UpdateOrganizationDto,
    @Request() req,
  ) {
    return this.organizationService.updateOrganization(id, updateOrganizationDto, req.user.userId);
  }

  @Delete(':id')
  @UseGuards(PermissionGuard)
  @RequirePermissions('system:write')
  @ApiOperation({ summary: 'Delete organization' })
  async deleteOrganization(@Param('id') id: string, @Request() req) {
    return this.organizationService.deleteOrganization(id, req.user.userId);
  }

  @Put(':id/suspend')
  @UseGuards(PermissionGuard)
  @RequirePermissions('system:write')
  @ApiOperation({ summary: 'Suspend organization' })
  async suspendOrganization(
    @Param('id') id: string,
    @Body() body: { reason?: string },
    @Request() req,
  ) {
    return this.organizationService.suspendOrganization(id, req.user.userId, body.reason);
  }

  @Put(':id/activate')
  @UseGuards(PermissionGuard)
  @RequirePermissions('system:write')
  @ApiOperation({ summary: 'Activate organization' })
  async activateOrganization(@Param('id') id: string, @Request() req) {
    return this.organizationService.activateOrganization(id, req.user.userId);
  }
}