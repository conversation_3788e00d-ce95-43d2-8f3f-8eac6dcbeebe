"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "../ui/card";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../ui/tabs";
import { Badge } from "../ui/badge";
import { Checkbox } from "../ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "../ui/pagination";
import { Calendar } from "../ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { format } from "date-fns";
import {
  CalendarIcon,
  Check,
  Copy,
  Edit,
  Grid,
  List,
  MoreHorizontal,
  Search,
  Trash2,
} from "lucide-react";

interface Workflow {
  id: string;
  name: string;
  status: "active" | "draft" | "error";
  lastModified: Date;
  creator: string;
  description?: string;
}

export default function WorkflowList() {
  const [view, setView] = useState<"grid" | "list">("grid");
  const [selectedWorkflows, setSelectedWorkflows] = useState<string[]>([]);
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [creatorFilter, setCreatorFilter] = useState<string>("all");
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({ from: undefined, to: undefined });
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [currentPage, setCurrentPage] = useState<number>(1);

  // Mock data for demonstration
  const workflows: Workflow[] = [
    {
      id: "1",
      name: "Customer Support Assistant",
      status: "active",
      lastModified: new Date(2023, 5, 15),
      creator: "John Doe",
      description:
        "AI workflow for handling customer support inquiries with knowledge base integration",
    },
    {
      id: "2",
      name: "Content Generator",
      status: "draft",
      lastModified: new Date(2023, 6, 20),
      creator: "Jane Smith",
      description:
        "Creates blog posts and social media content based on topic inputs",
    },
    {
      id: "3",
      name: "Data Analysis Pipeline",
      status: "error",
      lastModified: new Date(2023, 7, 5),
      creator: "Alex Johnson",
      description: "Processes CSV data and generates insights reports",
    },
    {
      id: "4",
      name: "Email Responder",
      status: "active",
      lastModified: new Date(2023, 7, 10),
      creator: "John Doe",
      description:
        "Automatically responds to customer emails based on intent classification",
    },
    {
      id: "5",
      name: "Lead Qualification",
      status: "draft",
      lastModified: new Date(2023, 8, 1),
      creator: "Jane Smith",
      description:
        "Qualifies sales leads based on criteria and engagement metrics",
    },
    {
      id: "6",
      name: "Document Summarizer",
      status: "active",
      lastModified: new Date(2023, 8, 15),
      creator: "Alex Johnson",
      description: "Summarizes long documents and extracts key information",
    },
  ];

  // Filter workflows based on selected filters
  const filteredWorkflows = workflows.filter((workflow) => {
    // Status filter
    if (statusFilter !== "all" && workflow.status !== statusFilter)
      return false;

    // Creator filter
    if (creatorFilter !== "all" && workflow.creator !== creatorFilter)
      return false;

    // Date range filter
    if (dateRange.from && new Date(workflow.lastModified) < dateRange.from)
      return false;
    if (dateRange.to && new Date(workflow.lastModified) > dateRange.to)
      return false;

    // Search query
    if (
      searchQuery &&
      !workflow.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
      !workflow.description?.toLowerCase().includes(searchQuery.toLowerCase())
    )
      return false;

    return true;
  });

  // Pagination
  const itemsPerPage = 6;
  const totalPages = Math.ceil(filteredWorkflows.length / itemsPerPage);
  const paginatedWorkflows = filteredWorkflows.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage,
  );

  // Get unique creators for filter dropdown
  const creators = Array.from(new Set(workflows.map((w) => w.creator)));

  // Handle workflow selection
  const toggleWorkflowSelection = (id: string) => {
    setSelectedWorkflows((prev) =>
      prev.includes(id) ? prev.filter((wId) => wId !== id) : [...prev, id],
    );
  };

  // Handle bulk actions
  const handleBulkAction = (action: string) => {
    // Implementation would connect to backend API
    console.log(`Performing ${action} on workflows:`, selectedWorkflows);
    // Reset selection after action
    setSelectedWorkflows([]);
  };

  // Handle individual workflow actions
  const handleWorkflowAction = (action: string, id: string) => {
    // Implementation would connect to backend API
    console.log(`Performing ${action} on workflow:`, id);
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-500 hover:bg-green-600";
      case "draft":
        return "bg-yellow-500 hover:bg-yellow-600";
      case "error":
        return "bg-red-500 hover:bg-red-600";
      default:
        return "";
    }
  };

  return (
    <div className="bg-background p-6 rounded-lg shadow-sm w-full">
      <div className="flex flex-col space-y-4">
        {/* Header with title and create button */}
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold">Workflows</h2>
          <Button>Create Workflow</Button>
        </div>

        {/* Filters and view toggle */}
        <div className="flex flex-col md:flex-row justify-between gap-4">
          <div className="flex flex-1 gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search workflows..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="error">Error</SelectItem>
              </SelectContent>
            </Select>

            <Select value={creatorFilter} onValueChange={setCreatorFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Creator" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Creators</SelectItem>
                {creators.map((creator) => (
                  <SelectItem key={creator} value={creator}>
                    {creator}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="w-[240px] justify-start text-left font-normal"
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {dateRange.from ? (
                    dateRange.to ? (
                      <>
                        {format(dateRange.from, "LLL dd, y")} -{" "}
                        {format(dateRange.to, "LLL dd, y")}
                      </>
                    ) : (
                      format(dateRange.from, "LLL dd, y")
                    )
                  ) : (
                    <span>Date range</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="range"
                  selected={dateRange}
                  onSelect={setDateRange as any}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          <div className="flex items-center gap-2">
            {selectedWorkflows.length > 0 && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    Bulk Actions ({selectedWorkflows.length})
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem
                    onClick={() => handleBulkAction("activate")}
                  >
                    Activate Selected
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => handleBulkAction("deactivate")}
                  >
                    Deactivate Selected
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleBulkAction("delete")}>
                    Delete Selected
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleBulkAction("export")}>
                    Export Selected
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}

            <div className="border rounded-md flex">
              <Button
                variant="ghost"
                size="icon"
                className={view === "grid" ? "bg-muted" : ""}
                onClick={() => setView("grid")}
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className={view === "list" ? "bg-muted" : ""}
                onClick={() => setView("list")}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Workflows display */}
        {paginatedWorkflows.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <p className="text-muted-foreground mb-4">
              No workflows found matching your filters.
            </p>
            <Button
              variant="outline"
              onClick={() => {
                setStatusFilter("all");
                setCreatorFilter("all");
                setDateRange({ from: undefined, to: undefined });
                setSearchQuery("");
              }}
            >
              Clear Filters
            </Button>
          </div>
        ) : view === "grid" ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {paginatedWorkflows.map((workflow) => (
              <Card key={workflow.id} className="overflow-hidden">
                <CardHeader className="pb-2">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <Checkbox
                          checked={selectedWorkflows.includes(workflow.id)}
                          onCheckedChange={() =>
                            toggleWorkflowSelection(workflow.id)
                          }
                        />
                        <CardTitle className="text-lg">
                          {workflow.name}
                        </CardTitle>
                      </div>
                      <CardDescription className="mt-1">
                        {workflow.description}
                      </CardDescription>
                    </div>
                    <Badge className={getStatusBadgeColor(workflow.status)}>
                      {workflow.status.charAt(0).toUpperCase() +
                        workflow.status.slice(1)}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="text-sm text-muted-foreground pb-2">
                  <p>Created by: {workflow.creator}</p>
                  <p>
                    Last modified:{" "}
                    {format(workflow.lastModified, "MMM d, yyyy")}
                  </p>
                </CardContent>
                <CardFooter className="pt-2 flex justify-end gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleWorkflowAction("edit", workflow.id)}
                  >
                    <Edit className="h-4 w-4 mr-1" /> Edit
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={() =>
                          handleWorkflowAction("clone", workflow.id)
                        }
                      >
                        <Copy className="h-4 w-4 mr-2" /> Clone
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() =>
                          handleWorkflowAction("export", workflow.id)
                        }
                      >
                        <Check className="h-4 w-4 mr-2" /> Export
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() =>
                          handleWorkflowAction("delete", workflow.id)
                        }
                        className="text-red-600"
                      >
                        <Trash2 className="h-4 w-4 mr-2" /> Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </CardFooter>
              </Card>
            ))}
          </div>
        ) : (
          <div className="border rounded-md overflow-hidden">
            <table className="w-full">
              <thead>
                <tr className="bg-muted/50">
                  <th className="p-2 text-left w-8">
                    <Checkbox
                      checked={
                        selectedWorkflows.length ===
                          paginatedWorkflows.length &&
                        paginatedWorkflows.length > 0
                      }
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setSelectedWorkflows(
                            paginatedWorkflows.map((w) => w.id),
                          );
                        } else {
                          setSelectedWorkflows([]);
                        }
                      }}
                    />
                  </th>
                  <th className="p-2 text-left">Name</th>
                  <th className="p-2 text-left">Status</th>
                  <th className="p-2 text-left">Creator</th>
                  <th className="p-2 text-left">Last Modified</th>
                  <th className="p-2 text-left">Actions</th>
                </tr>
              </thead>
              <tbody>
                {paginatedWorkflows.map((workflow) => (
                  <tr key={workflow.id} className="border-t hover:bg-muted/50">
                    <td className="p-2">
                      <Checkbox
                        checked={selectedWorkflows.includes(workflow.id)}
                        onCheckedChange={() =>
                          toggleWorkflowSelection(workflow.id)
                        }
                      />
                    </td>
                    <td className="p-2">
                      <div>
                        <p className="font-medium">{workflow.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {workflow.description}
                        </p>
                      </div>
                    </td>
                    <td className="p-2">
                      <Badge className={getStatusBadgeColor(workflow.status)}>
                        {workflow.status.charAt(0).toUpperCase() +
                          workflow.status.slice(1)}
                      </Badge>
                    </td>
                    <td className="p-2">{workflow.creator}</td>
                    <td className="p-2">
                      {format(workflow.lastModified, "MMM d, yyyy")}
                    </td>
                    <td className="p-2">
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            handleWorkflowAction("edit", workflow.id)
                          }
                        >
                          <Edit className="h-4 w-4 mr-1" /> Edit
                        </Button>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={() =>
                                handleWorkflowAction("clone", workflow.id)
                              }
                            >
                              <Copy className="h-4 w-4 mr-2" /> Clone
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() =>
                                handleWorkflowAction("export", workflow.id)
                              }
                            >
                              <Check className="h-4 w-4 mr-2" /> Export
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() =>
                                handleWorkflowAction("delete", workflow.id)
                              }
                              className="text-red-600"
                            >
                              <Trash2 className="h-4 w-4 mr-2" /> Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination */}
        {filteredWorkflows.length > itemsPerPage && (
          <Pagination className="mt-4">
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() =>
                    setCurrentPage((prev) => Math.max(prev - 1, 1))
                  }
                  className={
                    currentPage === 1 ? "pointer-events-none opacity-50" : ""
                  }
                />
              </PaginationItem>

              {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                (page) => (
                  <PaginationItem key={page}>
                    <PaginationLink
                      isActive={currentPage === page}
                      onClick={() => setCurrentPage(page)}
                    >
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                ),
              )}

              <PaginationItem>
                <PaginationNext
                  onClick={() =>
                    setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                  }
                  className={
                    currentPage === totalPages
                      ? "pointer-events-none opacity-50"
                      : ""
                  }
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        )}
      </div>
    </div>
  );
}
