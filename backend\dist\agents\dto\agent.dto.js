"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentCollaborationDto = exports.ExecuteAgentDto = exports.UpdateAgentInstanceDto = exports.CreateAgentInstanceDto = exports.CreateAgentTemplateDto = exports.ProviderType = exports.AgentStatus = exports.AgentType = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
var AgentType;
(function (AgentType) {
    AgentType["BASIC"] = "BASIC";
    AgentType["TOOL_DRIVEN"] = "TOOL_DRIVEN";
    AgentType["HYBRID"] = "HYBRID";
    AgentType["MULTI_TASK"] = "MULTI_TASK";
    AgentType["MULTI_PROVIDER"] = "MULTI_PROVIDER";
    AgentType["COLLABORATIVE"] = "COLLABORATIVE";
})(AgentType || (exports.AgentType = AgentType = {}));
var AgentStatus;
(function (AgentStatus) {
    AgentStatus["ACTIVE"] = "ACTIVE";
    AgentStatus["INACTIVE"] = "INACTIVE";
    AgentStatus["ERROR"] = "ERROR";
    AgentStatus["PAUSED"] = "PAUSED";
})(AgentStatus || (exports.AgentStatus = AgentStatus = {}));
var ProviderType;
(function (ProviderType) {
    ProviderType["OPENAI"] = "OPENAI";
    ProviderType["CLAUDE"] = "CLAUDE";
    ProviderType["GEMINI"] = "GEMINI";
    ProviderType["MISTRAL"] = "MISTRAL";
    ProviderType["GROQ"] = "GROQ";
})(ProviderType || (exports.ProviderType = ProviderType = {}));
class CreateAgentTemplateDto {
}
exports.CreateAgentTemplateDto = CreateAgentTemplateDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateAgentTemplateDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateAgentTemplateDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateAgentTemplateDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], CreateAgentTemplateDto.prototype, "config", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateAgentTemplateDto.prototype, "skills", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ default: false }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], CreateAgentTemplateDto.prototype, "isPublic", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateAgentTemplateDto.prototype, "promptTemplate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: AgentType }),
    (0, class_validator_1.IsEnum)(AgentType),
    __metadata("design:type", String)
], CreateAgentTemplateDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: ProviderType, isArray: true }),
    (0, class_validator_1.IsEnum)(ProviderType, { each: true }),
    __metadata("design:type", Array)
], CreateAgentTemplateDto.prototype, "supportedProviders", void 0);
class CreateAgentInstanceDto {
}
exports.CreateAgentInstanceDto = CreateAgentInstanceDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateAgentInstanceDto.prototype, "templateId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateAgentInstanceDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], CreateAgentInstanceDto.prototype, "config", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: AgentType }),
    (0, class_validator_1.IsEnum)(AgentType),
    __metadata("design:type", String)
], CreateAgentInstanceDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: ProviderType }),
    (0, class_validator_1.IsEnum)(ProviderType),
    __metadata("design:type", String)
], CreateAgentInstanceDto.prototype, "primaryProvider", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: ProviderType, isArray: true }),
    (0, class_validator_1.IsEnum)(ProviderType, { each: true }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], CreateAgentInstanceDto.prototype, "fallbackProviders", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsObject)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], CreateAgentInstanceDto.prototype, "memoryConfig", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], CreateAgentInstanceDto.prototype, "skills", void 0);
class UpdateAgentInstanceDto {
}
exports.UpdateAgentInstanceDto = UpdateAgentInstanceDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateAgentInstanceDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsObject)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], UpdateAgentInstanceDto.prototype, "config", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: AgentStatus }),
    (0, class_validator_1.IsEnum)(AgentStatus),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateAgentInstanceDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: ProviderType }),
    (0, class_validator_1.IsEnum)(ProviderType),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateAgentInstanceDto.prototype, "primaryProvider", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: ProviderType, isArray: true }),
    (0, class_validator_1.IsEnum)(ProviderType, { each: true }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], UpdateAgentInstanceDto.prototype, "fallbackProviders", void 0);
class ExecuteAgentDto {
}
exports.ExecuteAgentDto = ExecuteAgentDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ExecuteAgentDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], ExecuteAgentDto.prototype, "sessionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsObject)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], ExecuteAgentDto.prototype, "context", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], ExecuteAgentDto.prototype, "streamResponse", void 0);
class AgentCollaborationDto {
}
exports.AgentCollaborationDto = AgentCollaborationDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AgentCollaborationDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsUUID)(4, { each: true }),
    __metadata("design:type", Array)
], AgentCollaborationDto.prototype, "agentIds", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AgentCollaborationDto.prototype, "coordinatorId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], AgentCollaborationDto.prototype, "workflow", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsObject)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], AgentCollaborationDto.prototype, "sharedContext", void 0);
//# sourceMappingURL=agent.dto.js.map