"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const apix_gateway_1 = require("../websocket/apix.gateway");
const bcrypt = require("bcryptjs");
const client_1 = require("@prisma/client");
let UserService = class UserService {
    constructor(prisma, apixGateway) {
        this.prisma = prisma;
        this.apixGateway = apixGateway;
    }
    async createUser(createUserDto, createdById) {
        const { email, name, password, systemRole = client_1.SystemUserRole.VIEWER, organizationId, roleIds = [], permissionIds = [] } = createUserDto;
        const existingUser = await this.prisma.user.findUnique({
            where: { email },
        });
        if (existingUser) {
            throw new common_1.ConflictException('User with this email already exists');
        }
        const organization = await this.prisma.organization.findUnique({
            where: { id: organizationId, status: client_1.OrganizationStatus.ACTIVE },
        });
        if (!organization) {
            throw new common_1.NotFoundException('Organization not found or inactive');
        }
        let passwordHash;
        if (password) {
            passwordHash = await bcrypt.hash(password, 12);
        }
        const result = await this.prisma.$transaction(async (tx) => {
            const user = await tx.user.create({
                data: {
                    email,
                    name,
                    passwordHash,
                    systemRole,
                    organizationId,
                },
                include: {
                    organization: true,
                    userRoles: {
                        include: {
                            role: true,
                        },
                    },
                    userPermissions: {
                        include: {
                            permission: true,
                        },
                    },
                },
            });
            if (roleIds.length > 0) {
                await tx.userRole.createMany({
                    data: roleIds.map(roleId => ({
                        userId: user.id,
                        roleId,
                        organizationId,
                    })),
                });
            }
            if (permissionIds.length > 0) {
                await tx.userPermission.createMany({
                    data: permissionIds.map(permissionId => ({
                        userId: user.id,
                        permissionId,
                        organizationId,
                    })),
                });
            }
            await tx.auditLog.create({
                data: {
                    userId: createdById,
                    organizationId,
                    action: 'CREATE',
                    resource: 'user',
                    resourceId: user.id,
                    details: {
                        action: 'user_created',
                        email: user.email,
                        name: user.name,
                        systemRole: user.systemRole,
                        roleIds,
                        permissionIds,
                    },
                },
            });
            return user;
        });
        await this.apixGateway.publishToOrganization(organizationId, {
            type: 'user.created',
            payload: {
                userId: result.id,
                email: result.email,
                name: result.name,
                systemRole: result.systemRole,
                createdBy: createdById,
            },
        });
        return this.sanitizeUser(result);
    }
    async updateUser(userId, updateUserDto, updatedById) {
        const { name, email, systemRole, isActive, roleIds, permissionIds } = updateUserDto;
        const existingUser = await this.prisma.user.findUnique({
            where: { id: userId },
            include: {
                organization: true,
                userRoles: true,
                userPermissions: true,
            },
        });
        if (!existingUser) {
            throw new common_1.NotFoundException('User not found');
        }
        if (email && email !== existingUser.email) {
            const emailExists = await this.prisma.user.findUnique({
                where: { email },
            });
            if (emailExists) {
                throw new common_1.ConflictException('Email already in use');
            }
        }
        const result = await this.prisma.$transaction(async (tx) => {
            const user = await tx.user.update({
                where: { id: userId },
                data: Object.assign(Object.assign(Object.assign(Object.assign({}, (name && { name })), (email && { email })), (systemRole && { systemRole })), (isActive !== undefined && { isActive })),
                include: {
                    organization: true,
                    userRoles: {
                        include: {
                            role: true,
                        },
                    },
                    userPermissions: {
                        include: {
                            permission: true,
                        },
                    },
                },
            });
            if (roleIds !== undefined) {
                await tx.userRole.deleteMany({
                    where: { userId },
                });
                if (roleIds.length > 0) {
                    await tx.userRole.createMany({
                        data: roleIds.map(roleId => ({
                            userId,
                            roleId,
                            organizationId: existingUser.organizationId,
                        })),
                    });
                }
            }
            if (permissionIds !== undefined) {
                await tx.userPermission.deleteMany({
                    where: { userId },
                });
                if (permissionIds.length > 0) {
                    await tx.userPermission.createMany({
                        data: permissionIds.map(permissionId => ({
                            userId,
                            permissionId,
                            organizationId: existingUser.organizationId,
                        })),
                    });
                }
            }
            await tx.auditLog.create({
                data: {
                    userId: updatedById,
                    organizationId: existingUser.organizationId,
                    action: 'UPDATE',
                    resource: 'user',
                    resourceId: userId,
                    details: {
                        action: 'user_updated',
                        changes: updateUserDto,
                    },
                },
            });
            return user;
        });
        await this.apixGateway.publishToOrganization(existingUser.organizationId, {
            type: 'user.updated',
            payload: {
                userId,
                changes: updateUserDto,
                updatedBy: updatedById,
            },
        });
        return this.sanitizeUser(result);
    }
    async deleteUser(userId, deletedById) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
            include: { organization: true },
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        await this.prisma.$transaction(async (tx) => {
            await tx.user.update({
                where: { id: userId },
                data: { isActive: false },
            });
            await tx.auditLog.create({
                data: {
                    userId: deletedById,
                    organizationId: user.organizationId,
                    action: 'DELETE',
                    resource: 'user',
                    resourceId: userId,
                    details: {
                        action: 'user_deleted',
                        email: user.email,
                        name: user.name,
                    },
                },
            });
        });
        await this.apixGateway.publishToOrganization(user.organizationId, {
            type: 'user.deleted',
            payload: {
                userId,
                email: user.email,
                name: user.name,
                deletedBy: deletedById,
            },
        });
        return { message: 'User deleted successfully' };
    }
    async getUsers(filters) {
        const { search, role, organizationId, isActive, page = 1, limit = 20, sortBy = 'createdAt', sortOrder = 'desc', } = filters;
        const skip = (page - 1) * limit;
        const where = {};
        if (organizationId) {
            where.organizationId = organizationId;
        }
        if (search) {
            where.OR = [
                { name: { contains: search, mode: 'insensitive' } },
                { email: { contains: search, mode: 'insensitive' } },
            ];
        }
        if (role) {
            where.systemRole = role;
        }
        if (isActive !== undefined) {
            where.isActive = isActive;
        }
        const [users, total] = await Promise.all([
            this.prisma.user.findMany({
                where,
                include: {
                    organization: true,
                    userRoles: {
                        include: {
                            role: true,
                        },
                    },
                    userPermissions: {
                        include: {
                            permission: true,
                        },
                    },
                },
                orderBy: { [sortBy]: sortOrder },
                skip,
                take: limit,
            }),
            this.prisma.user.count({ where }),
        ]);
        return {
            users: users.map(user => this.sanitizeUser(user)),
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit),
            },
        };
    }
    async getUserById(userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
            include: {
                organization: true,
                userRoles: {
                    include: {
                        role: true,
                    },
                },
                userPermissions: {
                    include: {
                        permission: true,
                    },
                },
                socialLogins: true,
            },
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        return this.sanitizeUser(user);
    }
    async bulkUpdateUsers(userIds, updateData, updatedById) {
        const users = await this.prisma.user.findMany({
            where: { id: { in: userIds } },
            include: { organization: true },
        });
        if (users.length !== userIds.length) {
            throw new common_1.BadRequestException('Some users not found');
        }
        const organizationId = users[0].organizationId;
        if (!users.every(user => user.organizationId === organizationId)) {
            throw new common_1.BadRequestException('All users must belong to the same organization');
        }
        await this.prisma.$transaction(async (tx) => {
            await tx.user.updateMany({
                where: { id: { in: userIds } },
                data: updateData,
            });
            await tx.auditLog.create({
                data: {
                    userId: updatedById,
                    organizationId,
                    action: 'UPDATE',
                    resource: 'user',
                    resourceId: userIds.join(','),
                    details: {
                        action: 'bulk_user_update',
                        userIds,
                        changes: updateData,
                    },
                },
            });
        });
        await this.apixGateway.publishToOrganization(organizationId, {
            type: 'user.bulk_updated',
            payload: {
                userIds,
                changes: updateData,
                updatedBy: updatedById,
            },
        });
        return { message: `${userIds.length} users updated successfully` };
    }
    async resetPassword(userId, newPassword, resetById) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
            include: { organization: true },
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        const passwordHash = await bcrypt.hash(newPassword, 12);
        await this.prisma.$transaction(async (tx) => {
            await tx.user.update({
                where: { id: userId },
                data: {
                    passwordHash,
                    resetToken: null,
                    resetTokenExpiry: null,
                },
            });
            await tx.auditLog.create({
                data: {
                    userId: resetById,
                    organizationId: user.organizationId,
                    action: 'UPDATE',
                    resource: 'user',
                    resourceId: userId,
                    details: {
                        action: 'password_reset',
                        email: user.email,
                    },
                },
            });
        });
        await this.apixGateway.publishToOrganization(user.organizationId, {
            type: 'user.password_reset',
            payload: {
                userId,
                email: user.email,
                resetBy: resetById,
            },
        });
        return { message: 'Password reset successfully' };
    }
    sanitizeUser(user) {
        const { passwordHash, resetToken, resetTokenExpiry } = user, sanitized = __rest(user, ["passwordHash", "resetToken", "resetTokenExpiry"]);
        return sanitized;
    }
};
exports.UserService = UserService;
exports.UserService = UserService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        apix_gateway_1.ApixGateway])
], UserService);
//# sourceMappingURL=users.service.js.map