import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, OneToMany, JoinColumn } from 'typeorm';
import { User } from '../../users/user.entity';
import { Organization } from '../../organizations/organization.entity';
import { AgentExecution } from './agent-execution.entity';

export enum AgentType {
  BASIC = 'BASIC',
  TOOL_DRIVEN = 'TOOL_DRIVEN', 
  HYBRID = 'HYBRID',
  MULTI_TASK = 'MULTI_TASK',
  MULTI_PROVIDER = 'MULTI_PROVIDER',
  COLLABORATIVE = 'COLLABORATIVE'
}

export enum AgentStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  ERROR = 'ERROR',
  PAUSED = 'PAUSED'
}

export enum ProviderType {
  OPENAI = 'OPENAI',
  CLAUDE = 'CLAUDE',
  GEMINI = 'GEMINI',
  MISTRAL = 'MISTRAL',
  GROQ = 'GROQ'
}

@Entity('agent_templates')
export class AgentTemplate {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column()
  category: string;

  @Column('text')
  description: string;

  @Column('jsonb')
  config: Record<string, any>;

  @Column('text', { array: true })
  skills: string[];

  @Column({ default: false })
  isPublic: boolean;

  @Column('text')
  promptTemplate: string;

  @Column({
    type: 'enum',
    enum: AgentType,
    default: AgentType.BASIC
  })
  type: AgentType;

  @Column('enum', { enum: ProviderType, array: true })
  supportedProviders: ProviderType[];

  @Column('jsonb', { nullable: true })
  metadata: Record<string, any>;

  @Column('uuid')
  createdBy: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'createdBy' })
  creator: User;

  @Column('uuid')
  organizationId: string;

  @ManyToOne(() => Organization)
  @JoinColumn({ name: 'organizationId' })
  organization: Organization;

  @OneToMany(() => Agent, agent => agent.template)
  instances: Agent[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}

@Entity('agents')
export class Agent {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column('uuid')
  templateId: string;

  @ManyToOne(() => AgentTemplate, template => template.instances)
  @JoinColumn({ name: 'templateId' })
  template: AgentTemplate;

  @Column('jsonb')
  config: Record<string, any>;

  @Column({
    type: 'enum',
    enum: AgentType,
    default: AgentType.BASIC
  })
  type: AgentType;

  @Column({
    type: 'enum',
    enum: AgentStatus,
    default: AgentStatus.ACTIVE
  })
  status: AgentStatus;

  @Column({
    type: 'enum',
    enum: ProviderType
  })
  primaryProvider: ProviderType;

  @Column('enum', { enum: ProviderType, array: true, nullable: true })
  fallbackProviders: ProviderType[];

  @Column('jsonb', { nullable: true })
  memoryConfig: {
    maxTokens: number;
    retentionDays: number;
    enableLongTerm: boolean;
  };

  @Column('text', { array: true, nullable: true })
  skills: string[];

  @Column('jsonb', { nullable: true })
  performanceMetrics: {
    totalExecutions: number;
    successRate: number;
    averageResponseTime: number;
    lastExecuted: Date;
  };

  @Column('uuid')
  organizationId: string;

  @ManyToOne(() => Organization)
  @JoinColumn({ name: 'organizationId' })
  organization: Organization;

  @Column('uuid')
  createdBy: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'createdBy' })
  creator: User;

  @OneToMany(() => AgentExecution, execution => execution.agent)
  executions: AgentExecution[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}

@Entity('agent_collaborations')
export class AgentCollaboration {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column('uuid', { array: true })
  agentIds: string[];

  @Column('uuid')
  coordinatorId: string;

  @Column('jsonb')
  workflow: Record<string, any>;

  @Column('jsonb', { nullable: true })
  sharedContext: Record<string, any>;

  @Column({
    type: 'enum',
    enum: ['ACTIVE', 'PAUSED', 'COMPLETED', 'FAILED'],
    default: 'ACTIVE'
  })
  status: string;

  @Column('uuid')
  organizationId: string;

  @ManyToOne(() => Organization)
  @JoinColumn({ name: 'organizationId' })
  organization: Organization;

  @Column('uuid')
  createdBy: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'createdBy' })
  creator: User;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}